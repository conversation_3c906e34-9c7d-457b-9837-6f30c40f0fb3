# 阿里云号码认证一键登录集成说明

## 概述

本项目已集成阿里云号码认证SDK（ATAuthSDK），支持运营商一键登录功能。集成后用户可以通过运营商网络直接验证手机号码，实现免密快速登录。

## 已完成的集成工作

### 1. SDK文件集成
- ✅ **ATAuthSDK.framework** - 主要认证SDK
- ✅ **YTXOperators.framework** - 运营商服务框架
- ✅ **YTXMonitor.framework** - 监控统计框架

### 2. 代码实现
- ✅ **BROneClickLoginViewController** - 一键登录主界面
- ✅ **UserManager扩展** - 增加token登录方法
- ✅ **LoginViewController更新** - 增加一键登录入口
- ✅ **BRAuthConfig** - 配置管理类

### 3. UI界面
- ✅ 完整的一键登录UI界面
- ✅ 自定义授权页面样式
- ✅ 隐私协议集成
- ✅ 错误处理和用户提示

## 必需的配置步骤

### 1. 获取阿里云App Key

1. 登录[阿里云控制台](https://ecs.console.aliyun.com/)
2. 进入"产品与服务" -> "移动研发平台" -> "号码认证服务"
3. 创建应用并获取App Key
4. 记录下App Key，后续配置中会用到

### 2. 配置App Key

在 `BRZY/Classes/Helpers/BRAuthConfig.m` 文件中配置App Key：

```objc
+ (NSString *)getATAuthSDKAppKey {
    // 替换为从阿里云控制台获取的真实App Key
    return @"您的真实App Key";
}
```

### 3. Xcode项目配置

#### 3.1 添加Framework到项目

确保以下Framework已添加到项目中：

```
ATAuthSDK.framework
YTXOperators.framework  
YTXMonitor.framework
```

#### 3.2 Build Settings配置

在项目的Build Settings中确保：

- **Other Linker Flags**: 添加 `-ObjC`
- **Framework Search Paths**: 包含SDK路径
- **Always Embed Swift Standard Libraries**: YES

#### 3.3 Info.plist配置

添加必要的权限和配置：

```xml
<!-- 网络权限 -->
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>

<!-- 运营商查询权限 -->
<key>NSCellularUsagePolicy</key>
<string>data</string>
```

### 4. 后端服务器配置

#### 4.1 Token验证接口

后端需要实现token验证接口，接收客户端传来的token进行验证：

```
POST /api/auth/verify-token
Content-Type: application/json

{
    "token": "从ATAuthSDK获取的token",
    "device": "设备信息"
}
```

#### 4.2 集成阿里云服务端SDK

后端需要集成阿里云号码认证服务端SDK来验证token的有效性。

## 使用流程

### 1. 用户流程
1. 用户打开登录页面
2. 点击"本机号码一键登录"按钮
3. 进入一键登录页面
4. 点击"本机号码一键登录"触发授权页面
5. 用户同意隐私协议并确认登录
6. SDK获取token并发送给服务器验证
7. 验证成功后完成登录流程

### 2. 技术流程
1. **环境检测**: 检查当前设备和网络是否支持一键登录
2. **预取号**: 预先获取号码信息，提升用户体验
3. **授权页面**: 展示自定义的授权确认页面
4. **获取Token**: 用户确认后获取认证token
5. **服务器验证**: 将token发送给后端进行验证
6. **登录完成**: 验证成功后完成登录流程

## 文件说明

### 核心文件

| 文件 | 说明 |
|------|------|
| `BROneClickLoginViewController.h/m` | 一键登录主界面控制器 |
| `BRAuthConfig.h/m` | SDK配置管理类 |
| `UserManager.h/m` | 用户管理类，增加了token登录方法 |
| `LoginViewController.m` | 登录页面，增加一键登录入口 |

### Framework文件

| Framework | 说明 |
|-----------|------|
| `ATAuthSDK.framework` | 阿里云号码认证主SDK |
| `YTXOperators.framework` | 运营商相关服务 |
| `YTXMonitor.framework` | 监控和统计功能 |

## 测试说明

### 1. 开发环境测试

在开发环境中，可以使用SDK提供的debug模式进行界面测试：

```objc
[[TXCommonHandler sharedInstance] debugLoginUIWithController:self 
                                                       model:customModel 
                                                    complete:^(NSDictionary *resultDic) {
    // 处理调试结果
}];
```

### 2. 真机测试

一键登录功能需要在真机上测试，且需要：
- 真实的手机号码SIM卡
- 移动网络环境（非WiFi）
- 支持的运营商（中移动、联通、电信）

### 3. 生产环境测试

确保在生产环境中：
- App Key配置正确
- 后端验证接口正常工作
- 证书和Bundle ID匹配

## 常见问题

### 1. SDK初始化失败
- 检查App Key是否正确配置
- 确认网络连接正常
- 检查SDK框架是否正确导入

### 2. 环境不支持一键登录
- 确认使用移动网络而非WiFi
- 检查SIM卡是否为支持的运营商
- 确认设备系统版本兼容

### 3. Token验证失败
- 检查后端验证接口是否正常
- 确认token是否在有效期内
- 检查网络请求是否成功

## 支持的运营商

- **中国移动** (China Mobile)
- **中国联通** (China Unicom) 
- **中国电信** (China Telecom)

## 技术支持

如有技术问题，请参考：
- [阿里云号码认证官方文档](https://help.aliyun.com/zh/pnvs/)
- [iOS客户端接入指南](https://help.aliyun.com/zh/pnvs/developer-reference/the-ios-client-access)

## 注意事项

1. **隐私合规**: 确保隐私政策中包含号码认证相关说明
2. **用户体验**: 提供备用登录方式，避免强制使用一键登录
3. **错误处理**: 妥善处理各种异常情况，提供友好的用户提示
4. **安全性**: 保护App Key和token的安全，避免泄露
5. **测试**: 在各种网络环境和设备上充分测试功能