# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

必然中医 (BRZY) is an iOS application for Traditional Chinese Medicine (TCM) healthcare services, built with Objective-C. The app handles doctor-patient communication, prescription management, and medical consultations.

## Key Architecture Components

### Module System
The app uses a modular architecture with `BRModuleManager` that loads modules from `ModulesRegister.plist`:
- **BRAPNsModule**: Handles Apple Push Notifications (JPush integration)
- **BRAppStartModule**: Manages app initialization and startup flow
- **BRThirdPartModule**: Manages third-party SDK integrations (WeChat, etc.)

### Core Systems

1. **IM (Instant Messaging) System**
   - Located in `BRZY/Classes/General/`
   - Key classes: `IMClient`, `IMSessionManager`, `IMContactManager`, `IMDataBaseManager`
   - Uses WCDB for local storage and CocoaAsyncSocket for network communication
   - Custom chat UI implementation with collection view-based message display

2. **Prescription System**
   - Main views in `BRZY/Classes/Sections/` (BRPrescription*, BRPres*)
   - Handles TCM prescription creation, editing, and management
   - Includes drug selection, contraindication checking, and patient information

3. **User & Authentication**
   - `UserManager` singleton handles user state and authentication
   - Login flow managed through `Utils` helper methods
   - WeChat SDK integration for social login

## Development Commands

### Build & Run
```bash
# Install dependencies (must have CocoaPods installed)
pod install

# Open workspace (not .xcodeproj)
open BRZY.xcworkspace

# Build via Xcode:
# - Select BRZY scheme
# - Choose target device/simulator
# - Press Cmd+R to build and run
```

### Environment Configuration
The app uses different server environments configured in `BRDefines.h`:
- Production: `im.haoniuzhongyi.top` (Socket), `api.haoniuzhongyi.top:9090` (API)
- SSL Socket Port: 5333

### Key File Locations
- **API Configuration**: `BRZY/Classes/Macro/BRDefines.h`
- **App Constants**: `BRZY/Classes/Macro/AppMacro.h`
- **Network Layer**: `BRZY/Classes/General/HTTPRequest.h/m`
- **Database Models**: Files with `+WCTTableCoding.h` suffix
- **Main Entry**: `BRZY/AppDelegate.m`

## Important Technical Details

### Dependencies (via CocoaPods)
- **Networking**: AFNetworking 3.0.4, CocoaAsyncSocket 7.6.2
- **UI**: Masonry (AutoLayout), MJRefresh, IQKeyboardManager, LSTPopView
- **Data**: WCDB 2.1.10 (SQLite ORM), MJExtension (JSON mapping)
- **Media**: SDWebImage 3.8.2, MWPhotoBrowser
- **Social**: WechatOpenSDK-XCFramework
- **Push**: JPush 3.2.4-noidfa

### Database Structure
Uses WCDB with tables for:
- `IMTableMessage`: Chat messages
- `IMTableSession`: Chat sessions
- `IMTableContact`: User contacts
- `BRTablePharmacopeia`: Drug database
- `BRTemporaryPrescription`: Draft prescriptions

### Notification System
The app uses NSNotificationCenter extensively. Key notifications defined in `BRDefines.h`:
- `kIMNotificationUpdateSessionListFromDataBase`: Update chat session list
- `kNotificationPatientCountChange`: Patient list updates
- `kNotificationPrescriptionChangePatient`: Patient selection in prescription

### UI Architecture
- Uses `BaseViewController`, `BaseNavigationController`, `BaseTabBarController` as base classes
- Custom chat UI built on UICollectionView
- Prescription views use complex custom layouts with multiple input components

## Development Tips

1. **Module Loading**: New modules must be registered in `ModulesRegister.plist`
2. **Network Requests**: Use `HTTPRequest` class for all API calls
3. **Database Operations**: Use WCDB macros for model mapping
4. **Push Notifications**: JPush is configured without IDFA for App Store compliance
5. **Image Loading**: Use SDWebImage for async image loading
6. **Socket Communication**: SocketManager handles real-time messaging

## Common Tasks

### Adding a New API Endpoint
1. Define the endpoint URL in `BRDefines.h` or use dynamic configuration
2. Create request method in appropriate manager class
3. Use `HTTPRequest` to make the call
4. Handle response with MJExtension for JSON mapping

### Creating a New Database Table
1. Create model class inheriting from NSObject
2. Add WCDB binding header (ModelName+WCTTableCoding.h)
3. Implement WCDB macros in .mm file
4. Add table creation in `IMDataBaseManager`

### Adding a New Chat Message Type
1. Create message cell class inheriting from `BRBaseMessageCell`
2. Create corresponding layout class inheriting from `BRBaseMessageCellLayout`
3. Register cell type in chat view controller
4. Handle message type in `BRMessage` model
=======
always response in 中文。

## 项目概述

必然中医 (BRZY) 是一个中医远程医疗 iOS 应用，使用 Objective-C 开发。主要功能包括医患聊天、处方管理、支付系统等。

对应的还有相同功能的安卓项目，安卓项目在目录 brzy_android ，你有权限使用 filesystem 的 mcp server 进行查看。 iOS 和安卓端的功能和 UI 界面是高度一致的，如果没有特殊情况和说明，两种的功能和 UI 等应该是尽可能一致的。 

## 开发环境设置

### 依赖管理
```bash
# 安装 CocoaPods 依赖
pod install

# 必须使用 workspace 打开项目
open BRZY.xcworkspace
```

### 构建命令
```bash
# 清理构建
xcodebuild clean -workspace BRZY.xcworkspace -scheme BRZY

# 模拟器构建
xcodebuild build -workspace BRZY.xcworkspace -scheme BRZY -destination 'platform=iOS Simulator,name=iPhone 14'

# 真机构建
xcodebuild build -workspace BRZY.xcworkspace -scheme BRZY -destination 'generic/platform=iOS'
```

## 项目架构

### 目录结构
```
BRZY/
├── AppDelegate.m           # 应用入口，模块管理器初始化
├── Classes/
│   ├── General/           # 基础类、分类、工具类
│   ├── Helpers/           # 网络请求、文件管理等辅助类
│   ├── Macro/             # 常量定义、宏定义
│   └── Sections/          # 功能模块（各个界面的 ViewController）
└── Assets.xcassets/       # 图片资源
```

### 核心架构组件

- **模块管理**: `BRModuleManager` - 基于 plist 配置的模块化启动系统
- **用户管理**: `UserManager` - 单例模式的用户状态管理
- **即时通讯**: `IMClient` - 自定义 Socket 通信实现的聊天系统
- **数据存储**: WCDB - 本地数据库，存储聊天记录等数据
- **网络请求**: AFNetworking 3.0.4 + 自定义封装

### 关键技术点

1. **Socket 通信**: 使用 CocoaAsyncSocket 实现 TCP 长连接，支持 SSL
2. **推送服务**: 集成极光推送（JPush）无 IDFA 版本
3. **支付集成**: 微信支付、银行卡、提现功能
4. **OCR 识别**: 腾讯云 OCR 用于处方扫描
5. **微信集成**: 登录、分享功能（AppID: wx266e4342efb1a84b）

## 开发注意事项

### API 环境
- 生产环境: `https://api.haoniuzhongyi.top:9090/easydoctorv2-ws/apiController`
- Socket 服务器: `im.haoniuzhongyi.top:5333` (SSL)

### 代码规范
- 使用 Objective-C，遵循苹果命名规范
- 类名前缀使用 `BR`
- 使用 Masonry 进行自动布局
- 网络请求统一使用项目封装的网络层

### 功能模块
主要功能模块位于 `Classes/Sections/`:
- `我的钱包` - 支付、提现相关
- `CommonlyPrescription` - 常用处方
- `PatientDocument` - 患者档案
- `BRIntelligent` - 智能功能
- `VisitsArrangement` - 出诊安排

### 依赖库版本
关键依赖库版本已在 Podfile 中锁定，修改版本需谨慎测试：
- iOS 最低支持版本: 13.0
- 使用 `use_frameworks!` 动态库方式
- 特殊配置: WCDB 需要 C++14 标准

### 调试技巧
1. IM 连接问题查看 `IMClient` 日志
2. 网络请求失败检查 `Helpers` 中的网络封装类
3. 推送问题查看 JPush 相关配置
4. 支付问题检查微信 SDK 配置和 Universal Links


## Project Overview

BRZY (必然中医) is a Traditional Chinese Medicine (TCM) telemedicine iOS application built with Objective-C, targeting iOS 13.0+. The app enables doctors to conduct remote consultations, prescribe traditional Chinese medicine, and manage patient relationships.

## Development Commands

### Building and Dependencies
```bash
# Install CocoaPods dependencies
pod install

# Open workspace (not project)
open BRZY.xcworkspace

# Build the project
xcodebuild -workspace BRZY.xcworkspace -scheme BRZY -configuration Debug build

# Clean build
xcodebuild -workspace BRZY.xcworkspace -scheme BRZY clean
```

### Database Management
The project uses WCDB (WeChat Database) for local data persistence. Database models are located in `Classes/General/` with `+WCTTableCoding.h` files.

## Architecture Overview

### Core Structure
- **MVC Pattern**: Classic Model-View-Controller with clear separation
- **Module System**: Managed by `BRModuleManager` with three main modules:
  - `BRAPNsModule`: Push notification handling
  - `BRAppStartModule`: Application startup logic
  - `BRThirdPartModule`: Third-party SDK integration

### Directory Organization

#### `Classes/General/`
Core framework components including:
- Base view controllers (`BaseViewController`, `BaseNavigationController`, `BaseTabBarController`)
- Complete IM system (`IMClient`, `IMSessionManager`, `IMContactManager`)
- WCDB database models and managers
- UI extensions and utilities

#### `Classes/Helpers/`
Utility and configuration layer:
- Configuration management (`Config`, `EnvironmentConfig`)
- Network and socket management (`SocketManager`)
- Authentication and user management
- Common utilities and view tools

#### `Classes/Sections/`
Business logic modules organized by feature area:
- Patient management and medical records
- TCM prescription system with herb database
- Chat and messaging components
- User profile and settings
- Financial transactions and billing
- Appointment scheduling

### Key Data Models

#### IM/Chat System
- `IMTableMessage`: Chat message storage
- `IMTableContact`: Contact/roster management  
- `IMTableSession`: Chat session data
- `BRMessage*`: Various message type models

#### TCM-Specific Models
- `BRTablePharmacopeia`: Traditional Chinese medicine database
- `BRPrescriptionModel`: TCM prescription data
- `BRMedicineModel`: Individual medicine/herb information
- `BRPatientModel`: Patient information and medical history

### Message System Architecture

The app implements a comprehensive messaging system supporting:
- Text, image, audio, and video messages
- TCM-specific message types (prescriptions, consultations)
- Custom collection view layouts for chat UI
- Real-time socket communication via `CocoaAsyncSocket`

### Environment Configuration

The project supports multiple environments managed through:
- `EnvironmentConfig.h/m`: Environment-specific settings
- `Config.h/m`: Runtime configuration management
- Compile-time flags for development/staging/production builds

### Third-Party Integration

Key dependencies include:
- **WCDB**: High-performance local database
- **AFNetworking**: HTTP networking layer
- **CocoaAsyncSocket**: Real-time communication
- **WechatOpenSDK**: WeChat payments and sharing
- **JPush**: Push notification services

## Development Guidelines

### Code Organization
- Follow the established MVC pattern
- Place reusable UI components in `Classes/General/`
- Business logic belongs in appropriate `Classes/Sections/` subdirectories
- Use the existing base classes for consistency

### Database Operations
- All database models inherit from WCDB table coding protocols
- Use `IMDataBaseManager` for IM-related database operations
- Implement proper error handling for database operations

### IM Message Handling
- Extend existing message cell types for new message formats
- Follow the established cell layout pattern for consistent UI
- Use `IMSessionManager` for session lifecycle management

### TCM-Specific Features
When working with Traditional Chinese Medicine functionality:
- Consult `BRTablePharmacopeia` for herb/medicine data
- Use established prescription models for data consistency
- Implement proper dosage calculation and contraindication checking

## 添加文件说明
如果要新增文件，注意新增后要同步修改 BRZY.xcodeproj/project.pbxproj 文件，让新增文件可以正常显示到xcode中，删除也是一样。
