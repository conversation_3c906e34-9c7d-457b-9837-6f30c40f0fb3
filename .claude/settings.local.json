{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(xcodebuild build:*)", "Bash(find:*)", "Bash(xcodebuild:*)", "mcp__filesystem__list_directory", "mcp__filesystem__search_files", "mcp__filesystem__list_allowed_directories", "mcp__filesystem__read_multiple_files", "Bash(grep:*)", "mcp__ide__getDiagnostics", "mcp__search1api__search", "mcp__desktop-commander__search_files", "mcp__desktop-commander__read_file", "mcp__desktop-commander__search_code"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["filesystem"]}