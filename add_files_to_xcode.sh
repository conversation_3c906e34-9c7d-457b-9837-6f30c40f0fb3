#!/bin/bash

# 自动将新文件添加到Xcode项目的脚本
# 使用方法：chmod +x add_files_to_xcode.sh && ./add_files_to_xcode.sh

echo "🚀 开始将新文件添加到Xcode项目..."

PROJECT_DIR="/Volumes/MacExtra/必然中医/brzy_ios"
PROJECT_FILE="$PROJECT_DIR/BRZY.xcodeproj/project.pbxproj"

# 检查项目文件是否存在
if [ ! -f "$PROJECT_FILE" ]; then
    echo "❌ 错误：找不到Xcode项目文件"
    exit 1
fi

# 要添加的文件列表
FILES_TO_ADD=(
    "BRZY/Classes/Sections/BROneClickLoginViewController.h"
    "BRZY/Classes/Sections/BROneClickLoginViewController.m"
    "BRZY/Classes/Helpers/BRAuthConfig.h" 
    "BRZY/Classes/Helpers/BRAuthConfig.m"
)

echo "📋 需要添加的文件："
for file in "${FILES_TO_ADD[@]}"; do
    echo "  - $file"
done

echo ""
echo "📝 接下来需要手动完成以下步骤："
echo ""
echo "1. 打开Xcode项目："
echo "   cd '$PROJECT_DIR'"
echo "   open BRZY.xcworkspace"
echo ""
echo "2. 在Xcode中添加文件："
echo "   a) 右键点击 'BRZY/Classes/Sections' 文件夹"
echo "   b) 选择 'Add Files to \"BRZY\"'"
echo "   c) 选择以下文件："
echo "      - BROneClickLoginViewController.h"
echo "      - BROneClickLoginViewController.m"
echo ""
echo "   d) 右键点击 'BRZY/Classes/Helpers' 文件夹"
echo "   e) 选择 'Add Files to \"BRZY\"'"
echo "   f) 选择以下文件："
echo "      - BRAuthConfig.h"
echo "      - BRAuthConfig.m"
echo ""
echo "3. 确认Framework集成："
echo "   a) 选择项目根节点 'BRZY'"
echo "   b) 点击 'BRZY' target"
echo "   c) 转到 'General' 标签页"
echo "   d) 检查 'Frameworks, Libraries, and Embedded Content'"
echo "   e) 如果没有看到ATAuthSDK相关框架，点击 '+' 添加："
echo "      - ATAuthSDK.framework"
echo "      - YTXOperators.framework"  
echo "      - YTXMonitor.framework"
echo ""
echo "4. 编译测试："
echo "   a) 按 Cmd+B 编译项目"
echo "   b) 检查是否有编译错误"
echo ""
echo "5. 配置App Key："
echo "   打开 BRAuthConfig.m 文件，替换 'YOUR_APP_KEY_HERE' 为真实的阿里云App Key"
echo ""

# 检查文件是否存在
echo "✅ 检查文件状态："
for file in "${FILES_TO_ADD[@]}"; do
    full_path="$PROJECT_DIR/$file"
    if [ -f "$full_path" ]; then
        echo "  ✓ $file (存在)"
    else
        echo "  ❌ $file (不存在)"
    fi
done

echo ""
echo "📚 参考文档："
echo "  - 阿里云号码认证集成说明.md"
echo "  - iOS客户端接入文档: https://help.aliyun.com/zh/pnvs/developer-reference/the-ios-client-access"
echo ""
echo "🎯 完成后即可在设备上测试一键登录功能！"