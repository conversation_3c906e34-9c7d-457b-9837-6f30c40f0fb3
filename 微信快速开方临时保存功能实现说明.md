# 微信快速开方临时保存功能实现说明

## 功能概述

为微信快速开方添加了基于患者姓名的临时保存功能，实现了与正常开方相同的保存和恢复机制。

## 主要实现内容

### 1. 新增检查快速开方临时处方方法

在 `PrescriptionViewController.m` 中添加了 `checkQuickPrescriptionWithPatientName:` 方法：

- **功能**：根据患者姓名检查是否存在未完成的处方
- **主键规则**：`quick_{医生ID}_{患者姓名}`
- **触发时机**：患者姓名输入时实时检测
- **弹窗提示**："{患者姓名}上次用药方案未完成，是否继续？"
- **选项**：
  - "继续编辑"：恢复之前的处方内容
  - "重新用药"：删除临时处方，重新开始

### 2. 修改临时处方保存逻辑

#### 修改 `saveTemporaryPrescription` 方法：

- **患者信息检查**：快速开方检查患者姓名，正常开方检查患者模型
- **患者信息构建**：根据开方类型使用不同的患者信息源
  - 快速开方：使用输入的 `patientName`、`patientAge`、`patientSex`、`patient_is_pregnanet`
  - 正常开方：使用 `_patientModel` 的属性
- **主键设置**：
  - 快速开方：`quick_{医生ID}_{患者姓名}`
  - 正常开方：`{医生ID}{患者ID}`

### 3. 修改处方恢复逻辑

#### 修改 `restoredToTemporaryPrescriptionWithTemPresModel:` 方法：

- **快速开方恢复**：将处方数据恢复到患者信息输入界面
- **正常开方恢复**：保持原有的患者模型恢复逻辑

### 4. 修改患者姓名输入回调

在患者姓名输入变化时：
- 保存患者姓名到 `self.patientName`
- 实时调用 `checkQuickPrescriptionWithPatientName:` 检查临时处方

### 5. 修改处方变化检测

#### 修改 `prescriptionIfChanged` 方法：

为快速开方添加患者信息变化检测：
- 患者姓名不为空
- 年龄不为默认值
- 性别不为默认值（男性）
- 怀孕状态不为默认值（未怀孕）

## 技术细节

### 数据库存储

- **表**：`BRTemporaryPrescription`
- **主键**：快速开方使用 `quick_{医生ID}_{患者姓名}` 格式
- **患者标识**：快速开方使用患者姓名作为 `patientId`

### 弹窗样式

使用项目现有的 `BRAlertView` 组件：
- 标题：动态显示患者姓名
- 按钮：
  - 取消按钮：显示"继续编辑"
  - 确认按钮：显示"重新用药"
- 背景点击隐藏：`isHideWhenTapBackground = YES`

### 保存时机

- **应用退出时**：通过 `UIApplicationWillResignActiveNotification` 自动保存
- **手动触发**：与正常开方保持一致的保存机制

## 防串联机制

- **主键包含医生ID**：确保不同医生的处方不会混淆
- **患者姓名作为标识**：同一医生下，不同患者姓名的处方独立存储
- **类型前缀区分**：快速开方使用 `quick_` 前缀，与正常开方区分

## 兼容性

- **向后兼容**：不影响现有正常开方的临时保存功能
- **代码复用**：最大程度复用现有的保存、加载、删除逻辑
- **界面一致**：使用相同的弹窗样式和交互逻辑

## 使用流程

1. 用户在微信快速开方界面输入患者姓名
2. 系统实时检测是否存在该患者的未完成处方
3. 如果存在，弹窗询问用户是否继续编辑
4. 用户选择后，系统相应地恢复处方或重新开始
5. 处方编辑过程中自动保存临时数据
6. 应用退出时确保数据持久化

## 注意事项

- 患者姓名作为唯一标识，需要用户输入准确的姓名
- 临时处方的清除机制与正常开方保持一致
- 支持所有剂型的处方保存和恢复
