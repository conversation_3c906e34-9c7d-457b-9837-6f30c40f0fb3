// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		0A097E431FCFDF4F00A1BFEF /* BRPatientModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A097E421FCFDF4F00A1BFEF /* BRPatientModel.m */; };
		0A2999221FCBB38B003C5CAA /* BRSearchModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A2999211FCBB38B003C5CAA /* BRSearchModel.m */; };
		0A3124592004974700D56D36 /* BRAPNsModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A3124582004974700D56D36 /* BRAPNsModule.m */; };
		0A31245B200499B100D56D36 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0A31245A2004999E00D56D36 /* CFNetwork.framework */; };
		0A31245D200499D000D56D36 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0A31245C200499BE00D56D36 /* CoreFoundation.framework */; };
		0A31245F20049A0E00D56D36 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0A31245E200499DE00D56D36 /* CoreTelephony.framework */; };
		0A31246120049A1D00D56D36 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0A31246020049A1700D56D36 /* SystemConfiguration.framework */; };
		0A31246720049AD400D56D36 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 0A31246620049AD400D56D36 /* libz.tbd */; };
		0A31246A20049AF300D56D36 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 0A31246920049AF200D56D36 /* libresolv.tbd */; };
		0A42DD161FD7DEFA0060D4F3 /* BRPresSelectTypeDetailCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A42DD151FD7DEFA0060D4F3 /* BRPresSelectTypeDetailCell.m */; };
		0A5F4D511FBC3B5F00491FE7 /* PrescriptionViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A5F4D501FBC3B5F00491FE7 /* PrescriptionViewController.m */; };
		0A5F4D541FBC486800491FE7 /* BRPrescriptionPatientView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A5F4D531FBC486800491FE7 /* BRPrescriptionPatientView.m */; };
		0A5F4D571FBEF18600491FE7 /* BRPresUsageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A5F4D561FBEF18500491FE7 /* BRPresUsageView.m */; };
		0A5F4D5A1FC0313800491FE7 /* BRPresOtherView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A5F4D591FC0313800491FE7 /* BRPresOtherView.m */; };
		0A69BE181FD246D5009F8630 /* BRPresInfoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A69BE171FD246D5009F8630 /* BRPresInfoView.m */; };
		0A69BE1B1FD29E48009F8630 /* BRPresSelectTypeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A69BE1A1FD29E48009F8630 /* BRPresSelectTypeView.m */; };
		0A69BE1F1FD2ACD8009F8630 /* BRPresSelectTypeMasterCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A69BE1E1FD2ACD8009F8630 /* BRPresSelectTypeMasterCell.m */; };
		0A69E5BC1FD940BE003342A6 /* BRFactoryModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A69E5BB1FD940BE003342A6 /* BRFactoryModel.m */; };
		0A69E5BF1FD946E7003342A6 /* BRSubFactoryModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A69E5BE1FD946E7003342A6 /* BRSubFactoryModel.m */; };
		0A6F25411FB3FF8D001D4FC9 /* BRPrescriptionInputPanel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F25401FB3FF8D001D4FC9 /* BRPrescriptionInputPanel.m */; };
		0A71DB111F94668C00BF831F /* AddDrugViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A71DB101F94668C00BF831F /* AddDrugViewController.m */; };
		0A71DB151F948F1800BF831F /* BRPresInputView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A71DB141F948F1800BF831F /* BRPresInputView.m */; };
		0A71DB1E1F959E6300BF831F /* BRSubMedicineModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A71DB1D1F959E6300BF831F /* BRSubMedicineModel.m */; };
		0A71DB211F976DDF00BF831F /* BRPresNoticeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A71DB201F976DDF00BF831F /* BRPresNoticeView.m */; };
		0A73F4701FE51A9000125F2D /* BRRiskTipModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A73F46F1FE51A9000125F2D /* BRRiskTipModel.m */; };
		0A7FD4681FCE8DCC00318462 /* BRDatePicker.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A7FD4671FCE8DCC00318462 /* BRDatePicker.m */; };
		0A89E71F1FF0A24100834D48 /* FactoryInfoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A89E71E1FF0A24100834D48 /* FactoryInfoViewController.m */; };
		0A8CFB3C1FC7B704006A449E /* BRPresContraindicationView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A8CFB3B1FC7B704006A449E /* BRPresContraindicationView.m */; };
		0A9202091F8F6DC10053B7D2 /* BRUnderlineRedTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A9202081F8F6DC10053B7D2 /* BRUnderlineRedTextField.m */; };
		0A97FEDD1FE263F50043D14D /* BRPresReplaceDrugTitleCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A97FEDC1FE263F50043D14D /* BRPresReplaceDrugTitleCell.m */; };
		0A9926111FCD6800003B537E /* BRPresAddPatientView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A9926101FCD6800003B537E /* BRPresAddPatientView.m */; };
		0A9A1BFC1FE13CB600F5DA0E /* BRPresInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A9A1BFB1FE13CB600F5DA0E /* BRPresInfoModel.m */; };
		0A9CED1A1F7B3A380072CF4A /* BRPrescriptionTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A9CED191F7B3A380072CF4A /* BRPrescriptionTitleView.m */; };
		0A9CED1D1F7B7BFF0072CF4A /* BRPrescriptionDiagnosesView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A9CED1C1F7B7BFF0072CF4A /* BRPrescriptionDiagnosesView.m */; };
		0A9CED201F7CD28C0072CF4A /* BRPrescriptionDisplayView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A9CED1F1F7CD28C0072CF4A /* BRPrescriptionDisplayView.m */; };
		0A9CED231F7CE1F20072CF4A /* BRPrescriptionDrugListView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A9CED221F7CE1F20072CF4A /* BRPrescriptionDrugListView.m */; };
		0A9CED261F7CE36F0072CF4A /* BRPrescriptionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A9CED251F7CE36F0072CF4A /* BRPrescriptionModel.m */; };
		0A9CED2A1F7CE5EE0072CF4A /* BRMedicineModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A9CED291F7CE5EE0072CF4A /* BRMedicineModel.m */; };
		0A9CED2D1F7CE6C40072CF4A /* BRPrescriptionDrugCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A9CED2C1F7CE6C40072CF4A /* BRPrescriptionDrugCell.m */; };
		0AA18C821FF33A9D00CC3C61 /* ZYActivitiesView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AA18C771FF33A9D00CC3C61 /* ZYActivitiesView.m */; };
		0AA18C831FF33A9D00CC3C61 /* ZYActivitiesViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AA18C791FF33A9D00CC3C61 /* ZYActivitiesViewController.m */; };
		0AA18C841FF33A9D00CC3C61 /* ZYActivityImgView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AA18C7B1FF33A9D00CC3C61 /* ZYActivityImgView.m */; };
		0AA18C851FF33A9D00CC3C61 /* ZYActivityModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AA18C7D1FF33A9D00CC3C61 /* ZYActivityModel.m */; };
		0AA18C861FF33A9D00CC3C61 /* ZYPageControl.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AA18C7F1FF33A9D00CC3C61 /* ZYPageControl.m */; };
		0AA18C871FF33A9D00CC3C61 /* ZYScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AA18C811FF33A9D00CC3C61 /* ZYScrollView.m */; };
		0AA894DA1FDEA2B60034C3A2 /* BRPresReplaceDrugView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AA894D91FDEA2B60034C3A2 /* BRPresReplaceDrugView.m */; };
		0AA894DE1FDEABAF0034C3A2 /* BRPresReplaceDrugCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AA894DD1FDEABAF0034C3A2 /* BRPresReplaceDrugCell.m */; };
		0AADC48D1FFF9A96008F29D6 /* BRContraindicationModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AADC48C1FFF9A96008F29D6 /* BRContraindicationModel.m */; };
		0ABEE64F1FC2B551006FE61B /* BRPresNoteView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0ABEE64E1FC2B551006FE61B /* BRPresNoteView.m */; };
		0ABEE6521FC67073006FE61B /* BRPresDrugCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0ABEE6511FC67073006FE61B /* BRPresDrugCell.m */; };
		0ABEE6551FC672A7006FE61B /* BRZYTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 0ABEE6541FC672A6006FE61B /* BRZYTextField.m */; };
		0ACF4F251FC943A400A3B87F /* BRPresHaveNoticeCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0ACF4F241FC943A400A3B87F /* BRPresHaveNoticeCell.m */; };
		0ACF4F291FC974A500A3B87F /* MMNumberKeyboard.m in Sources */ = {isa = PBXBuildFile; fileRef = 0ACF4F281FC974A500A3B87F /* MMNumberKeyboard.m */; };
		0AF203231F8B699700432DD6 /* BRPrescriptionDescView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AF203221F8B699700432DD6 /* BRPrescriptionDescView.m */; };
		0AF203261F8B6E8F00432DD6 /* BRPresDescTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AF203251F8B6E8F00432DD6 /* BRPresDescTitleView.m */; };
		0AF203291F8CBB0300432DD6 /* BRUnderlineTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AF203281F8CBB0300432DD6 /* BRUnderlineTextField.m */; };
		0AF2032C1F8CBF8800432DD6 /* BRPrescriptionChargeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AF2032B1F8CBF8800432DD6 /* BRPrescriptionChargeView.m */; };
		0AF2032F1F8CD48C00432DD6 /* BRPresOfflineView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AF2032E1F8CD48C00432DD6 /* BRPresOfflineView.m */; };
		0F419020204FEC73005B81D3 /* BRGuidePageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F41901F204FEC73005B81D3 /* BRGuidePageView.m */; };
		0F43AEF120FF352500D568B5 /* PhotoPresWaitCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F43AEF020FF352500D568B5 /* PhotoPresWaitCell.m */; };
		0F57A579204D27FA002914A9 /* BRPrescriptionToolBar.m in Sources */ = {isa = PBXBuildFile; fileRef = 0F57A578204D27FA002914A9 /* BRPrescriptionToolBar.m */; };
		0F9F54A320941EA600F2741C /* BRTemporaryPrescription.mm in Sources */ = {isa = PBXBuildFile; fileRef = 0F9F54A220941EA600F2741C /* BRTemporaryPrescription.mm */; };
		0FB534AB20F488B200E084B7 /* BRCanTapImgView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0FB534AA20F488B200E084B7 /* BRCanTapImgView.m */; };
		0FB534AE20F49B2B00E084B7 /* PhotoPresViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0FB534AD20F49B2B00E084B7 /* PhotoPresViewController.m */; };
		0FB534B220F4A40100E084B7 /* PhotoPresCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0FB534B120F4A40100E084B7 /* PhotoPresCell.m */; };
		0FB534B520F4B14900E084B7 /* PhotoPresModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0FB534B420F4B14900E084B7 /* PhotoPresModel.m */; };
		0FB534B820F4B61600E084B7 /* PhotoTableView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0FB534B720F4B61600E084B7 /* PhotoTableView.m */; };
		0FB534BB20F4D22100E084B7 /* PhotoPresDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0FB534BA20F4D22100E084B7 /* PhotoPresDetailViewController.m */; };
		0FB534C120F5A2FD00E084B7 /* BRSegmentView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0FB534C020F5A2FD00E084B7 /* BRSegmentView.m */; };
		770044482C8EE94700236FF1 /* FloatingButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 770044472C8EE94700236FF1 /* FloatingButton.m */; };
		7700F3511FCBE4920059BA14 /* MedicatedInfoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7700F3501FCBE4920059BA14 /* MedicatedInfoViewController.m */; };
		7705516A1FDBBBBD000306B1 /* BRBinDingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 770551691FDBBBBD000306B1 /* BRBinDingViewController.m */; };
		7705516D1FDBC99D000306B1 /* BRBankCardListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7705516C1FDBC99D000306B1 /* BRBankCardListViewController.m */; };
		770605851FDBBD510091FA60 /* SessionXiaoRanViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 770605841FDBBD510091FA60 /* SessionXiaoRanViewController.m */; };
		770605881FDBEAC60091FA60 /* FileManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 770605871FDBEAC60091FA60 /* FileManager.m */; };
		7706FF371F6A1C2C00EBB35C /* BRChatInputMoreContainerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7706FF361F6A1C2C00EBB35C /* BRChatInputMoreContainerView.m */; };
		77089A802CF0991C00C9E6C3 /* OcrSDK.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 77089A7F2CF0991C00C9E6C3 /* OcrSDK.bundle */; };
		77089A822CF0994D00C9E6C3 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 77089A812CF0994D00C9E6C3 /* Accelerate.framework */; };
		77089A882CF0996900C9E6C3 /* CoreML.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 77089A872CF0996900C9E6C3 /* CoreML.framework */; };
		77089A912CF09B3E00C9E6C3 /* Photos.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 77089A832CF0995A00C9E6C3 /* Photos.framework */; };
		77089A922CF09B3E00C9E6C3 /* PhotosUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 77089A852CF0996200C9E6C3 /* PhotosUI.framework */; };
		77089A932CF09B4700C9E6C3 /* CoreML.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 77089A872CF0996900C9E6C3 /* CoreML.framework */; };
		770B62F01FCD565500F6380C /* ConfigInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 770B62EF1FCD565500F6380C /* ConfigInfo.m */; };
		770CBCE51FDFD02400C27AB3 /* QuestionTypeCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 770CBCE41FDFD02400C27AB3 /* QuestionTypeCell.m */; };
		770CBCE81FDFD04100C27AB3 /* QuestionListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 770CBCE71FDFD04100C27AB3 /* QuestionListCell.m */; };
		770CBCEB1FDFDC2300C27AB3 /* FrequentQuestionShowView.m in Sources */ = {isa = PBXBuildFile; fileRef = 770CBCEA1FDFDC2300C27AB3 /* FrequentQuestionShowView.m */; };
		770D3E1A200753CF00D48C5A /* BRShareView.m in Sources */ = {isa = PBXBuildFile; fileRef = 770D3E19200753CF00D48C5A /* BRShareView.m */; };
		770FE95C1FF33D1800008C78 /* ShowMyPurseSMSCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 770FE95B1FF33D1800008C78 /* ShowMyPurseSMSCode.m */; };
		77110C8E2E069F8000F002FC /* BRWithdrawPasswordView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77110C8D2E069F8000F002FC /* BRWithdrawPasswordView.m */; };
		771138DB1FC7B4920084D9CB /* BRAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 771138DA1FC7B4920084D9CB /* BRAlertView.m */; };
		7712BE121FD919E00064B0F6 /* BRWzdAnswerMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7712BE111FD919E00064B0F6 /* BRWzdAnswerMessageCell.m */; };
		7712BE151FD91A2A0064B0F6 /* BRWzdAnswerMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 7712BE141FD91A2A0064B0F6 /* BRWzdAnswerMessageCellLayout.m */; };
		7712BE181FD91FC60064B0F6 /* BRWzdQuestionMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7712BE171FD91FC60064B0F6 /* BRWzdQuestionMessageCell.m */; };
		7712BE1B1FD91FEA0064B0F6 /* BRWzdQuestionMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 7712BE1A1FD91FEA0064B0F6 /* BRWzdQuestionMessageCellLayout.m */; };
		7712BE1E1FD920130064B0F6 /* BRFzdAnswerMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7712BE1D1FD920130064B0F6 /* BRFzdAnswerMessageCell.m */; };
		7712BE211FD920330064B0F6 /* BRFzdQuestionMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7712BE201FD920330064B0F6 /* BRFzdQuestionMessageCell.m */; };
		7712BE241FD9205C0064B0F6 /* BRFzdQuestionMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 7712BE231FD9205C0064B0F6 /* BRFzdQuestionMessageCellLayout.m */; };
		7712BE271FD920720064B0F6 /* BRFzdAnswerMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 7712BE261FD920720064B0F6 /* BRFzdAnswerMessageCellLayout.m */; };
		7712BE2C1FD927B10064B0F6 /* BRSupplementQuestionMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7712BE2B1FD927B10064B0F6 /* BRSupplementQuestionMessageCell.m */; };
		7712BE2F1FD927CD0064B0F6 /* BRSupplementAnswerMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 7712BE2E1FD927CD0064B0F6 /* BRSupplementAnswerMessageCellLayout.m */; };
		7712BE321FD927ED0064B0F6 /* BRSupplementQuestionMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 7712BE311FD927ED0064B0F6 /* BRSupplementQuestionMessageCellLayout.m */; };
		7712BE351FD928080064B0F6 /* BRSupplementAnswerMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7712BE341FD928080064B0F6 /* BRSupplementAnswerMessageCell.m */; };
		77131E9E1F4D7503006C9F79 /* NSDateFormatter+Singleton.m in Sources */ = {isa = PBXBuildFile; fileRef = 77131E9D1F4D7503006C9F79 /* NSDateFormatter+Singleton.m */; };
		77131EA41F4D758D006C9F79 /* NSDate+Message.m in Sources */ = {isa = PBXBuildFile; fileRef = 77131EA31F4D758D006C9F79 /* NSDate+Message.m */; };
		77131EA71F4D76D2006C9F79 /* UINavigationBar+BackgroundColor.m in Sources */ = {isa = PBXBuildFile; fileRef = 77131EA61F4D76D2006C9F79 /* UINavigationBar+BackgroundColor.m */; };
		77131EAB1F4D7D6B006C9F79 /* Reachability.m in Sources */ = {isa = PBXBuildFile; fileRef = 77131EAA1F4D7D6B006C9F79 /* Reachability.m */; };
		771736C61FA9689500ADE932 /* BRMessageModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 771736C51FA9689500ADE932 /* BRMessageModel.m */; };
		771736C91FA968A200ADE932 /* BRMessageContentModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 771736C81FA968A200ADE932 /* BRMessageContentModel.m */; };
		771736CC1FA96A7E00ADE932 /* BRMessagesModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 771736CB1FA96A7E00ADE932 /* BRMessagesModel.m */; };
		7718689820AD326500B62997 /* BRBillDetailsAccountOfMoneyCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7718689720AD326500B62997 /* BRBillDetailsAccountOfMoneyCell.m */; };
		771911AB1FE21A6000070347 /* BRBaseTextLinePositionModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = 771911AA1FE21A6000070347 /* BRBaseTextLinePositionModifier.m */; };
		7719417E1F7B402D00E11088 /* UIFont+Util.m in Sources */ = {isa = PBXBuildFile; fileRef = 7719417D1F7B402D00E11088 /* UIFont+Util.m */; };
		77194D971FD0026800716835 /* BRDHTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 77194D961FD0026800716835 /* BRDHTextField.m */; };
		7719B97320206233005504C6 /* UIImage+BR.m in Sources */ = {isa = PBXBuildFile; fileRef = 7719B97220206233005504C6 /* UIImage+BR.m */; };
		771C15CC1F4D2B4100099626 /* UserManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 771C15CB1F4D2B4100099626 /* UserManager.m */; };
		771C15CF1F4D709200099626 /* UIView+Util.m in Sources */ = {isa = PBXBuildFile; fileRef = 771C15CE1F4D709200099626 /* UIView+Util.m */; };
		771D93AA2056292500A59DD2 /* PrescriptionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 771D93A92056292500A59DD2 /* PrescriptionModel.m */; };
		771D93AD205667E600A59DD2 /* ClassicPrescriptionCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 771D93AC205667E600A59DD2 /* ClassicPrescriptionCell.m */; };
		771E520B2134E9DE006B6ACB /* DrugStoreModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 771E520A2134E9DE006B6ACB /* DrugStoreModel.m */; };
		771E520E2134ED30006B6ACB /* DrugStoreListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 771E520D2134ED30006B6ACB /* DrugStoreListModel.m */; };
		771F66F01FC8090F002AAA5C /* BRPatientDocumentTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 771F66EE1FC8090F002AAA5C /* BRPatientDocumentTableViewCell.m */; };
		771F66F11FC8090F002AAA5C /* BRPatientDocumentTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 771F66EF1FC8090F002AAA5C /* BRPatientDocumentTableViewCell.xib */; };
		7720F1961FE8F626009DC35C /* BRSessionListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7720F1951FE8F626009DC35C /* BRSessionListModel.m */; };
		7720F1991FE8F658009DC35C /* BRSessionRosterUserModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7720F1981FE8F658009DC35C /* BRSessionRosterUserModel.m */; };
		7720F19C1FE8F9B2009DC35C /* BRSessionListItemModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7720F19B1FE8F9B2009DC35C /* BRSessionListItemModel.m */; };
		7721B3EA1FC422E7000A6DFA /* BRWzdBaseMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7721B3E91FC422E7000A6DFA /* BRWzdBaseMessageCell.m */; };
		7721B3ED1FC42310000A6DFA /* BRWzdBaseMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 7721B3EC1FC42310000A6DFA /* BRWzdBaseMessageCellLayout.m */; };
		7721B3F51FC43299000A6DFA /* BRActionSheetView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7721B3F41FC43299000A6DFA /* BRActionSheetView.m */; };
		772400441F56549C0015E0E3 /* BRModuleManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 772400431F56549C0015E0E3 /* BRModuleManager.m */; };
		7724004D1F5658B10015E0E3 /* BRThirdPartModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 7724004C1F5658B10015E0E3 /* BRThirdPartModule.m */; };
		7724004F1F5659600015E0E3 /* ModulesRegister.plist in Resources */ = {isa = PBXBuildFile; fileRef = 7724004E1F5659600015E0E3 /* ModulesRegister.plist */; };
		772400531F5665850015E0E3 /* BRAppStartModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 772400521F5665850015E0E3 /* BRAppStartModule.m */; };
		7724007F1F56D9DA0015E0E3 /* PanelIconModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7724007E1F56D9DA0015E0E3 /* PanelIconModel.m */; };
		7724EE6A1FBE8DE100309E92 /* UINavigationBar+Addition.m in Sources */ = {isa = PBXBuildFile; fileRef = 7724EE691FBE8DE100309E92 /* UINavigationBar+Addition.m */; };
		77264C961F58F647002C9CBD /* MyQRCodeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77264C951F58F647002C9CBD /* MyQRCodeViewController.m */; };
		77264C991F590452002C9CBD /* BaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77264C981F590452002C9CBD /* BaseViewController.m */; };
		77264DF4212D3BFC002EE6C9 /* DrugStoreListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77264DF3212D3BFC002EE6C9 /* DrugStoreListCell.m */; };
		77264DF7212D5444002EE6C9 /* DrugStoreListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77264DF6212D5444002EE6C9 /* DrugStoreListViewController.m */; };
		7726F6031F958FE000D2E07B /* BRImageMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7726F6021F958FE000D2E07B /* BRImageMessageCell.m */; };
		7726F6061F95902100D2E07B /* BRImageMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 7726F6051F95902100D2E07B /* BRImageMessageCellLayout.m */; };
		77273AEE2CF70E1C0018A9CD /* EnvironmentConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 77273AED2CF70E1C0018A9CD /* EnvironmentConfig.m */; };
		77286C852057755E00117038 /* ClassicPrescriptionSearchViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77286C842057755E00117038 /* ClassicPrescriptionSearchViewController.m */; };
		77286C882057CAB700117038 /* ClassicSearchModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77286C872057CAB700117038 /* ClassicSearchModel.m */; };
		77286C8B2057D77B00117038 /* ClassicPrescriptionListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77286C8A2057D77B00117038 /* ClassicPrescriptionListViewController.m */; };
		772901961F5D385A008C51F6 /* UIView+BR.m in Sources */ = {isa = PBXBuildFile; fileRef = 772901951F5D385A008C51F6 /* UIView+BR.m */; };
		772901991F5D3C96008C51F6 /* BRBadgeView.m in Sources */ = {isa = PBXBuildFile; fileRef = 772901981F5D3C96008C51F6 /* BRBadgeView.m */; };
		7729019C1F5D4F4E008C51F6 /* IMSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 7729019B1F5D4F4E008C51F6 /* IMSessionManager.m */; };
		77293F9F1FC9659F00329DC2 /* RRFPSBar.m in Sources */ = {isa = PBXBuildFile; fileRef = 77293F9E1FC9659F00329DC2 /* RRFPSBar.m */; };
		772A7D061FA1903C001AE0BB /* BRAudioMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 772A7D051FA1903C001AE0BB /* BRAudioMessageCell.m */; };
		772A7D091FA19066001AE0BB /* BRAudioMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 772A7D081FA19066001AE0BB /* BRAudioMessageCellLayout.m */; };
		772B10C71FA85F1A00D894EA /* IMUIHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 772B10C61FA85F1A00D894EA /* IMUIHelper.m */; };
		772B59BB1FE7DA84009EA260 /* BRRevokeMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 772B59BA1FE7DA84009EA260 /* BRRevokeMessageCell.m */; };
		772B59BE1FE7DACC009EA260 /* BRRevokeMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 772B59BD1FE7DACC009EA260 /* BRRevokeMessageCellLayout.m */; };
		772E57F21FF1E319008252E0 /* BRTablePharmacopeia.mm in Sources */ = {isa = PBXBuildFile; fileRef = 772E57F11FF1E319008252E0 /* BRTablePharmacopeia.mm */; };
		772F70D7205139FE004BFE4D /* BRStockInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 772F70D6205139FE004BFE4D /* BRStockInfoModel.m */; };
		7731F8862E42CA5400A7CC99 /* mapping.txt in Resources */ = {isa = PBXBuildFile; fileRef = 7731F8852E42CA5400A7CC99 /* mapping.txt */; };
		77327D731FC269B90023939F /* BRSessionStartMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 77327D721FC269B90023939F /* BRSessionStartMessageCellLayout.m */; };
		77327D761FC269F50023939F /* BRSessionStartMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77327D751FC269F50023939F /* BRSessionStartMessageCell.m */; };
		77327D791FC2757C0023939F /* BRSessionEndMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77327D781FC2757C0023939F /* BRSessionEndMessageCell.m */; };
		77327D7C1FC275A00023939F /* BRSessionEndMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 77327D7B1FC275A00023939F /* BRSessionEndMessageCellLayout.m */; };
		77327D801FC2830F0023939F /* BRCustomSystemMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77327D7F1FC2830F0023939F /* BRCustomSystemMessageCell.m */; };
		77327D831FC283380023939F /* BRCustomSystemMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 77327D821FC283380023939F /* BRCustomSystemMessageCellLayout.m */; };
		7733342F1FA2FE6F00F996A0 /* BRPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 7733342E1FA2FE6F00F996A0 /* BRPlayer.m */; };
		7733F25F1FCE51430085B43F /* BRComView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7733F25E1FCE51430085B43F /* BRComView.m */; };
		7733F2621FCE51530085B43F /* BRHistoryView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7733F2611FCE51530085B43F /* BRHistoryView.m */; };
		7733F2691FCEC9890085B43F /* BRHistoryTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7733F2671FCEC9890085B43F /* BRHistoryTableViewCell.m */; };
		7733F26A1FCEC9890085B43F /* BRHistoryTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 7733F2681FCEC9890085B43F /* BRHistoryTableViewCell.xib */; };
		7734EF0E2CDC95110037B345 /* QuickPrescribeSessionViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7734EF0D2CDC95110037B345 /* QuickPrescribeSessionViewController.m */; };
		77351B1720037DF500E266F7 /* DataInitLoadingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77351B1620037DF500E266F7 /* DataInitLoadingViewController.m */; };
		7735EF321F62A713003CE02D /* ChatViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7735EF311F62A713003CE02D /* ChatViewController.m */; };
		7735EF351F62A720003CE02D /* SessionViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7735EF341F62A720003CE02D /* SessionViewController.m */; };
		7735EF381F62A9D4003CE02D /* PatientDocumentViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7735EF371F62A9D4003CE02D /* PatientDocumentViewController.m */; };
		7735EF441F62BFEF003CE02D /* InvitePatientViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7735EF431F62BFEF003CE02D /* InvitePatientViewController.m */; };
		7735EF471F62C00C003CE02D /* InviteDoctorViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7735EF461F62C00C003CE02D /* InviteDoctorViewController.m */; };
		7736B9441FE100F700C55A50 /* QuestionAddOrEditListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7736B9431FE100F700C55A50 /* QuestionAddOrEditListCell.m */; };
		7736B9471FE1257C00C55A50 /* QuestionItemModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7736B9461FE1257C00C55A50 /* QuestionItemModel.m */; };
		7736B94A1FE1260700C55A50 /* QuestionInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7736B9491FE1260700C55A50 /* QuestionInfoModel.m */; };
		7736B94D1FE144D700C55A50 /* FrequentlyQuestionContentAddViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7736B94C1FE144D700C55A50 /* FrequentlyQuestionContentAddViewController.m */; };
		77399A0D20060B8F00CA2277 /* BRDataEmptyView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77399A0C20060B8F00CA2277 /* BRDataEmptyView.m */; };
		773D913F2CF5D63D00547A91 /* BRTencentCloudAPISignature.m in Sources */ = {isa = PBXBuildFile; fileRef = 773D913E2CF5D63D00547A91 /* BRTencentCloudAPISignature.m */; };
		773D91422CF5D8B100547A91 /* BRTencentOCRRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 773D91412CF5D8B100547A91 /* BRTencentOCRRequest.m */; };
		773D91472CF5D99200547A91 /* OCRPoint.m in Sources */ = {isa = PBXBuildFile; fileRef = 773D91462CF5D99200547A91 /* OCRPoint.m */; };
		773D914A2CF5DA1400547A91 /* OCRWordPolygon.m in Sources */ = {isa = PBXBuildFile; fileRef = 773D91492CF5DA1400547A91 /* OCRWordPolygon.m */; };
		773D914D2CF5DA3300547A91 /* OCRTextDetection.m in Sources */ = {isa = PBXBuildFile; fileRef = 773D914C2CF5DA3300547A91 /* OCRTextDetection.m */; };
		773D91502CF5DA6500547A91 /* OCRResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = 773D914F2CF5DA6500547A91 /* OCRResponse.m */; };
		773E8AF82CD9E62B0096F0A5 /* BRAdjustByMultipleContentView.m in Sources */ = {isa = PBXBuildFile; fileRef = 773E8AF72CD9E62B0096F0A5 /* BRAdjustByMultipleContentView.m */; };
		774162DA279EE2E50028FC2C /* BRWechatBindResultModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 774162D9279EE2E50028FC2C /* BRWechatBindResultModel.m */; };
		774162DD279EE7300028FC2C /* BRWithdrawInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 774162DC279EE7300028FC2C /* BRWithdrawInfoModel.m */; };
		7742ECAF1F4BD7ED00A9B110 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 7742ECAE1F4BD7ED00A9B110 /* main.m */; };
		7742ECB21F4BD7ED00A9B110 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 7742ECB11F4BD7ED00A9B110 /* AppDelegate.m */; };
		7742ECBA1F4BD7ED00A9B110 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 7742ECB91F4BD7ED00A9B110 /* Assets.xcassets */; };
		7742ECE51F4C183B00A9B110 /* BaseTabBarController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7742ECE41F4C183B00A9B110 /* BaseTabBarController.m */; };
		7742ECEF1F4C22EC00A9B110 /* Utils.m in Sources */ = {isa = PBXBuildFile; fileRef = 7742ECEE1F4C22EC00A9B110 /* Utils.m */; };
		7742ECF21F4C234F00A9B110 /* ViewTools.m in Sources */ = {isa = PBXBuildFile; fileRef = 7742ECF11F4C234F00A9B110 /* ViewTools.m */; };
		7742ED001F4C26E400A9B110 /* MessageListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7742ECFF1F4C26E400A9B110 /* MessageListViewController.m */; };
		7742ED031F4C272200A9B110 /* PatientViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7742ED021F4C272200A9B110 /* PatientViewController.m */; };
		7742ED061F4C273200A9B110 /* InviteViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7742ED051F4C273200A9B110 /* InviteViewController.m */; };
		7742ED091F4C274F00A9B110 /* UserCenterViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7742ED081F4C274F00A9B110 /* UserCenterViewController.m */; };
		7742ED0C1F4C2A1E00A9B110 /* BaseNavigationController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7742ED0B1F4C2A1E00A9B110 /* BaseNavigationController.m */; };
		7742ED101F4C346800A9B110 /* UIColor+Util.m in Sources */ = {isa = PBXBuildFile; fileRef = 7742ED0F1F4C346800A9B110 /* UIColor+Util.m */; };
		774494451FE0C2A900522A88 /* BRNoDataView.m in Sources */ = {isa = PBXBuildFile; fileRef = 774494441FE0C2A900522A88 /* BRNoDataView.m */; };
		7744A61E1FEA059100AE7ABD /* PatientInfoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7744A61D1FEA059100AE7ABD /* PatientInfoViewController.m */; };
		7744A6211FEA068700AE7ABD /* LogisticInfoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7744A6201FEA068700AE7ABD /* LogisticInfoViewController.m */; };
		7744A6241FEA07D000AE7ABD /* PatientInfoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7744A6231FEA07D000AE7ABD /* PatientInfoView.m */; };
		7744A6281FEA3C8300AE7ABD /* PatientInfoListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7744A6271FEA3C8300AE7ABD /* PatientInfoListCell.m */; };
		7745181A1FDE5C6800DAB506 /* BRXiaoRanChatInputMoreContainerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 774518191FDE5C6800DAB506 /* BRXiaoRanChatInputMoreContainerView.m */; };
		77475AE31FF26D740066F5C8 /* HistorySelectPatientView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77475AE21FF26D740066F5C8 /* HistorySelectPatientView.m */; };
		77494F2A28A71AEE0000132A /* BRQuickPrescribeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77494F2928A71AEE0000132A /* BRQuickPrescribeViewController.m */; };
		77494F3128A722FF0000132A /* BRWritePatientInfoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77494F3028A722FF0000132A /* BRWritePatientInfoView.m */; };
		7749E1A11F7DE23B00E93881 /* ButtonInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7749E1A01F7DE23B00E93881 /* ButtonInfoModel.m */; };
		774AE86D1FD7FDCE009AFEF9 /* BRBankCardViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 774AE86C1FD7FDCE009AFEF9 /* BRBankCardViewController.m */; };
		774C2DD91FEE2E03004CD7E2 /* PatientInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 774C2DD81FEE2E03004CD7E2 /* PatientInfoModel.m */; };
		774C2DDC1FEE5E87004CD7E2 /* PatientModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 774C2DDB1FEE5E87004CD7E2 /* PatientModel.m */; };
		774DC664212EB17F008F4931 /* ShareContentView.m in Sources */ = {isa = PBXBuildFile; fileRef = 774DC663212EB17F008F4931 /* ShareContentView.m */; };
		7751DD5E1F4EAF8800C10C3C /* LoginViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7751DD5D1F4EAF8800C10C3C /* LoginViewController.m */; };
		7751DD611F4EE06400C10C3C /* LoginInputView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7751DD601F4EE06400C10C3C /* LoginInputView.m */; };
		7752437F1FC977CB00EA2ED3 /* BRVisitsArrangementModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7752437E1FC977CB00EA2ED3 /* BRVisitsArrangementModel.m */; };
		775243831FC978EE00EA2ED3 /* BRDHBaseModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 775243821FC978EE00EA2ED3 /* BRDHBaseModel.m */; };
		7755B71C20A5A49F00162617 /* MyPurseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7755B71B20A5A49F00162617 /* MyPurseViewController.m */; };
		775643142DF7381900392EFE /* BRAuthConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 775643132DF7381900392EFE /* BRAuthConfig.m */; };
		7756569C1F6282EC00F779C2 /* SessionSearchViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7756569B1F6282EC00F779C2 /* SessionSearchViewController.m */; };
		7756569F1F62849000F779C2 /* PatientSearchViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7756569E1F62849000F779C2 /* PatientSearchViewController.m */; };
		775656A51F628BF800F779C2 /* SessionListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 775656A41F628BF800F779C2 /* SessionListCell.m */; };
		7757A81F1FE4BE6C00B065A5 /* SystemMessageViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7757A81E1FE4BE6C00B065A5 /* SystemMessageViewController.m */; };
		7757A8231FE4C04400B065A5 /* SystemMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7757A8221FE4C04400B065A5 /* SystemMessageCell.m */; };
		7757A8261FE4C11F00B065A5 /* SystemDateCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7757A8251FE4C11F00B065A5 /* SystemDateCell.m */; };
		7757A8291FE50FDE00B065A5 /* MessageTransmitViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7757A8281FE50FDE00B065A5 /* MessageTransmitViewController.m */; };
		7758EFCD1FE7CE5C0084D11E /* BRUpDateView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7758EFCC1FE7CE5C0084D11E /* BRUpDateView.m */; };
		775967351FFB280700ADC587 /* DoctorAuthentiationModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 775967341FFB280700ADC587 /* DoctorAuthentiationModel.m */; };
		775E751220A999D500EAA5DB /* MyPurseHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 775E751120A999D500EAA5DB /* MyPurseHeaderView.m */; };
		775E751520AA76B600EAA5DB /* BillDetailsHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 775E751420AA76B600EAA5DB /* BillDetailsHeaderView.m */; };
		775E751820AADE4B00EAA5DB /* BillDetailsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 775E751720AADE4B00EAA5DB /* BillDetailsModel.m */; };
		775E751B20AAEB0600EAA5DB /* BillDetailsBaseCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 775E751A20AAEB0600EAA5DB /* BillDetailsBaseCell.m */; };
		7761534D1FCD30D9006A2FAC /* PatientDocumentModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7761534C1FCD30D9006A2FAC /* PatientDocumentModel.m */; };
		776153541FCD74D0006A2FAC /* BRComPrescriptionViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 776153531FCD74D0006A2FAC /* BRComPrescriptionViewController.m */; };
		7761772C209958F400C35E77 /* ChatTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7761772B209958F400C35E77 /* ChatTitleView.m */; };
		7761772F20995B2300C35E77 /* AgeGenderButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 7761772E20995B2300C35E77 /* AgeGenderButton.m */; };
		776191581F5A436100A19B77 /* UIImageView+Badge.m in Sources */ = {isa = PBXBuildFile; fileRef = 776191571F5A436100A19B77 /* UIImageView+Badge.m */; };
		776191641F5A491100A19B77 /* UIButton+Badge.m in Sources */ = {isa = PBXBuildFile; fileRef = 776191631F5A491100A19B77 /* UIButton+Badge.m */; };
		7762973C1FDA69CC00E480F2 /* SessionHistoryViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7762973B1FDA69CC00E480F2 /* SessionHistoryViewController.m */; };
		7762B04320CE1A8700273CD5 /* BRMedicationWarningAlertView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7762B04220CE1A8700273CD5 /* BRMedicationWarningAlertView.m */; };
		776365FA1F4FC18A00AF92A1 /* UITextView+Placeholder.m in Sources */ = {isa = PBXBuildFile; fileRef = 776365F91F4FC18A00AF92A1 /* UITextView+Placeholder.m */; };
		776365FD1F4FC19300AF92A1 /* UIButton+HotPointButtonCategory.m in Sources */ = {isa = PBXBuildFile; fileRef = 776365FC1F4FC19300AF92A1 /* UIButton+HotPointButtonCategory.m */; };
		776366001F4FC2D800AF92A1 /* UITextField+Max.m in Sources */ = {isa = PBXBuildFile; fileRef = 776365FF1F4FC2D800AF92A1 /* UITextField+Max.m */; };
		776366031F4FD00100AF92A1 /* UserInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 776366021F4FD00100AF92A1 /* UserInfo.m */; };
		7764AB0E1F60081800B26332 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 7764AB101F60081800B26332 /* Localizable.strings */; };
		776633931F5E97F500CF7029 /* IMTableMessage.mm in Sources */ = {isa = PBXBuildFile; fileRef = 776633921F5E97F500CF7029 /* IMTableMessage.mm */; };
		776633961F5E980200CF7029 /* IMTableSession.mm in Sources */ = {isa = PBXBuildFile; fileRef = 776633951F5E980200CF7029 /* IMTableSession.mm */; };
		776633991F5E981B00CF7029 /* IMTableContact.mm in Sources */ = {isa = PBXBuildFile; fileRef = 776633981F5E981B00CF7029 /* IMTableContact.mm */; };
		776A6769200EE4F100412402 /* BRGuideView.m in Sources */ = {isa = PBXBuildFile; fileRef = 776A6768200EE4F100412402 /* BRGuideView.m */; };
		776BDF0A1FE522F600CBD754 /* BRMedicationWarning.m in Sources */ = {isa = PBXBuildFile; fileRef = 776BDF091FE522F600CBD754 /* BRMedicationWarning.m */; };
		776F86021FF496D600CAAE33 /* WzdWebViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 776F86011FF496D600CAAE33 /* WzdWebViewController.m */; };
		776FF32A212C2CB300B9F71A /* DrugStoreHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 776FF329212C2CB300B9F71A /* DrugStoreHeaderView.m */; };
		776FF32D212C348E00B9F71A /* DrugHeaderButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 776FF32C212C348E00B9F71A /* DrugHeaderButton.m */; };
		7770FD591F53FCA000651814 /* IMClient.m in Sources */ = {isa = PBXBuildFile; fileRef = 7770FD581F53FCA000651814 /* IMClient.m */; };
		7770FD601F53FEA100651814 /* IMMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 7770FD5F1F53FEA100651814 /* IMMessage.m */; };
		7770FD691F54089A00651814 /* IMContact.m in Sources */ = {isa = PBXBuildFile; fileRef = 7770FD681F54089A00651814 /* IMContact.m */; };
		7770FD6C1F5408AD00651814 /* IMContactManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 7770FD6B1F5408AD00651814 /* IMContactManager.m */; };
		7770FD701F540D7D00651814 /* IMSDKHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 7770FD6F1F540D7D00651814 /* IMSDKHelper.m */; };
		77723E071FD635BE00E1BD5A /* AddCommonlyPrescriptionViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77723E051FD635BE00E1BD5A /* AddCommonlyPrescriptionViewController.m */; };
		777418982CEF208B00392B18 /* BRIntelligentEntryViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 777418972CEF208B00392B18 /* BRIntelligentEntryViewController.m */; };
		7774866027B79B1100B49BB4 /* PatientComplainViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7774865F27B79B1100B49BB4 /* PatientComplainViewController.m */; };
		7774866327B7F39D00B49BB4 /* AccountTerminateViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7774866227B7F39D00B49BB4 /* AccountTerminateViewController.m */; };
		7775A1DA1FFF4C77009D2131 /* BRDebugInfoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7775A1D91FFF4C77009D2131 /* BRDebugInfoViewController.m */; };
		777727D2276DA66000DA7FD4 /* EbookListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 777727D1276DA66000DA7FD4 /* EbookListViewController.m */; };
		777727D5276DA73400DA7FD4 /* EbookMallListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 777727D4276DA73400DA7FD4 /* EbookMallListViewController.m */; };
		777727DB276DDF7200DA7FD4 /* EbookListItemCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 777727DA276DDF7200DA7FD4 /* EbookListItemCell.m */; };
		777727DE276DE62C00DA7FD4 /* EbookMallListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 777727DD276DE62C00DA7FD4 /* EbookMallListCell.m */; };
		777742311FD56A710060BEF2 /* PatientsListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 777742301FD56A710060BEF2 /* PatientsListCell.m */; };
		7777423A1FD587150060BEF2 /* FrequentlyQuestionAddViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 777742391FD587150060BEF2 /* FrequentlyQuestionAddViewController.m */; };
		777A239A2CF80E2300C0853F /* BRHerbInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 777A23992CF80E2300C0853F /* BRHerbInfo.m */; };
		777B879A2CFC15B900004875 /* CustomBoilyWayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 777B87992CFC15B900004875 /* CustomBoilyWayViewController.m */; };
		777B879D2CFC1A7C00004875 /* CustomBoilyWayCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 777B879C2CFC1A7C00004875 /* CustomBoilyWayCell.m */; };
		777DC1F7273D687D00A594D7 /* JTPrivacyWebViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 777DC1F6273D687D00A594D7 /* JTPrivacyWebViewController.m */; };
		777E588220526B1C000336AC /* ClassicPrescriptionViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 777E588120526B1C000336AC /* ClassicPrescriptionViewController.m */; };
		778139231FF2521700852B27 /* PatientsDocumentModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 778139221FF2521700852B27 /* PatientsDocumentModel.m */; };
		7781E2901F4D19CE00DDD047 /* Config.m in Sources */ = {isa = PBXBuildFile; fileRef = 7781E28F1F4D19CE00DDD047 /* Config.m */; };
		7781E2931F4D1AB900DDD047 /* SocketManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 7781E2921F4D1AB900DDD047 /* SocketManager.m */; };
		7781E2A21F4D1D4700DDD047 /* IMDataBaseManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 7781E2A11F4D1D4700DDD047 /* IMDataBaseManager.mm */; };
		7785B2912E224E9F006F68FC /* BRPackageSpecModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7785B2902E224E9F006F68FC /* BRPackageSpecModel.m */; };
		7786ECE91FF0AACD008BEEE7 /* PatientSelectedInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7786ECE81FF0AACD008BEEE7 /* PatientSelectedInfoModel.m */; };
		778E0B65279BCBD700F9BA6B /* BRWechatPocketMoneyViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 778E0B64279BCBD700F9BA6B /* BRWechatPocketMoneyViewController.m */; };
		778E0B68279BD0BD00F9BA6B /* WechatWithdrawChooseCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 778E0B67279BD0BD00F9BA6B /* WechatWithdrawChooseCell.m */; };
		778E0B6B279BD15A00F9BA6B /* WechatWithDrawSumCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 778E0B6A279BD15A00F9BA6B /* WechatWithDrawSumCell.m */; };
		778E0B6E279BD9BE00F9BA6B /* WechatWithdrawBottomView.m in Sources */ = {isa = PBXBuildFile; fileRef = 778E0B6D279BD9BE00F9BA6B /* WechatWithdrawBottomView.m */; };
		778E0B71279BE0D900F9BA6B /* WechatWithdrawInputNumCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 778E0B70279BE0D900F9BA6B /* WechatWithdrawInputNumCell.m */; };
		778E0B75279D341100F9BA6B /* BRPrivacyPopView.m in Sources */ = {isa = PBXBuildFile; fileRef = 778E0B74279D341100F9BA6B /* BRPrivacyPopView.m */; };
		778E0B78279D6C7600F9BA6B /* FirstOpenViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 778E0B77279D6C7600F9BA6B /* FirstOpenViewController.m */; };
		7790B2082E06594800A08108 /* WithdrawPasswordViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7790B2072E06594800A08108 /* WithdrawPasswordViewController.m */; };
		7793B9642C15F1DD006AD7FF /* BRMessagePrescribeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7793B9632C15F1DD006AD7FF /* BRMessagePrescribeViewController.m */; };
		7793D29F2A236B9400971D97 /* BRTakerInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7793D29E2A236B9400971D97 /* BRTakerInfoModel.m */; };
		7793D2A22A2371D600971D97 /* BRQuickPrescribePatientModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7793D2A12A2371D600971D97 /* BRQuickPrescribePatientModel.m */; };
		7795B578213E214E006A2A9E /* BillDetailsDrugStoreRewardCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 7795B577213E214E006A2A9E /* BillDetailsDrugStoreRewardCell.m */; };
		779642831FC40B9A00AA0976 /* BRSelectPatientView.m in Sources */ = {isa = PBXBuildFile; fileRef = 779642791FC40B9A00AA0976 /* BRSelectPatientView.m */; };
		779642841FC40B9A00AA0976 /* UILabel+myLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7796427B1FC40B9A00AA0976 /* UILabel+myLabel.m */; };
		779642851FC40B9A00AA0976 /* BRSelectCityZone.m in Sources */ = {isa = PBXBuildFile; fileRef = 7796427E1FC40B9A00AA0976 /* BRSelectCityZone.m */; };
		779642861FC40B9A00AA0976 /* BRDHBaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 779642811FC40B9A00AA0976 /* BRDHBaseViewController.m */; };
		7796429E1FC4214500AA0976 /* BRAddVisitsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 779642911FC4214500AA0976 /* BRAddVisitsViewController.m */; };
		7796429F1FC4214500AA0976 /* BRVisitsArrangementViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 779642931FC4214500AA0976 /* BRVisitsArrangementViewController.m */; };
		779642A01FC4214500AA0976 /* BRCalendarCollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 779642961FC4214500AA0976 /* BRCalendarCollectionViewCell.m */; };
		779642A11FC4214500AA0976 /* BRCalendarCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 779642971FC4214500AA0976 /* BRCalendarCollectionViewCell.xib */; };
		779642A21FC4214500AA0976 /* BRCalendarView.m in Sources */ = {isa = PBXBuildFile; fileRef = 779642991FC4214500AA0976 /* BRCalendarView.m */; };
		779642A31FC4214500AA0976 /* BRMyPurseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7796429D1FC4214500AA0976 /* BRMyPurseViewController.m */; };
		779F1799213CDD2700F44BE3 /* PatientFooterView.m in Sources */ = {isa = PBXBuildFile; fileRef = 779F1798213CDD2700F44BE3 /* PatientFooterView.m */; };
		779FC82B2DF5C7F4001CA5D3 /* YTXMonitor.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 779FC8292DF5C7F4001CA5D3 /* YTXMonitor.framework */; };
		779FC82C2DF5C7F4001CA5D3 /* YTXOperators.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 779FC82A2DF5C7F4001CA5D3 /* YTXOperators.framework */; };
		779FC82D2DF5C7F4001CA5D3 /* ATAuthSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 779FC8282DF5C7F4001CA5D3 /* ATAuthSDK.framework */; };
		77A3BB5D2D09D65F0074A786 /* BRUpdateUserInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77A3BB5C2D09D65F0074A786 /* BRUpdateUserInfoModel.m */; };
		77A546211FFE46020086EFD8 /* InterrogationAndVisitViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77A546201FFE46020086EFD8 /* InterrogationAndVisitViewController.m */; };
		77A546241FFE52A80086EFD8 /* BRNameTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 77A546231FFE52A80086EFD8 /* BRNameTextField.m */; };
		77A7DF991FA09F520012C18C /* EWVoiceHUD.m in Sources */ = {isa = PBXBuildFile; fileRef = 77A7DF981FA09F520012C18C /* EWVoiceHUD.m */; };
		77A7DF9C1FA0A10F0012C18C /* AACRecord.m in Sources */ = {isa = PBXBuildFile; fileRef = 77A7DF9B1FA0A10F0012C18C /* AACRecord.m */; };
		77ABE80728B0A8BD00176DC3 /* QuickReplyShowView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77ABE80628B0A8BD00176DC3 /* QuickReplyShowView.m */; };
		77ABE80B28B0B57200176DC3 /* QuickReplyContentModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77ABE80A28B0B57200176DC3 /* QuickReplyContentModel.m */; };
		77ABE80E28B0B5EE00176DC3 /* QuickReplyTypeInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77ABE80D28B0B5EE00176DC3 /* QuickReplyTypeInfoModel.m */; };
		77ABE81128B0E3C900176DC3 /* QuickReplyTopView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77ABE81028B0E3C900176DC3 /* QuickReplyTopView.m */; };
		77ABE81428B0EB9900176DC3 /* QuickReplyContentListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77ABE81328B0EB9900176DC3 /* QuickReplyContentListCell.m */; };
		77ABE81728B0F74800176DC3 /* QuickReplyQuesAddViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77ABE81628B0F74800176DC3 /* QuickReplyQuesAddViewController.m */; };
		77B3A54D2D24465000097E8A /* BRDrugSpecSelectView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B3A54C2D24465000097E8A /* BRDrugSpecSelectView.m */; };
		77B3A5502D24479A00097E8A /* TCMMatchResult.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B3A54F2D24479A00097E8A /* TCMMatchResult.m */; };
		77B3A5532D2447C000097E8A /* TCPMappingParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B3A5522D2447C000097E8A /* TCPMappingParser.m */; };
		77B481741F6638FF00466F3B /* IMChatCollectionViewLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481731F6638FF00466F3B /* IMChatCollectionViewLayout.m */; };
		77B481771F663A8D00466F3B /* IMChatContainerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481761F663A8D00466F3B /* IMChatContainerView.m */; };
		77B4817A1F663B8400466F3B /* IMChatInputPanel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481791F663B8400466F3B /* IMChatInputPanel.m */; };
		77B4817D1F665DDB00466F3B /* IMChatItemCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B4817C1F665DDB00466F3B /* IMChatItemCell.m */; };
		77B481801F665EDA00466F3B /* IMChatCollectionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B4817F1F665EDA00466F3B /* IMChatCollectionView.m */; };
		77B481841F6660E600466F3B /* IMChatViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481831F6660E600466F3B /* IMChatViewController.m */; };
		77B4818D1F66740A00466F3B /* HPGrowingTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B4818A1F66740A00466F3B /* HPGrowingTextView.m */; };
		77B4818E1F66740A00466F3B /* HPTextViewInternal.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B4818C1F66740A00466F3B /* HPTextViewInternal.m */; };
		77B481961F6676E100466F3B /* BRChatInputTextPanel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481951F6676E100466F3B /* BRChatInputTextPanel.m */; };
		77B481991F667A3700466F3B /* BRMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481981F667A3700466F3B /* BRMessage.m */; };
		77B4819C1F6682C800466F3B /* BRBaseMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B4819B1F6682C800466F3B /* BRBaseMessageCellLayout.m */; };
		77B4819F1F6683F200466F3B /* BRBaseMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B4819E1F6683F200466F3B /* BRBaseMessageCell.m */; };
		77B481A21F66849E00466F3B /* BRTextMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481A11F66849E00466F3B /* BRTextMessageCellLayout.m */; };
		77B481A51F6689E700466F3B /* BRTextMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481A41F6689E700466F3B /* BRTextMessageCell.m */; };
		77B481A81F668B5D00466F3B /* BRSystemMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481A71F668B5D00466F3B /* BRSystemMessageCellLayout.m */; };
		77B481AE1F668CC300466F3B /* NSAttributedString+BR.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481AD1F668CC300466F3B /* NSAttributedString+BR.m */; };
		77B481B11F668DC900466F3B /* BRSystemMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481B01F668DC900466F3B /* BRSystemMessageCell.m */; };
		77B481B41F668E6300466F3B /* BRDateMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481B31F668E6300466F3B /* BRDateMessageCellLayout.m */; };
		77B481BA1F668FC100466F3B /* BRDateMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B481B91F668FC100466F3B /* BRDateMessageCell.m */; };
		77B684981FFFAC2F00A1FFF8 /* BRSessionStartPatientMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B684971FFFAC2F00A1FFF8 /* BRSessionStartPatientMessageCell.m */; };
		77B6849B1FFFAC4900A1FFF8 /* BRSessionStartPatientMessageCellLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 77B6849A1FFFAC4900A1FFF8 /* BRSessionStartPatientMessageCellLayout.m */; };
		77BC46B52000DCCE0066CE29 /* BRVisitsHospitalName.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BC46B42000DCCE0066CE29 /* BRVisitsHospitalName.m */; };
		77BC46B82000DD280066CE29 /* BRVisitsHospitalAddress.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BC46B72000DD280066CE29 /* BRVisitsHospitalAddress.m */; };
		77BDAF4A26CE990F000470BE /* JTAreaInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BDAF4926CE990F000470BE /* JTAreaInfoModel.m */; };
		77BDD74620AC0CB400762078 /* BillDetailsPrescriptionOrderCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BDD74520AC0CB400762078 /* BillDetailsPrescriptionOrderCell.m */; };
		77BDD74920AC0CC800762078 /* BillDetailsWithdrawCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BDD74820AC0CC800762078 /* BillDetailsWithdrawCell.m */; };
		77BDD74C20AC10AE00762078 /* BillDetailsWithdrawFailedCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BDD74B20AC10AE00762078 /* BillDetailsWithdrawFailedCell.m */; };
		77BDD74F20AC10E400762078 /* BillDetailsConsultFeeCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BDD74E20AC10E400762078 /* BillDetailsConsultFeeCell.m */; };
		77BDD75220AC113F00762078 /* BillDetailsPatientRewardCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BDD75120AC113F00762078 /* BillDetailsPatientRewardCell.m */; };
		77BDD75520AC116A00762078 /* BillDetailsPlateformRewardCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BDD75420AC116A00762078 /* BillDetailsPlateformRewardCell.m */; };
		77BDD75820AC2ECF00762078 /* BillDetailsWithdrawServiceChargeCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BDD75720AC2ECF00762078 /* BillDetailsWithdrawServiceChargeCell.m */; };
		77BDD75B20AC2F3A00762078 /* BillDetailsWithdrawFailedServiceChargeCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BDD75A20AC2F3A00762078 /* BillDetailsWithdrawFailedServiceChargeCell.m */; };
		77BDD75E20AC506600762078 /* BillDetailsInviteCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BDD75D20AC506600762078 /* BillDetailsInviteCell.m */; };
		77BEE6611F5565BE00597489 /* BRSocketReceiveBaseModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BEE6601F5565BE00597489 /* BRSocketReceiveBaseModel.m */; };
		77BEE6651F55690A00597489 /* BRLoginModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BEE6641F55690A00597489 /* BRLoginModel.m */; };
		77BEE6691F556EC900597489 /* BRError.m in Sources */ = {isa = PBXBuildFile; fileRef = 77BEE6681F556EC900597489 /* BRError.m */; };
		77C021F528BAF4E000F551AF /* EnterQuickOrderViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77C021F428BAF4E000F551AF /* EnterQuickOrderViewController.m */; };
		77C1EA19212C036100256EA8 /* DrugStoreViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77C1EA18212C036100256EA8 /* DrugStoreViewController.m */; };
		77C29327293B0995001A5B45 /* DrugDetailListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77C29326293B0995001A5B45 /* DrugDetailListViewController.m */; };
		77C2932B293C29EA001A5B45 /* DrugInfoListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77C2932A293C29EA001A5B45 /* DrugInfoListCell.m */; };
		77C5D3C61F7A499500E836BE /* ManagerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77C5D3C51F7A499500E836BE /* ManagerViewController.m */; };
		77C5D3C91F7A4D6800E836BE /* HTTPRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 77C5D3C81F7A4D6800E836BE /* HTTPRequest.m */; };
		77C7D24E1F5815C300C6F31A /* UserCenterFooterView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77C7D24D1F5815C300C6F31A /* UserCenterFooterView.m */; };
		77CA9C3C1F5CFEBF00B6CDE5 /* IMSession.m in Sources */ = {isa = PBXBuildFile; fileRef = 77CA9C3B1F5CFEBF00B6CDE5 /* IMSession.m */; };
		77CB751322D5CAF000262B0C /* BrokerOrderListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77CB751222D5CAF000262B0C /* BrokerOrderListViewController.m */; };
		77CB751722D5DD4800262B0C /* BrokerOrderListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77CB751622D5DD4800262B0C /* BrokerOrderListCell.m */; };
		77CB751B22D5E7D500262B0C /* BrokerInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77CB751A22D5E7D500262B0C /* BrokerInfoModel.m */; };
		77CFB9462AD4D77600A1EA90 /* BeianViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77CFB9452AD4D77600A1EA90 /* BeianViewController.m */; };
		77CFB9492AD5031900A1EA90 /* InviteScanPrescribeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77CFB9482AD5031900A1EA90 /* InviteScanPrescribeViewController.m */; };
		77D14A8D22D6EA150013CC7D /* BrokerOrderSearchViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77D14A8C22D6EA150013CC7D /* BrokerOrderSearchViewController.m */; };
		77D39E501FD2A5F1004A01E0 /* UITextView+APSUIControlTargetAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 77D39E4C1FD2A5EF004A01E0 /* UITextView+APSUIControlTargetAction.m */; };
		77D39E511FD2A5F1004A01E0 /* UITextView+MaxLength.m in Sources */ = {isa = PBXBuildFile; fileRef = 77D39E4D1FD2A5EF004A01E0 /* UITextView+MaxLength.m */; };
		77D7782A1FEDEB3100CB1A6F /* BRAnnouncementView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77D778291FEDEB3100CB1A6F /* BRAnnouncementView.m */; };
		77DB713C1F56E07F00D07919 /* UserCenterPanelCollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77DB713B1F56E07F00D07919 /* UserCenterPanelCollectionViewCell.m */; };
		77DB713F1F56F34E00D07919 /* UserCenterHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77DB713E1F56F34E00D07919 /* UserCenterHeaderView.m */; };
		77E59D9A2C8C19B000CB2662 /* BRCustomPopupView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77E59D992C8C19B000CB2662 /* BRCustomPopupView.m */; };
		77E870151FE3C4AC005E0F74 /* BRContactModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77E870141FE3C4AC005E0F74 /* BRContactModel.m */; };
		77E8B6C722F2977D00D5C08C /* BrokerUserModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77E8B6C622F2977D00D5C08C /* BrokerUserModel.m */; };
		77E8B6CA22F298E900D5C08C /* BrokerOrderUserCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77E8B6C922F298E900D5C08C /* BrokerOrderUserCell.m */; };
		77EA2618295DA047000D1359 /* BRMessageTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77EA2617295DA047000D1359 /* BRMessageTitleView.m */; };
		77EA261B295EDE64000D1359 /* BRCashTypeInfoModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77EA261A295EDE64000D1359 /* BRCashTypeInfoModel.m */; };
		77EF33DC1FD8D84B0008B712 /* BRGuiZeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 77EF33DB1FD8D84B0008B712 /* BRGuiZeViewController.m */; };
		77EF50602DA2294000F085B1 /* AuthCheckHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 77EF505F2DA2294000F085B1 /* AuthCheckHelper.m */; };
		77EF7D6F1FEB549800232BC0 /* PatientsListXiaoRanView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77EF7D6E1FEB549800232BC0 /* PatientsListXiaoRanView.m */; };
		77F4E7711F6BBC9D00897C4A /* IMChatInputMoreView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77F4E7701F6BBC9D00897C4A /* IMChatInputMoreView.m */; };
		77F6D00D2D12878E00247F4E /* .env in Resources */ = {isa = PBXBuildFile; fileRef = 77F6D00C2D12878E00247F4E /* .env */; };
		77F7A1461F5E3D6600EA5FC1 /* UserCenterListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77F7A1451F5E3D6600EA5FC1 /* UserCenterListCell.m */; };
		77F7A1491F5E3DCF00EA5FC1 /* ListCellModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 77F7A1481F5E3DCF00EA5FC1 /* ListCellModel.m */; };
		77F7A14C1F5E3FCD00EA5FC1 /* AboutHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 77F7A14B1F5E3FCD00EA5FC1 /* AboutHeader.m */; };
		77F7A14F1F5E51AB00EA5FC1 /* AboutFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 77F7A14E1F5E51AB00EA5FC1 /* AboutFooter.m */; };
		77FCA5D31F60EC5400369CB9 /* PopListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77FCA5D21F60EC5400369CB9 /* PopListCell.m */; };
		77FCA5DB1F6136D400369CB9 /* PopoverAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 77FCA5D61F6136D400369CB9 /* PopoverAction.m */; };
		77FCA5DC1F6136D400369CB9 /* PopoverView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77FCA5D81F6136D400369CB9 /* PopoverView.m */; };
		77FCA5DD1F6136D400369CB9 /* PopoverViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 77FCA5DA1F6136D400369CB9 /* PopoverViewCell.m */; };
		77FCA5E01F61512000369CB9 /* TitleBarView.m in Sources */ = {isa = PBXBuildFile; fileRef = 77FCA5DF1F61512000369CB9 /* TitleBarView.m */; };
		A60629B41FFC77A9004BA00A /* HeadCollectionReusableView.m in Sources */ = {isa = PBXBuildFile; fileRef = A60629B31FFC77A9004BA00A /* HeadCollectionReusableView.m */; };
		A60629B71FFCD39A004BA00A /* PharmacopeiaCache.m in Sources */ = {isa = PBXBuildFile; fileRef = A60629B61FFCD39A004BA00A /* PharmacopeiaCache.m */; };
		A60629BA1FFCDD16004BA00A /* MedicineParticularsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A60629B81FFCDD16004BA00A /* MedicineParticularsViewController.m */; };
		A60629BD1FFCDDA9004BA00A /* MedicinePCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A60629BC1FFCDDA9004BA00A /* MedicinePCell.m */; };
		A60629C01FFCFD4F004BA00A /* ProblemSolvingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A60629BF1FFCFD4F004BA00A /* ProblemSolvingViewController.m */; };
		A60629C31FFD050D004BA00A /* SearchArchitectureViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A60629C21FFD050D004BA00A /* SearchArchitectureViewController.m */; };
		A60629C61FFD0E74004BA00A /* SearchArchitectureCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A60629C51FFD0E74004BA00A /* SearchArchitectureCell.m */; };
		A60B54521F95AB5500123620 /* ResultSummaryViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A60B54511F95AB5500123620 /* ResultSummaryViewController.m */; };
		A60B54551F95B07400123620 /* ResultSummaryCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A60B54541F95B07400123620 /* ResultSummaryCell.m */; };
		A60DECEF1FE3684100FE2C42 /* ArchitectureCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A60DECEE1FE3684100FE2C42 /* ArchitectureCell.m */; };
		A60DECF21FE3718700FE2C42 /* ArchitectureTabCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A60DECF11FE3718700FE2C42 /* ArchitectureTabCell.m */; };
		A60DECF51FE3DB7E00FE2C42 /* CommonlyUsedPartyModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A60DECF41FE3DB7E00FE2C42 /* CommonlyUsedPartyModel.m */; };
		A60FFDEF1FC2CD360097C11D /* InputDataViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A60FFDEE1FC2CD360097C11D /* InputDataViewController.m */; };
		A61044E12057F4A20055B6B2 /* SearchDownloadViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A61044E02057F4A20055B6B2 /* SearchDownloadViewController.m */; };
		A6106C1E1FF34CB7006E5D32 /* SchedulingModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A6106C1D1FF34CB7006E5D32 /* SchedulingModel.m */; };
		A6106C221FF39543006E5D32 /* MyDoctorModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A6106C211FF39543006E5D32 /* MyDoctorModel.m */; };
		A6106C251FF3AAC2006E5D32 /* QueryModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A6106C241FF3AAC2006E5D32 /* QueryModel.m */; };
		A6106C281FF3CA4B006E5D32 /* ResultSummaryModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A6106C271FF3CA4B006E5D32 /* ResultSummaryModel.m */; };
		A61BCE3D1FF24E84006C2EF1 /* ArchitectureModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A61BCE3C1FF24E84006C2EF1 /* ArchitectureModel.m */; };
		A61BCE401FF25129006C2EF1 /* TeamListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A61BCE3F1FF25129006C2EF1 /* TeamListModel.m */; };
		A61BCE431FF2574B006C2EF1 /* UserListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A61BCE421FF2574B006C2EF1 /* UserListModel.m */; };
		A61C69411FCE56C100E7AB32 /* CommonlyUsedPartyViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A61C69401FCE56C100E7AB32 /* CommonlyUsedPartyViewController.m */; };
		A61C69471FCE59C500E7AB32 /* ServiceSettingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A61C69461FCE59C500E7AB32 /* ServiceSettingViewController.m */; };
		A61C694A1FCE5A8400E7AB32 /* MyAnnouncementViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A61C69491FCE5A8400E7AB32 /* MyAnnouncementViewController.m */; };
		A61C694D1FCE5B4300E7AB32 /* BlacklistViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A61C694C1FCE5B4300E7AB32 /* BlacklistViewController.m */; };
		A61C69501FCE5BF400E7AB32 /* PharmacopoeiaViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A61C694F1FCE5BF400E7AB32 /* PharmacopoeiaViewController.m */; };
		A61C69531FCE975600E7AB32 /* PharmacopeiaModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A61C69521FCE975600E7AB32 /* PharmacopeiaModel.m */; };
		A61C69571FCEAA8000E7AB32 /* CommonlyUsedPartyCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A61C69561FCEAA8000E7AB32 /* CommonlyUsedPartyCell.m */; };
		A61F8E811FC6BB8600AB928E /* CameraManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A61F8E801FC6BB8500AB928E /* CameraManager.m */; };
		A62003D120901A9400D1B82F /* AvplayerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A62003D020901A9400D1B82F /* AvplayerViewController.m */; };
		A620D4EB20EF13FC0022A052 /* PhotoPrescAgreementViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A620D4EA20EF13FC0022A052 /* PhotoPrescAgreementViewController.m */; };
		A620D4EE20EF20370022A052 /* PhotoPrescViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A620D4ED20EF20370022A052 /* PhotoPrescViewController.m */; };
		A62459751F8F5AE900C88DD4 /* AboutBRZYViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A62459741F8F5AE900C88DD4 /* AboutBRZYViewController.m */; };
		A62459781F8F5C6100C88DD4 /* ToPushViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A62459771F8F5C6100C88DD4 /* ToPushViewController.m */; };
		A624597E1F8F640800C88DD4 /* WaitAcceptDoctorViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A624597D1F8F640800C88DD4 /* WaitAcceptDoctorViewController.m */; };
		A62459811F8F64A300C88DD4 /* MyDoctorViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A62459801F8F64A300C88DD4 /* MyDoctorViewController.m */; };
		A62459841F8F664100C88DD4 /* ResultsQueryViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A62459831F8F664100C88DD4 /* ResultsQueryViewController.m */; };
		A62459871F8F673D00C88DD4 /* TaskAllocationViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A62459861F8F673D00C88DD4 /* TaskAllocationViewController.m */; };
		A624598A1F8F715900C88DD4 /* WaitAcceptDoctorModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A62459891F8F715900C88DD4 /* WaitAcceptDoctorModel.m */; };
		A624598D1F8F71CE00C88DD4 /* WaitAcceptDoctorCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A624598C1F8F71CE00C88DD4 /* WaitAcceptDoctorCell.m */; };
		A62587402004CB3A00259622 /* RegisterTF.m in Sources */ = {isa = PBXBuildFile; fileRef = A625873F2004CB3A00259622 /* RegisterTF.m */; };
		A6274F251FEE405D000BE82A /* CompleteModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A6274F241FEE405D000BE82A /* CompleteModel.m */; };
		A6295B2A1FC959CF00BD1A53 /* OrderCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A6295B291FC959CF00BD1A53 /* OrderCell.m */; };
		A62ABA8F1FD12F8D00668558 /* ReplaceTelViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A62ABA8E1FD12F8D00668558 /* ReplaceTelViewController.m */; };
		A635E7FB1FBD2C810085E1D0 /* FindPasswordViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A635E7FA1FBD2C810085E1D0 /* FindPasswordViewController.m */; };
		A63695991FE7A79D002DFB0A /* SearchCommonViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A63695981FE7A79D002DFB0A /* SearchCommonViewController.m */; };
		A636959C1FE7DF5C002DFB0A /* BlackListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A636959B1FE7DF5C002DFB0A /* BlackListCell.m */; };
		A636959F1FE7E87C002DFB0A /* BlackListModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A636959E1FE7E87C002DFB0A /* BlackListModel.m */; };
		A63F1FFE1FF5ED6B004A0DFA /* DownloadCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A63F1FFD1FF5ED6B004A0DFA /* DownloadCell.m */; };
		A63F20011FF62ED5004A0DFA /* DownloadModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A63F20001FF62ED5004A0DFA /* DownloadModel.m */; };
		A642A8632137E8EF00C8FA8A /* GoodsContentViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A642A8622137E8EF00C8FA8A /* GoodsContentViewController.m */; };
		A64703B9204E302800F33B3D /* InvitaUnregisteredViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64703B8204E302800F33B3D /* InvitaUnregisteredViewController.m */; };
		A64703BC204E310A00F33B3D /* RegisterUnauthorizedViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64703BB204E310A00F33B3D /* RegisterUnauthorizedViewController.m */; };
		A64703BF204E323C00F33B3D /* CertificationProcessViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64703BE204E323C00F33B3D /* CertificationProcessViewController.m */; };
		A64703C2204E32CB00F33B3D /* CertificationFailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64703C1204E32CB00F33B3D /* CertificationFailViewController.m */; };
		A64703C5204E33AA00F33B3D /* CertificationNoPatientViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64703C4204E33AA00F33B3D /* CertificationNoPatientViewController.m */; };
		A64703C8204E340F00F33B3D /* NoPrescriptionViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64703C7204E340F00F33B3D /* NoPrescriptionViewController.m */; };
		A64703CB204E357F00F33B3D /* PrescriptionNoPayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64703CA204E357F00F33B3D /* PrescriptionNoPayViewController.m */; };
		A64703CE204E35E600F33B3D /* CertificationViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64703CD204E35E600F33B3D /* CertificationViewController.m */; };
		A64703D1204E3F7700F33B3D /* AbnormalDoctorViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64703D0204E3F7700F33B3D /* AbnormalDoctorViewController.m */; };
		A64703D5204E8E0A00F33B3D /* InvitaUnregisteredCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A64703D4204E8E0A00F33B3D /* InvitaUnregisteredCell.m */; };
		A64703D8204E92F500F33B3D /* RegisterUnauthorizedCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A64703D7204E92F500F33B3D /* RegisterUnauthorizedCell.m */; };
		A64703DB204EA20F00F33B3D /* CertificationProcessCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A64703DA204EA20F00F33B3D /* CertificationProcessCell.m */; };
		A64761551F94B00000C84511 /* PatientNumberViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64761541F94B00000C84511 /* PatientNumberViewController.m */; };
		A64761641F94B91800C84511 /* PatientNumberCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A64761631F94B91800C84511 /* PatientNumberCell.m */; };
		A649543F1F8B72D600F16C42 /* QualificationCertificationViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A649543E1F8B72D600F16C42 /* QualificationCertificationViewController.m */; };
		A64954421F8B75E600F16C42 /* SecuritySettingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64954411F8B75E600F16C42 /* SecuritySettingViewController.m */; };
		A64954451F8B76DF00F16C42 /* CommonProblemsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64954441F8B76DF00F16C42 /* CommonProblemsViewController.m */; };
		A64954481F8B77B100F16C42 /* ProblemFeedbackViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64954471F8B77B100F16C42 /* ProblemFeedbackViewController.m */; };
		A649544E1F8B791B00F16C42 /* AboutViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A649544D1F8B791B00F16C42 /* AboutViewController.m */; };
		A64C6CBF1FC0464500138EB1 /* PersonalDataHeaderCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A64C6CBE1FC0464500138EB1 /* PersonalDataHeaderCell.m */; };
		A64C6CC21FC0476800138EB1 /* PersonalDataListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A64C6CC11FC0476800138EB1 /* PersonalDataListCell.m */; };
		A64E491C1F8C6BC50060CCDF /* PersonalDataViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A64E491B1F8C6BC50060CCDF /* PersonalDataViewController.m */; };
		A64F46F6204F806C004C5FAB /* DoctorStateModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A64F46F5204F806C004C5FAB /* DoctorStateModel.m */; };
		A64F46F92050060F004C5FAB /* CertificationFailCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A64F46F82050060F004C5FAB /* CertificationFailCell.m */; };
		A6504DEB20ADA4D4001C00F2 /* SearchSummaryViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A6504DEA20ADA4D4001C00F2 /* SearchSummaryViewController.m */; };
		A660C9041FCBF25800D36C7C /* CompleteTotalOrGapCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A660C9031FCBF25800D36C7C /* CompleteTotalOrGapCell.m */; };
		A660C9071FCBFE3C00D36C7C /* CompleteDetailCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A660C9061FCBFE3C00D36C7C /* CompleteDetailCell.m */; };
		A660C90A1FCC06D900D36C7C /* CompleteTotalCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A660C9091FCC06D900D36C7C /* CompleteTotalCell.m */; };
		A661A7F020EE1D9C009CFD40 /* UIImageView+ZFCache.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7B520EE1D9B009CFD40 /* UIImageView+ZFCache.m */; };
		A661A7F120EE1D9C009CFD40 /* UIView+ZFFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7B720EE1D9B009CFD40 /* UIView+ZFFrame.m */; };
		A661A7F220EE1D9C009CFD40 /* ZFLandScapeControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7B920EE1D9B009CFD40 /* ZFLandScapeControlView.m */; };
		A661A7F320EE1D9C009CFD40 /* ZFLoadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7BB20EE1D9B009CFD40 /* ZFLoadingView.m */; };
		A661A7F420EE1D9C009CFD40 /* ZFNetworkSpeedMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7BD20EE1D9B009CFD40 /* ZFNetworkSpeedMonitor.m */; };
		A661A7F520EE1D9C009CFD40 /* ZFPlayer.bundle in Resources */ = {isa = PBXBuildFile; fileRef = A661A7BE20EE1D9B009CFD40 /* ZFPlayer.bundle */; };
		A661A7F620EE1D9C009CFD40 /* ZFPlayerControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7C020EE1D9B009CFD40 /* ZFPlayerControlView.m */; };
		A661A7F720EE1D9C009CFD40 /* ZFPortraitControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7C220EE1D9B009CFD40 /* ZFPortraitControlView.m */; };
		A661A7F820EE1D9C009CFD40 /* ZFSliderView.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7C420EE1D9B009CFD40 /* ZFSliderView.m */; };
		A661A7F920EE1D9C009CFD40 /* ZFSmallFloatControlView.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7C620EE1D9B009CFD40 /* ZFSmallFloatControlView.m */; };
		A661A7FA20EE1D9C009CFD40 /* ZFSpeedLoadingView.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7C820EE1D9B009CFD40 /* ZFSpeedLoadingView.m */; };
		A661A7FB20EE1D9C009CFD40 /* ZFUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7CA20EE1D9B009CFD40 /* ZFUtilities.m */; };
		A661A7FC20EE1D9C009CFD40 /* ZFVolumeBrightnessView.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7CC20EE1D9B009CFD40 /* ZFVolumeBrightnessView.m */; };
		A661A7FD20EE1D9C009CFD40 /* ZFIJKPlayerManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7CF20EE1D9C009CFD40 /* ZFIJKPlayerManager.m */; };
		A661A7FE20EE1D9C009CFD40 /* KSMediaPlayerManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7D220EE1D9C009CFD40 /* KSMediaPlayerManager.m */; };
		A661A7FF20EE1D9C009CFD40 /* UIScrollView+ZFPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7D520EE1D9C009CFD40 /* UIScrollView+ZFPlayer.m */; };
		A661A80020EE1D9C009CFD40 /* UIViewController+ZFPlayerRotation.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7D720EE1D9C009CFD40 /* UIViewController+ZFPlayerRotation.m */; };
		A661A80120EE1D9C009CFD40 /* ZFFloatView.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7D920EE1D9C009CFD40 /* ZFFloatView.m */; };
		A661A80220EE1D9C009CFD40 /* ZFKVOController.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7DB20EE1D9C009CFD40 /* ZFKVOController.m */; };
		A661A80320EE1D9C009CFD40 /* ZFOrientationObserver.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7DD20EE1D9C009CFD40 /* ZFOrientationObserver.m */; };
		A661A80420EE1D9C009CFD40 /* ZFPlayerController.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7E020EE1D9C009CFD40 /* ZFPlayerController.m */; };
		A661A80520EE1D9C009CFD40 /* ZFPlayerGestureControl.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7E220EE1D9C009CFD40 /* ZFPlayerGestureControl.m */; };
		A661A80620EE1D9C009CFD40 /* ZFPlayerLogManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7E420EE1D9C009CFD40 /* ZFPlayerLogManager.m */; };
		A661A80720EE1D9C009CFD40 /* ZFPlayerNotification.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7E820EE1D9C009CFD40 /* ZFPlayerNotification.m */; };
		A661A80820EE1D9C009CFD40 /* ZFPlayerView.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7EA20EE1D9C009CFD40 /* ZFPlayerView.m */; };
		A661A80920EE1D9C009CFD40 /* ZFReachabilityManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7EC20EE1D9C009CFD40 /* ZFReachabilityManager.m */; };
		A661A80A20EE1D9C009CFD40 /* ZFAVPlayerManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A661A7EF20EE1D9C009CFD40 /* ZFAVPlayerManager.m */; };
		A66EAD321F9063AB0065ADC3 /* TaskAllocationCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A66EAD311F9063AB0065ADC3 /* TaskAllocationCell.m */; };
		A66EAD351F90979E0065ADC3 /* MyDoctorHeaderCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A66EAD341F90979E0065ADC3 /* MyDoctorHeaderCell.m */; };
		A66EAD381F909A400065ADC3 /* MyDoctorListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A66EAD371F909A400065ADC3 /* MyDoctorListCell.m */; };
		A66EAD3B1F90AABD0065ADC3 /* ResultsQueryCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A66EAD3A1F90AABD0065ADC3 /* ResultsQueryCell.m */; };
		A67511DD1FD6CA4E00B6DDD3 /* CertificationSuccessViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A67511DC1FD6CA4E00B6DDD3 /* CertificationSuccessViewController.m */; };
		A67B75871FC51FC4005C637F /* OrderViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A67B75861FC51FC4005C637F /* OrderViewController.m */; };
		A67CF4BA2058B9B900E2DEEA /* TwoWeeksNotPrescViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A67CF4B92058B9B900E2DEEA /* TwoWeeksNotPrescViewController.m */; };
		A67CF4BD2058BA0700E2DEEA /* PrescLessOneHundredViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A67CF4BC2058BA0700E2DEEA /* PrescLessOneHundredViewController.m */; };
		A67CF4C0205949B500E2DEEA /* SearchChooseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A67CF4BF205949B500E2DEEA /* SearchChooseViewController.m */; };
		A68CE8AA1FE22289008E5F05 /* OrdersModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A68CE8A91FE22289008E5F05 /* OrdersModel.m */; };
		A68CE8AD1FE2684A008E5F05 /* DownloadViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A68CE8AC1FE2684A008E5F05 /* DownloadViewController.m */; };
		A68CE8B01FE272C7008E5F05 /* CMOAssistantViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A68CE8AF1FE272C7008E5F05 /* CMOAssistantViewController.m */; };
		A68CE8B31FE275A1008E5F05 /* SchedulingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A68CE8B21FE275A1008E5F05 /* SchedulingViewController.m */; };
		A68CE8B61FE275F8008E5F05 /* ArchitectureViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A68CE8B51FE275F8008E5F05 /* ArchitectureViewController.m */; };
		A68CE8BA1FE277A8008E5F05 /* SchedulingCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A68CE8B91FE277A8008E5F05 /* SchedulingCell.m */; };
		A68FA27B1FD5435800DAF689 /* SeeTheSampleViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A68FA27A1FD5435800DAF689 /* SeeTheSampleViewController.m */; };
		A691B0771FD8E29300D29516 /* IsReviewingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A691B0761FD8E29300D29516 /* IsReviewingViewController.m */; };
		A69A30151FFB90AD007E9413 /* ScanQrcodeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A69A30141FFB90AD007E9413 /* ScanQrcodeViewController.m */; };
		A69A30181FFBA7FA007E9413 /* AllocationViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A69A30171FFBA7FA007E9413 /* AllocationViewController.m */; };
		A69A301B1FFBAEDB007E9413 /* AllocationCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A69A301A1FFBAEDB007E9413 /* AllocationCell.m */; };
		A69A301E1FFBB9C0007E9413 /* ArchitectureCollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A69A301D1FFBB9C0007E9413 /* ArchitectureCollectionViewCell.m */; };
		A69F62D91F7CF5F9004B7AE8 /* UserCenterHeaderCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A69F62D81F7CF5F9004B7AE8 /* UserCenterHeaderCell.m */; };
		A6A1CD531FEA073D00044E3C /* CommonProblemsModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A6A1CD521FEA073D00044E3C /* CommonProblemsModel.m */; };
		A6A4CF931FB942D300B545F6 /* BrzyAgreementViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A6A4CF921FB942D300B545F6 /* BrzyAgreementViewController.m */; };
		A6A574DA213528FD007F90AE /* OrderContentViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A6A574D9213528FD007F90AE /* OrderContentViewController.m */; };
		A6A6A8301FE93FBD00249235 /* CommonProblemsCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A6A6A82F1FE93FBD00249235 /* CommonProblemsCell.m */; };
		A6C45FAD1F96F64900F85928 /* DoctorDetailsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A6C45FAC1F96F64900F85928 /* DoctorDetailsViewController.m */; };
		A6C45FB01F96FFAC00F85928 /* DoctorDetailCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A6C45FAF1F96FFAC00F85928 /* DoctorDetailCell.m */; };
		A6C45FB31F9735B100F85928 /* RegisterViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A6C45FB21F9735B100F85928 /* RegisterViewController.m */; };
		A6C74F26212D07D800BC2AD7 /* OrderRecordViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A6C74F25212D07D800BC2AD7 /* OrderRecordViewController.m */; };
		A6C74F29212D495F00BC2AD7 /* OrderRecordView.m in Sources */ = {isa = PBXBuildFile; fileRef = A6C74F28212D495F00BC2AD7 /* OrderRecordView.m */; };
		A6C74F2C212D5C2600BC2AD7 /* OrderRecordCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A6C74F2B212D5C2600BC2AD7 /* OrderRecordCell.m */; };
		A6D9E7D61FFE4BF600694EC4 /* UITabBar+bagde.m in Sources */ = {isa = PBXBuildFile; fileRef = A6D9E7D51FFE4BF600694EC4 /* UITabBar+bagde.m */; };
		A6E4212920AE7AB700321AB4 /* SummaryMonthDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A6E4212820AE7AB700321AB4 /* SummaryMonthDetailViewController.m */; };
		A6E4212C20AE82CB00321AB4 /* SummaryMonthViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A6E4212B20AE82CB00321AB4 /* SummaryMonthViewController.m */; };
		A6F85C5A212E6D2E00780DA6 /* OrderRecordModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A6F85C59212E6D2E00780DA6 /* OrderRecordModel.m */; };
		A6F85C5D212EB6FA00780DA6 /* CompletedView.m in Sources */ = {isa = PBXBuildFile; fileRef = A6F85C5C212EB6FA00780DA6 /* CompletedView.m */; };
		A6F85C60212EBCEE00780DA6 /* CompletedCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A6F85C5F212EBCEE00780DA6 /* CompletedCell.m */; };
		A6FC85271FF4915D005B6CA8 /* TaskAllocationModel.m in Sources */ = {isa = PBXBuildFile; fileRef = A6FC85261FF4915D005B6CA8 /* TaskAllocationModel.m */; };
		A6FC852A1FF4E35A005B6CA8 /* ChooseAgentViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A6FC85291FF4E35A005B6CA8 /* ChooseAgentViewController.m */; };
		A6FC852D1FF4E416005B6CA8 /* ChooseAgentCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A6FC852C1FF4E416005B6CA8 /* ChooseAgentCell.m */; };
		A6FC85301FF4EB15005B6CA8 /* ChooseAgentmodel.m in Sources */ = {isa = PBXBuildFile; fileRef = A6FC852F1FF4EB15005B6CA8 /* ChooseAgentmodel.m */; };
		A6FF5D90204D38FE00261A8F /* DoctorStateCollecCell.m in Sources */ = {isa = PBXBuildFile; fileRef = A6FF5D8F204D38FE00261A8F /* DoctorStateCollecCell.m */; };
		FB82BBB2CE596EA49456D763 /* Pods_BRZY.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 89DB2C7A093AA53BF55F5BBE /* Pods_BRZY.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0A097E411FCFDF4F00A1BFEF /* BRPatientModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPatientModel.h; sourceTree = "<group>"; };
		0A097E421FCFDF4F00A1BFEF /* BRPatientModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPatientModel.m; sourceTree = "<group>"; };
		0A2999201FCBB38B003C5CAA /* BRSearchModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSearchModel.h; sourceTree = "<group>"; };
		0A2999211FCBB38B003C5CAA /* BRSearchModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSearchModel.m; sourceTree = "<group>"; };
		0A3124572004974700D56D36 /* BRAPNsModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRAPNsModule.h; sourceTree = "<group>"; };
		0A3124582004974700D56D36 /* BRAPNsModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRAPNsModule.m; sourceTree = "<group>"; };
		0A31245A2004999E00D56D36 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		0A31245C200499BE00D56D36 /* CoreFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreFoundation.framework; path = System/Library/Frameworks/CoreFoundation.framework; sourceTree = SDKROOT; };
		0A31245E200499DE00D56D36 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		0A31246020049A1700D56D36 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		0A31246220049A4100D56D36 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		0A31246320049A5300D56D36 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		0A31246420049A6200D56D36 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		0A31246520049AC800D56D36 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		0A31246620049AD400D56D36 /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		0A31246820049AE600D56D36 /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		0A31246920049AF200D56D36 /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		0A42DD141FD7DEFA0060D4F3 /* BRPresSelectTypeDetailCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresSelectTypeDetailCell.h; sourceTree = "<group>"; };
		0A42DD151FD7DEFA0060D4F3 /* BRPresSelectTypeDetailCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresSelectTypeDetailCell.m; sourceTree = "<group>"; };
		0A5F4D4F1FBC3B5F00491FE7 /* PrescriptionViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PrescriptionViewController.h; sourceTree = "<group>"; };
		0A5F4D501FBC3B5F00491FE7 /* PrescriptionViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PrescriptionViewController.m; sourceTree = "<group>"; };
		0A5F4D521FBC486800491FE7 /* BRPrescriptionPatientView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPrescriptionPatientView.h; sourceTree = "<group>"; };
		0A5F4D531FBC486800491FE7 /* BRPrescriptionPatientView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPrescriptionPatientView.m; sourceTree = "<group>"; };
		0A5F4D551FBEF18500491FE7 /* BRPresUsageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPresUsageView.h; sourceTree = "<group>"; };
		0A5F4D561FBEF18500491FE7 /* BRPresUsageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPresUsageView.m; sourceTree = "<group>"; };
		0A5F4D581FC0313800491FE7 /* BRPresOtherView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresOtherView.h; sourceTree = "<group>"; };
		0A5F4D591FC0313800491FE7 /* BRPresOtherView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresOtherView.m; sourceTree = "<group>"; };
		0A69BE161FD246D5009F8630 /* BRPresInfoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresInfoView.h; sourceTree = "<group>"; };
		0A69BE171FD246D5009F8630 /* BRPresInfoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresInfoView.m; sourceTree = "<group>"; };
		0A69BE191FD29E48009F8630 /* BRPresSelectTypeView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresSelectTypeView.h; sourceTree = "<group>"; };
		0A69BE1A1FD29E48009F8630 /* BRPresSelectTypeView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresSelectTypeView.m; sourceTree = "<group>"; };
		0A69BE1D1FD2ACD8009F8630 /* BRPresSelectTypeMasterCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresSelectTypeMasterCell.h; sourceTree = "<group>"; };
		0A69BE1E1FD2ACD8009F8630 /* BRPresSelectTypeMasterCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresSelectTypeMasterCell.m; sourceTree = "<group>"; };
		0A69E5BA1FD940BE003342A6 /* BRFactoryModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRFactoryModel.h; sourceTree = "<group>"; };
		0A69E5BB1FD940BE003342A6 /* BRFactoryModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRFactoryModel.m; sourceTree = "<group>"; };
		0A69E5BD1FD946E7003342A6 /* BRSubFactoryModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSubFactoryModel.h; sourceTree = "<group>"; };
		0A69E5BE1FD946E7003342A6 /* BRSubFactoryModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSubFactoryModel.m; sourceTree = "<group>"; };
		0A6F253F1FB3FF8D001D4FC9 /* BRPrescriptionInputPanel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPrescriptionInputPanel.h; sourceTree = "<group>"; };
		0A6F25401FB3FF8D001D4FC9 /* BRPrescriptionInputPanel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPrescriptionInputPanel.m; sourceTree = "<group>"; };
		0A71DB0F1F94668C00BF831F /* AddDrugViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AddDrugViewController.h; sourceTree = "<group>"; };
		0A71DB101F94668C00BF831F /* AddDrugViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AddDrugViewController.m; sourceTree = "<group>"; };
		0A71DB131F948F1800BF831F /* BRPresInputView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPresInputView.h; sourceTree = "<group>"; };
		0A71DB141F948F1800BF831F /* BRPresInputView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPresInputView.m; sourceTree = "<group>"; };
		0A71DB1C1F959E6300BF831F /* BRSubMedicineModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRSubMedicineModel.h; sourceTree = "<group>"; };
		0A71DB1D1F959E6300BF831F /* BRSubMedicineModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRSubMedicineModel.m; sourceTree = "<group>"; };
		0A71DB1F1F976DDF00BF831F /* BRPresNoticeView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPresNoticeView.h; sourceTree = "<group>"; };
		0A71DB201F976DDF00BF831F /* BRPresNoticeView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPresNoticeView.m; sourceTree = "<group>"; };
		0A73F46E1FE51A9000125F2D /* BRRiskTipModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRRiskTipModel.h; sourceTree = "<group>"; };
		0A73F46F1FE51A9000125F2D /* BRRiskTipModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRRiskTipModel.m; sourceTree = "<group>"; };
		0A7FD4661FCE8DCC00318462 /* BRDatePicker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRDatePicker.h; sourceTree = "<group>"; };
		0A7FD4671FCE8DCC00318462 /* BRDatePicker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRDatePicker.m; sourceTree = "<group>"; };
		0A89E71D1FF0A24100834D48 /* FactoryInfoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FactoryInfoViewController.h; sourceTree = "<group>"; };
		0A89E71E1FF0A24100834D48 /* FactoryInfoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FactoryInfoViewController.m; sourceTree = "<group>"; };
		0A8CFB3A1FC7B704006A449E /* BRPresContraindicationView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresContraindicationView.h; sourceTree = "<group>"; };
		0A8CFB3B1FC7B704006A449E /* BRPresContraindicationView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresContraindicationView.m; sourceTree = "<group>"; };
		0A9202071F8F6DC10053B7D2 /* BRUnderlineRedTextField.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRUnderlineRedTextField.h; sourceTree = "<group>"; };
		0A9202081F8F6DC10053B7D2 /* BRUnderlineRedTextField.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRUnderlineRedTextField.m; sourceTree = "<group>"; };
		0A97FEDB1FE263F50043D14D /* BRPresReplaceDrugTitleCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresReplaceDrugTitleCell.h; sourceTree = "<group>"; };
		0A97FEDC1FE263F50043D14D /* BRPresReplaceDrugTitleCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresReplaceDrugTitleCell.m; sourceTree = "<group>"; };
		0A99260F1FCD6800003B537E /* BRPresAddPatientView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresAddPatientView.h; sourceTree = "<group>"; };
		0A9926101FCD6800003B537E /* BRPresAddPatientView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresAddPatientView.m; sourceTree = "<group>"; };
		0A9A1BFA1FE13CB600F5DA0E /* BRPresInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresInfoModel.h; sourceTree = "<group>"; };
		0A9A1BFB1FE13CB600F5DA0E /* BRPresInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresInfoModel.m; sourceTree = "<group>"; };
		0A9CED181F7B3A380072CF4A /* BRPrescriptionTitleView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPrescriptionTitleView.h; sourceTree = "<group>"; };
		0A9CED191F7B3A380072CF4A /* BRPrescriptionTitleView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPrescriptionTitleView.m; sourceTree = "<group>"; };
		0A9CED1B1F7B7BFF0072CF4A /* BRPrescriptionDiagnosesView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPrescriptionDiagnosesView.h; sourceTree = "<group>"; };
		0A9CED1C1F7B7BFF0072CF4A /* BRPrescriptionDiagnosesView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPrescriptionDiagnosesView.m; sourceTree = "<group>"; };
		0A9CED1E1F7CD28C0072CF4A /* BRPrescriptionDisplayView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPrescriptionDisplayView.h; sourceTree = "<group>"; };
		0A9CED1F1F7CD28C0072CF4A /* BRPrescriptionDisplayView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPrescriptionDisplayView.m; sourceTree = "<group>"; };
		0A9CED211F7CE1F20072CF4A /* BRPrescriptionDrugListView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPrescriptionDrugListView.h; sourceTree = "<group>"; };
		0A9CED221F7CE1F20072CF4A /* BRPrescriptionDrugListView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPrescriptionDrugListView.m; sourceTree = "<group>"; };
		0A9CED241F7CE36F0072CF4A /* BRPrescriptionModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPrescriptionModel.h; sourceTree = "<group>"; };
		0A9CED251F7CE36F0072CF4A /* BRPrescriptionModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPrescriptionModel.m; sourceTree = "<group>"; };
		0A9CED281F7CE5EE0072CF4A /* BRMedicineModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRMedicineModel.h; sourceTree = "<group>"; };
		0A9CED291F7CE5EE0072CF4A /* BRMedicineModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRMedicineModel.m; sourceTree = "<group>"; };
		0A9CED2B1F7CE6C40072CF4A /* BRPrescriptionDrugCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPrescriptionDrugCell.h; sourceTree = "<group>"; };
		0A9CED2C1F7CE6C40072CF4A /* BRPrescriptionDrugCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPrescriptionDrugCell.m; sourceTree = "<group>"; };
		0AA18C761FF33A9D00CC3C61 /* ZYActivitiesView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZYActivitiesView.h; sourceTree = "<group>"; };
		0AA18C771FF33A9D00CC3C61 /* ZYActivitiesView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZYActivitiesView.m; sourceTree = "<group>"; };
		0AA18C781FF33A9D00CC3C61 /* ZYActivitiesViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZYActivitiesViewController.h; sourceTree = "<group>"; };
		0AA18C791FF33A9D00CC3C61 /* ZYActivitiesViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZYActivitiesViewController.m; sourceTree = "<group>"; };
		0AA18C7A1FF33A9D00CC3C61 /* ZYActivityImgView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZYActivityImgView.h; sourceTree = "<group>"; };
		0AA18C7B1FF33A9D00CC3C61 /* ZYActivityImgView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZYActivityImgView.m; sourceTree = "<group>"; };
		0AA18C7C1FF33A9D00CC3C61 /* ZYActivityModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZYActivityModel.h; sourceTree = "<group>"; };
		0AA18C7D1FF33A9D00CC3C61 /* ZYActivityModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZYActivityModel.m; sourceTree = "<group>"; };
		0AA18C7E1FF33A9D00CC3C61 /* ZYPageControl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZYPageControl.h; sourceTree = "<group>"; };
		0AA18C7F1FF33A9D00CC3C61 /* ZYPageControl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZYPageControl.m; sourceTree = "<group>"; };
		0AA18C801FF33A9D00CC3C61 /* ZYScrollView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZYScrollView.h; sourceTree = "<group>"; };
		0AA18C811FF33A9D00CC3C61 /* ZYScrollView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZYScrollView.m; sourceTree = "<group>"; };
		0AA894D81FDEA2B60034C3A2 /* BRPresReplaceDrugView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresReplaceDrugView.h; sourceTree = "<group>"; };
		0AA894D91FDEA2B60034C3A2 /* BRPresReplaceDrugView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresReplaceDrugView.m; sourceTree = "<group>"; };
		0AA894DC1FDEABAF0034C3A2 /* BRPresReplaceDrugCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresReplaceDrugCell.h; sourceTree = "<group>"; };
		0AA894DD1FDEABAF0034C3A2 /* BRPresReplaceDrugCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresReplaceDrugCell.m; sourceTree = "<group>"; };
		0AADC48B1FFF9A96008F29D6 /* BRContraindicationModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRContraindicationModel.h; sourceTree = "<group>"; };
		0AADC48C1FFF9A96008F29D6 /* BRContraindicationModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRContraindicationModel.m; sourceTree = "<group>"; };
		0ABEE64D1FC2B551006FE61B /* BRPresNoteView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresNoteView.h; sourceTree = "<group>"; };
		0ABEE64E1FC2B551006FE61B /* BRPresNoteView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresNoteView.m; sourceTree = "<group>"; };
		0ABEE6501FC67073006FE61B /* BRPresDrugCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresDrugCell.h; sourceTree = "<group>"; };
		0ABEE6511FC67073006FE61B /* BRPresDrugCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresDrugCell.m; sourceTree = "<group>"; };
		0ABEE6531FC672A6006FE61B /* BRZYTextField.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRZYTextField.h; sourceTree = "<group>"; };
		0ABEE6541FC672A6006FE61B /* BRZYTextField.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRZYTextField.m; sourceTree = "<group>"; };
		0ACF4F231FC943A400A3B87F /* BRPresHaveNoticeCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPresHaveNoticeCell.h; sourceTree = "<group>"; };
		0ACF4F241FC943A400A3B87F /* BRPresHaveNoticeCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPresHaveNoticeCell.m; sourceTree = "<group>"; };
		0ACF4F271FC974A500A3B87F /* MMNumberKeyboard.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MMNumberKeyboard.h; sourceTree = "<group>"; };
		0ACF4F281FC974A500A3B87F /* MMNumberKeyboard.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MMNumberKeyboard.m; sourceTree = "<group>"; };
		0AF203211F8B699700432DD6 /* BRPrescriptionDescView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPrescriptionDescView.h; sourceTree = "<group>"; };
		0AF203221F8B699700432DD6 /* BRPrescriptionDescView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPrescriptionDescView.m; sourceTree = "<group>"; };
		0AF203241F8B6E8F00432DD6 /* BRPresDescTitleView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPresDescTitleView.h; sourceTree = "<group>"; };
		0AF203251F8B6E8F00432DD6 /* BRPresDescTitleView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPresDescTitleView.m; sourceTree = "<group>"; };
		0AF203271F8CBB0300432DD6 /* BRUnderlineTextField.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRUnderlineTextField.h; sourceTree = "<group>"; };
		0AF203281F8CBB0300432DD6 /* BRUnderlineTextField.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRUnderlineTextField.m; sourceTree = "<group>"; };
		0AF2032A1F8CBF8800432DD6 /* BRPrescriptionChargeView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPrescriptionChargeView.h; sourceTree = "<group>"; };
		0AF2032B1F8CBF8800432DD6 /* BRPrescriptionChargeView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPrescriptionChargeView.m; sourceTree = "<group>"; };
		0AF2032D1F8CD48C00432DD6 /* BRPresOfflineView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRPresOfflineView.h; sourceTree = "<group>"; };
		0AF2032E1F8CD48C00432DD6 /* BRPresOfflineView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRPresOfflineView.m; sourceTree = "<group>"; };
		0F41901E204FEC73005B81D3 /* BRGuidePageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRGuidePageView.h; sourceTree = "<group>"; };
		0F41901F204FEC73005B81D3 /* BRGuidePageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRGuidePageView.m; sourceTree = "<group>"; };
		0F43AEEF20FF352500D568B5 /* PhotoPresWaitCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PhotoPresWaitCell.h; sourceTree = "<group>"; };
		0F43AEF020FF352500D568B5 /* PhotoPresWaitCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PhotoPresWaitCell.m; sourceTree = "<group>"; };
		0F57A577204D27FA002914A9 /* BRPrescriptionToolBar.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPrescriptionToolBar.h; sourceTree = "<group>"; };
		0F57A578204D27FA002914A9 /* BRPrescriptionToolBar.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPrescriptionToolBar.m; sourceTree = "<group>"; };
		0F9F54A120941EA600F2741C /* BRTemporaryPrescription.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRTemporaryPrescription.h; sourceTree = "<group>"; };
		0F9F54A220941EA600F2741C /* BRTemporaryPrescription.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = BRTemporaryPrescription.mm; sourceTree = "<group>"; };
		0FB534A920F488B200E084B7 /* BRCanTapImgView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRCanTapImgView.h; sourceTree = "<group>"; };
		0FB534AA20F488B200E084B7 /* BRCanTapImgView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRCanTapImgView.m; sourceTree = "<group>"; };
		0FB534AC20F49B2B00E084B7 /* PhotoPresViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PhotoPresViewController.h; sourceTree = "<group>"; };
		0FB534AD20F49B2B00E084B7 /* PhotoPresViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PhotoPresViewController.m; sourceTree = "<group>"; };
		0FB534B020F4A40100E084B7 /* PhotoPresCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PhotoPresCell.h; sourceTree = "<group>"; };
		0FB534B120F4A40100E084B7 /* PhotoPresCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PhotoPresCell.m; sourceTree = "<group>"; };
		0FB534B320F4B14900E084B7 /* PhotoPresModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PhotoPresModel.h; sourceTree = "<group>"; };
		0FB534B420F4B14900E084B7 /* PhotoPresModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PhotoPresModel.m; sourceTree = "<group>"; };
		0FB534B620F4B61600E084B7 /* PhotoTableView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PhotoTableView.h; sourceTree = "<group>"; };
		0FB534B720F4B61600E084B7 /* PhotoTableView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PhotoTableView.m; sourceTree = "<group>"; };
		0FB534B920F4D22100E084B7 /* PhotoPresDetailViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PhotoPresDetailViewController.h; sourceTree = "<group>"; };
		0FB534BA20F4D22100E084B7 /* PhotoPresDetailViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PhotoPresDetailViewController.m; sourceTree = "<group>"; };
		0FB534BF20F5A2FD00E084B7 /* BRSegmentView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSegmentView.h; sourceTree = "<group>"; };
		0FB534C020F5A2FD00E084B7 /* BRSegmentView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSegmentView.m; sourceTree = "<group>"; };
		770044462C8EE94700236FF1 /* FloatingButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FloatingButton.h; sourceTree = "<group>"; };
		770044472C8EE94700236FF1 /* FloatingButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FloatingButton.m; sourceTree = "<group>"; };
		7700F34F1FCBE4920059BA14 /* MedicatedInfoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MedicatedInfoViewController.h; sourceTree = "<group>"; };
		7700F3501FCBE4920059BA14 /* MedicatedInfoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MedicatedInfoViewController.m; sourceTree = "<group>"; };
		770551681FDBBBBD000306B1 /* BRBinDingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRBinDingViewController.h; sourceTree = "<group>"; };
		770551691FDBBBBD000306B1 /* BRBinDingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRBinDingViewController.m; sourceTree = "<group>"; };
		7705516B1FDBC99D000306B1 /* BRBankCardListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRBankCardListViewController.h; sourceTree = "<group>"; };
		7705516C1FDBC99D000306B1 /* BRBankCardListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRBankCardListViewController.m; sourceTree = "<group>"; };
		770605831FDBBD510091FA60 /* SessionXiaoRanViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SessionXiaoRanViewController.h; sourceTree = "<group>"; };
		770605841FDBBD510091FA60 /* SessionXiaoRanViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SessionXiaoRanViewController.m; sourceTree = "<group>"; };
		770605861FDBEAC60091FA60 /* FileManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FileManager.h; sourceTree = "<group>"; };
		770605871FDBEAC60091FA60 /* FileManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FileManager.m; sourceTree = "<group>"; };
		7706FF351F6A1C2C00EBB35C /* BRChatInputMoreContainerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRChatInputMoreContainerView.h; sourceTree = "<group>"; };
		7706FF361F6A1C2C00EBB35C /* BRChatInputMoreContainerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRChatInputMoreContainerView.m; sourceTree = "<group>"; };
		77089A6E2CF098C100C9E6C3 /* tiny_opencv2.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = tiny_opencv2.framework; path = ../../Downloads/OCR_iOS_SDK_v1.1.0.17/framework/tiny_opencv2.framework; sourceTree = "<group>"; };
		77089A6F2CF098C100C9E6C3 /* YTImageRefiner.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = YTImageRefiner.framework; path = ../../Downloads/OCR_iOS_SDK_v1.1.0.17/framework/YTImageRefiner.framework; sourceTree = "<group>"; };
		77089A702CF098C100C9E6C3 /* tnn.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = tnn.framework; path = ../../Downloads/OCR_iOS_SDK_v1.1.0.17/framework/tnn.framework; sourceTree = "<group>"; };
		77089A712CF098C100C9E6C3 /* OcrSDKKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OcrSDKKit.framework; path = ../../Downloads/OCR_iOS_SDK_v1.1.0.17/framework/OcrSDKKit.framework; sourceTree = "<group>"; };
		77089A7B2CF098E700C9E6C3 /* OcrSDK.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; name = OcrSDK.bundle; path = ../../Downloads/OCR_iOS_SDK_v1.1.0.17/bundle/OcrSDK.bundle; sourceTree = "<group>"; };
		77089A7F2CF0991C00C9E6C3 /* OcrSDK.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = OcrSDK.bundle; sourceTree = "<group>"; };
		77089A812CF0994D00C9E6C3 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		77089A832CF0995A00C9E6C3 /* Photos.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Photos.framework; path = System/Library/Frameworks/Photos.framework; sourceTree = SDKROOT; };
		77089A852CF0996200C9E6C3 /* PhotosUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = PhotosUI.framework; path = System/Library/Frameworks/PhotosUI.framework; sourceTree = SDKROOT; };
		77089A872CF0996900C9E6C3 /* CoreML.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreML.framework; path = System/Library/Frameworks/CoreML.framework; sourceTree = SDKROOT; };
		77089A892CF09AC400C9E6C3 /* OcrSDKKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = OcrSDKKit.framework; sourceTree = "<group>"; };
		77089A8A2CF09AC400C9E6C3 /* tiny_opencv2.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = tiny_opencv2.framework; sourceTree = "<group>"; };
		77089A8B2CF09AC400C9E6C3 /* tnn.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = tnn.framework; sourceTree = "<group>"; };
		77089A8C2CF09AC400C9E6C3 /* YTImageRefiner.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTImageRefiner.framework; sourceTree = "<group>"; };
		770B62EE1FCD565500F6380C /* ConfigInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ConfigInfo.h; sourceTree = "<group>"; };
		770B62EF1FCD565500F6380C /* ConfigInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ConfigInfo.m; sourceTree = "<group>"; };
		770CBCE31FDFD02400C27AB3 /* QuestionTypeCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuestionTypeCell.h; sourceTree = "<group>"; };
		770CBCE41FDFD02400C27AB3 /* QuestionTypeCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuestionTypeCell.m; sourceTree = "<group>"; };
		770CBCE61FDFD04100C27AB3 /* QuestionListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuestionListCell.h; sourceTree = "<group>"; };
		770CBCE71FDFD04100C27AB3 /* QuestionListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuestionListCell.m; sourceTree = "<group>"; };
		770CBCE91FDFDC2300C27AB3 /* FrequentQuestionShowView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FrequentQuestionShowView.h; sourceTree = "<group>"; };
		770CBCEA1FDFDC2300C27AB3 /* FrequentQuestionShowView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FrequentQuestionShowView.m; sourceTree = "<group>"; };
		770D3E18200753CF00D48C5A /* BRShareView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRShareView.h; sourceTree = "<group>"; };
		770D3E19200753CF00D48C5A /* BRShareView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRShareView.m; sourceTree = "<group>"; };
		770FE95A1FF33D1800008C78 /* ShowMyPurseSMSCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShowMyPurseSMSCode.h; sourceTree = "<group>"; };
		770FE95B1FF33D1800008C78 /* ShowMyPurseSMSCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShowMyPurseSMSCode.m; sourceTree = "<group>"; };
		7710B0A31FA09AED00C9CBE5 /* interf_dec.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = interf_dec.h; sourceTree = "<group>"; };
		7710B0A41FA09AED00C9CBE5 /* interf_enc.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = interf_enc.h; sourceTree = "<group>"; };
		7710B0A51FA09AF700C9CBE5 /* if_rom.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = if_rom.h; sourceTree = "<group>"; };
		7710B0A61FA09AF700C9CBE5 /* dec_if.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = dec_if.h; sourceTree = "<group>"; };
		7710B0A71FA09B3100C9CBE5 /* libopencore-amrnb.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = "libopencore-amrnb.a"; sourceTree = "<group>"; };
		7710B0A81FA09B3100C9CBE5 /* libopencore-amrwb.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = "libopencore-amrwb.a"; sourceTree = "<group>"; };
		77110C8C2E069F8000F002FC /* BRWithdrawPasswordView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRWithdrawPasswordView.h; sourceTree = "<group>"; };
		77110C8D2E069F8000F002FC /* BRWithdrawPasswordView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRWithdrawPasswordView.m; sourceTree = "<group>"; };
		771138D91FC7B4920084D9CB /* BRAlertView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRAlertView.h; sourceTree = "<group>"; };
		771138DA1FC7B4920084D9CB /* BRAlertView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRAlertView.m; sourceTree = "<group>"; };
		7712BE101FD919E00064B0F6 /* BRWzdAnswerMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRWzdAnswerMessageCell.h; sourceTree = "<group>"; };
		7712BE111FD919E00064B0F6 /* BRWzdAnswerMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRWzdAnswerMessageCell.m; sourceTree = "<group>"; };
		7712BE131FD91A2A0064B0F6 /* BRWzdAnswerMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRWzdAnswerMessageCellLayout.h; sourceTree = "<group>"; };
		7712BE141FD91A2A0064B0F6 /* BRWzdAnswerMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRWzdAnswerMessageCellLayout.m; sourceTree = "<group>"; };
		7712BE161FD91FC60064B0F6 /* BRWzdQuestionMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRWzdQuestionMessageCell.h; sourceTree = "<group>"; };
		7712BE171FD91FC60064B0F6 /* BRWzdQuestionMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRWzdQuestionMessageCell.m; sourceTree = "<group>"; };
		7712BE191FD91FEA0064B0F6 /* BRWzdQuestionMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRWzdQuestionMessageCellLayout.h; sourceTree = "<group>"; };
		7712BE1A1FD91FEA0064B0F6 /* BRWzdQuestionMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRWzdQuestionMessageCellLayout.m; sourceTree = "<group>"; };
		7712BE1C1FD920130064B0F6 /* BRFzdAnswerMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRFzdAnswerMessageCell.h; sourceTree = "<group>"; };
		7712BE1D1FD920130064B0F6 /* BRFzdAnswerMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRFzdAnswerMessageCell.m; sourceTree = "<group>"; };
		7712BE1F1FD920330064B0F6 /* BRFzdQuestionMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRFzdQuestionMessageCell.h; sourceTree = "<group>"; };
		7712BE201FD920330064B0F6 /* BRFzdQuestionMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRFzdQuestionMessageCell.m; sourceTree = "<group>"; };
		7712BE221FD9205C0064B0F6 /* BRFzdQuestionMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRFzdQuestionMessageCellLayout.h; sourceTree = "<group>"; };
		7712BE231FD9205C0064B0F6 /* BRFzdQuestionMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRFzdQuestionMessageCellLayout.m; sourceTree = "<group>"; };
		7712BE251FD920720064B0F6 /* BRFzdAnswerMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRFzdAnswerMessageCellLayout.h; sourceTree = "<group>"; };
		7712BE261FD920720064B0F6 /* BRFzdAnswerMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRFzdAnswerMessageCellLayout.m; sourceTree = "<group>"; };
		7712BE2A1FD927B10064B0F6 /* BRSupplementQuestionMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSupplementQuestionMessageCell.h; sourceTree = "<group>"; };
		7712BE2B1FD927B10064B0F6 /* BRSupplementQuestionMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSupplementQuestionMessageCell.m; sourceTree = "<group>"; };
		7712BE2D1FD927CD0064B0F6 /* BRSupplementAnswerMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSupplementAnswerMessageCellLayout.h; sourceTree = "<group>"; };
		7712BE2E1FD927CD0064B0F6 /* BRSupplementAnswerMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSupplementAnswerMessageCellLayout.m; sourceTree = "<group>"; };
		7712BE301FD927ED0064B0F6 /* BRSupplementQuestionMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSupplementQuestionMessageCellLayout.h; sourceTree = "<group>"; };
		7712BE311FD927ED0064B0F6 /* BRSupplementQuestionMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSupplementQuestionMessageCellLayout.m; sourceTree = "<group>"; };
		7712BE331FD928080064B0F6 /* BRSupplementAnswerMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSupplementAnswerMessageCell.h; sourceTree = "<group>"; };
		7712BE341FD928080064B0F6 /* BRSupplementAnswerMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSupplementAnswerMessageCell.m; sourceTree = "<group>"; };
		77131E9C1F4D7503006C9F79 /* NSDateFormatter+Singleton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDateFormatter+Singleton.h"; sourceTree = "<group>"; };
		77131E9D1F4D7503006C9F79 /* NSDateFormatter+Singleton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDateFormatter+Singleton.m"; sourceTree = "<group>"; };
		77131EA21F4D758D006C9F79 /* NSDate+Message.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDate+Message.h"; sourceTree = "<group>"; };
		77131EA31F4D758D006C9F79 /* NSDate+Message.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDate+Message.m"; sourceTree = "<group>"; };
		77131EA51F4D76D2006C9F79 /* UINavigationBar+BackgroundColor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UINavigationBar+BackgroundColor.h"; sourceTree = "<group>"; };
		77131EA61F4D76D2006C9F79 /* UINavigationBar+BackgroundColor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UINavigationBar+BackgroundColor.m"; sourceTree = "<group>"; };
		77131EA91F4D7D6B006C9F79 /* Reachability.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Reachability.h; sourceTree = "<group>"; };
		77131EAA1F4D7D6B006C9F79 /* Reachability.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Reachability.m; sourceTree = "<group>"; };
		771608C31F66371A00D3A03C /* IMChatItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMChatItem.h; sourceTree = "<group>"; };
		771608C41F66375100D3A03C /* IMChatItemCellLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMChatItemCellLayout.h; sourceTree = "<group>"; };
		771736C41FA9689500ADE932 /* BRMessageModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRMessageModel.h; sourceTree = "<group>"; };
		771736C51FA9689500ADE932 /* BRMessageModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRMessageModel.m; sourceTree = "<group>"; };
		771736C71FA968A200ADE932 /* BRMessageContentModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRMessageContentModel.h; sourceTree = "<group>"; };
		771736C81FA968A200ADE932 /* BRMessageContentModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRMessageContentModel.m; sourceTree = "<group>"; };
		771736CA1FA96A7E00ADE932 /* BRMessagesModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRMessagesModel.h; sourceTree = "<group>"; };
		771736CB1FA96A7E00ADE932 /* BRMessagesModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRMessagesModel.m; sourceTree = "<group>"; };
		7718689620AD326500B62997 /* BRBillDetailsAccountOfMoneyCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRBillDetailsAccountOfMoneyCell.h; sourceTree = "<group>"; };
		7718689720AD326500B62997 /* BRBillDetailsAccountOfMoneyCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRBillDetailsAccountOfMoneyCell.m; sourceTree = "<group>"; };
		771911A91FE21A6000070347 /* BRBaseTextLinePositionModifier.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRBaseTextLinePositionModifier.h; sourceTree = "<group>"; };
		771911AA1FE21A6000070347 /* BRBaseTextLinePositionModifier.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRBaseTextLinePositionModifier.m; sourceTree = "<group>"; };
		7719417C1F7B402D00E11088 /* UIFont+Util.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIFont+Util.h"; sourceTree = "<group>"; };
		7719417D1F7B402D00E11088 /* UIFont+Util.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIFont+Util.m"; sourceTree = "<group>"; };
		77194D951FD0026800716835 /* BRDHTextField.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRDHTextField.h; sourceTree = "<group>"; };
		77194D961FD0026800716835 /* BRDHTextField.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRDHTextField.m; sourceTree = "<group>"; };
		7719B97120206233005504C6 /* UIImage+BR.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIImage+BR.h"; sourceTree = "<group>"; };
		7719B97220206233005504C6 /* UIImage+BR.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIImage+BR.m"; sourceTree = "<group>"; };
		771C15CA1F4D2B4100099626 /* UserManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserManager.h; sourceTree = "<group>"; };
		771C15CB1F4D2B4100099626 /* UserManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserManager.m; sourceTree = "<group>"; };
		771C15CD1F4D709200099626 /* UIView+Util.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Util.h"; sourceTree = "<group>"; };
		771C15CE1F4D709200099626 /* UIView+Util.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+Util.m"; sourceTree = "<group>"; };
		771D93A82056292500A59DD2 /* PrescriptionModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PrescriptionModel.h; sourceTree = "<group>"; };
		771D93A92056292500A59DD2 /* PrescriptionModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PrescriptionModel.m; sourceTree = "<group>"; };
		771D93AB205667E600A59DD2 /* ClassicPrescriptionCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ClassicPrescriptionCell.h; sourceTree = "<group>"; };
		771D93AC205667E600A59DD2 /* ClassicPrescriptionCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ClassicPrescriptionCell.m; sourceTree = "<group>"; };
		771E52092134E9DE006B6ACB /* DrugStoreModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DrugStoreModel.h; sourceTree = "<group>"; };
		771E520A2134E9DE006B6ACB /* DrugStoreModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DrugStoreModel.m; sourceTree = "<group>"; };
		771E520C2134ED30006B6ACB /* DrugStoreListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DrugStoreListModel.h; sourceTree = "<group>"; };
		771E520D2134ED30006B6ACB /* DrugStoreListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DrugStoreListModel.m; sourceTree = "<group>"; };
		771F66ED1FC8090F002AAA5C /* BRPatientDocumentTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPatientDocumentTableViewCell.h; sourceTree = "<group>"; };
		771F66EE1FC8090F002AAA5C /* BRPatientDocumentTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPatientDocumentTableViewCell.m; sourceTree = "<group>"; };
		771F66EF1FC8090F002AAA5C /* BRPatientDocumentTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BRPatientDocumentTableViewCell.xib; sourceTree = "<group>"; };
		7720F1941FE8F626009DC35C /* BRSessionListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSessionListModel.h; sourceTree = "<group>"; };
		7720F1951FE8F626009DC35C /* BRSessionListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSessionListModel.m; sourceTree = "<group>"; };
		7720F1971FE8F658009DC35C /* BRSessionRosterUserModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSessionRosterUserModel.h; sourceTree = "<group>"; };
		7720F1981FE8F658009DC35C /* BRSessionRosterUserModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSessionRosterUserModel.m; sourceTree = "<group>"; };
		7720F19A1FE8F9B2009DC35C /* BRSessionListItemModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSessionListItemModel.h; sourceTree = "<group>"; };
		7720F19B1FE8F9B2009DC35C /* BRSessionListItemModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSessionListItemModel.m; sourceTree = "<group>"; };
		7721B3E81FC422E7000A6DFA /* BRWzdBaseMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRWzdBaseMessageCell.h; sourceTree = "<group>"; };
		7721B3E91FC422E7000A6DFA /* BRWzdBaseMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRWzdBaseMessageCell.m; sourceTree = "<group>"; };
		7721B3EB1FC42310000A6DFA /* BRWzdBaseMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRWzdBaseMessageCellLayout.h; sourceTree = "<group>"; };
		7721B3EC1FC42310000A6DFA /* BRWzdBaseMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRWzdBaseMessageCellLayout.m; sourceTree = "<group>"; };
		7721B3F31FC43299000A6DFA /* BRActionSheetView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRActionSheetView.h; sourceTree = "<group>"; };
		7721B3F41FC43299000A6DFA /* BRActionSheetView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRActionSheetView.m; sourceTree = "<group>"; };
		772400421F56549C0015E0E3 /* BRModuleManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = BRModuleManager.h; path = ../../AppDelegate/BRModuleManager.h; sourceTree = "<group>"; };
		772400431F56549C0015E0E3 /* BRModuleManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = BRModuleManager.m; path = ../../AppDelegate/BRModuleManager.m; sourceTree = "<group>"; };
		7724004B1F5658B10015E0E3 /* BRThirdPartModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRThirdPartModule.h; sourceTree = "<group>"; };
		7724004C1F5658B10015E0E3 /* BRThirdPartModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRThirdPartModule.m; sourceTree = "<group>"; };
		7724004E1F5659600015E0E3 /* ModulesRegister.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = ModulesRegister.plist; sourceTree = "<group>"; };
		772400511F5665850015E0E3 /* BRAppStartModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRAppStartModule.h; sourceTree = "<group>"; };
		772400521F5665850015E0E3 /* BRAppStartModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRAppStartModule.m; sourceTree = "<group>"; };
		7724007D1F56D9DA0015E0E3 /* PanelIconModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PanelIconModel.h; sourceTree = "<group>"; };
		7724007E1F56D9DA0015E0E3 /* PanelIconModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PanelIconModel.m; sourceTree = "<group>"; };
		7724EE681FBE8DE100309E92 /* UINavigationBar+Addition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UINavigationBar+Addition.h"; sourceTree = "<group>"; };
		7724EE691FBE8DE100309E92 /* UINavigationBar+Addition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UINavigationBar+Addition.m"; sourceTree = "<group>"; };
		77264C941F58F647002C9CBD /* MyQRCodeViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MyQRCodeViewController.h; sourceTree = "<group>"; };
		77264C951F58F647002C9CBD /* MyQRCodeViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MyQRCodeViewController.m; sourceTree = "<group>"; };
		77264C971F590452002C9CBD /* BaseViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BaseViewController.h; sourceTree = "<group>"; };
		77264C981F590452002C9CBD /* BaseViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BaseViewController.m; sourceTree = "<group>"; };
		77264DF2212D3BFC002EE6C9 /* DrugStoreListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DrugStoreListCell.h; sourceTree = "<group>"; };
		77264DF3212D3BFC002EE6C9 /* DrugStoreListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DrugStoreListCell.m; sourceTree = "<group>"; };
		77264DF5212D5444002EE6C9 /* DrugStoreListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DrugStoreListViewController.h; sourceTree = "<group>"; };
		77264DF6212D5444002EE6C9 /* DrugStoreListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DrugStoreListViewController.m; sourceTree = "<group>"; };
		7726F6011F958FE000D2E07B /* BRImageMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRImageMessageCell.h; sourceTree = "<group>"; };
		7726F6021F958FE000D2E07B /* BRImageMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRImageMessageCell.m; sourceTree = "<group>"; };
		7726F6041F95902100D2E07B /* BRImageMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRImageMessageCellLayout.h; sourceTree = "<group>"; };
		7726F6051F95902100D2E07B /* BRImageMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRImageMessageCellLayout.m; sourceTree = "<group>"; };
		77273AEC2CF70E1C0018A9CD /* EnvironmentConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EnvironmentConfig.h; sourceTree = "<group>"; };
		77273AED2CF70E1C0018A9CD /* EnvironmentConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EnvironmentConfig.m; sourceTree = "<group>"; };
		77275FE21F516918000A76BA /* BRZY.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = BRZY.entitlements; sourceTree = "<group>"; };
		77286C832057755E00117038 /* ClassicPrescriptionSearchViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ClassicPrescriptionSearchViewController.h; sourceTree = "<group>"; };
		77286C842057755E00117038 /* ClassicPrescriptionSearchViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ClassicPrescriptionSearchViewController.m; sourceTree = "<group>"; };
		77286C862057CAB700117038 /* ClassicSearchModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ClassicSearchModel.h; sourceTree = "<group>"; };
		77286C872057CAB700117038 /* ClassicSearchModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ClassicSearchModel.m; sourceTree = "<group>"; };
		77286C892057D77B00117038 /* ClassicPrescriptionListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ClassicPrescriptionListViewController.h; sourceTree = "<group>"; };
		77286C8A2057D77B00117038 /* ClassicPrescriptionListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ClassicPrescriptionListViewController.m; sourceTree = "<group>"; };
		772901941F5D385A008C51F6 /* UIView+BR.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+BR.h"; sourceTree = "<group>"; };
		772901951F5D385A008C51F6 /* UIView+BR.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+BR.m"; sourceTree = "<group>"; };
		772901971F5D3C96008C51F6 /* BRBadgeView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRBadgeView.h; sourceTree = "<group>"; };
		772901981F5D3C96008C51F6 /* BRBadgeView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRBadgeView.m; sourceTree = "<group>"; };
		7729019A1F5D4F4E008C51F6 /* IMSessionManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMSessionManager.h; sourceTree = "<group>"; };
		7729019B1F5D4F4E008C51F6 /* IMSessionManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMSessionManager.m; sourceTree = "<group>"; };
		77293F9D1FC9659F00329DC2 /* RRFPSBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RRFPSBar.h; sourceTree = "<group>"; };
		77293F9E1FC9659F00329DC2 /* RRFPSBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RRFPSBar.m; sourceTree = "<group>"; };
		772A7D041FA1903C001AE0BB /* BRAudioMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRAudioMessageCell.h; sourceTree = "<group>"; };
		772A7D051FA1903C001AE0BB /* BRAudioMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRAudioMessageCell.m; sourceTree = "<group>"; };
		772A7D071FA19066001AE0BB /* BRAudioMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRAudioMessageCellLayout.h; sourceTree = "<group>"; };
		772A7D081FA19066001AE0BB /* BRAudioMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRAudioMessageCellLayout.m; sourceTree = "<group>"; };
		772B10C51FA85F1A00D894EA /* IMUIHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IMUIHelper.h; sourceTree = "<group>"; };
		772B10C61FA85F1A00D894EA /* IMUIHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IMUIHelper.m; sourceTree = "<group>"; };
		772B59B91FE7DA84009EA260 /* BRRevokeMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRRevokeMessageCell.h; sourceTree = "<group>"; };
		772B59BA1FE7DA84009EA260 /* BRRevokeMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRRevokeMessageCell.m; sourceTree = "<group>"; };
		772B59BC1FE7DACC009EA260 /* BRRevokeMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRRevokeMessageCellLayout.h; sourceTree = "<group>"; };
		772B59BD1FE7DACC009EA260 /* BRRevokeMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRRevokeMessageCellLayout.m; sourceTree = "<group>"; };
		772DFD421F55589600350AED /* enumList.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = enumList.h; sourceTree = "<group>"; };
		772E57F01FF1E319008252E0 /* BRTablePharmacopeia.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRTablePharmacopeia.h; sourceTree = "<group>"; };
		772E57F11FF1E319008252E0 /* BRTablePharmacopeia.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = BRTablePharmacopeia.mm; sourceTree = "<group>"; };
		772F70D5205139FE004BFE4D /* BRStockInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRStockInfoModel.h; sourceTree = "<group>"; };
		772F70D6205139FE004BFE4D /* BRStockInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRStockInfoModel.m; sourceTree = "<group>"; };
		7731F8852E42CA5400A7CC99 /* mapping.txt */ = {isa = PBXFileReference; lastKnownFileType = text; path = mapping.txt; sourceTree = "<group>"; };
		77327D711FC269B90023939F /* BRSessionStartMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSessionStartMessageCellLayout.h; sourceTree = "<group>"; };
		77327D721FC269B90023939F /* BRSessionStartMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSessionStartMessageCellLayout.m; sourceTree = "<group>"; };
		77327D741FC269F50023939F /* BRSessionStartMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSessionStartMessageCell.h; sourceTree = "<group>"; };
		77327D751FC269F50023939F /* BRSessionStartMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSessionStartMessageCell.m; sourceTree = "<group>"; };
		77327D771FC2757C0023939F /* BRSessionEndMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSessionEndMessageCell.h; sourceTree = "<group>"; };
		77327D781FC2757C0023939F /* BRSessionEndMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSessionEndMessageCell.m; sourceTree = "<group>"; };
		77327D7A1FC275A00023939F /* BRSessionEndMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSessionEndMessageCellLayout.h; sourceTree = "<group>"; };
		77327D7B1FC275A00023939F /* BRSessionEndMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSessionEndMessageCellLayout.m; sourceTree = "<group>"; };
		77327D7E1FC2830F0023939F /* BRCustomSystemMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRCustomSystemMessageCell.h; sourceTree = "<group>"; };
		77327D7F1FC2830F0023939F /* BRCustomSystemMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRCustomSystemMessageCell.m; sourceTree = "<group>"; };
		77327D811FC283380023939F /* BRCustomSystemMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRCustomSystemMessageCellLayout.h; sourceTree = "<group>"; };
		77327D821FC283380023939F /* BRCustomSystemMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRCustomSystemMessageCellLayout.m; sourceTree = "<group>"; };
		7733342D1FA2FE6F00F996A0 /* BRPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPlayer.h; sourceTree = "<group>"; };
		7733342E1FA2FE6F00F996A0 /* BRPlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPlayer.m; sourceTree = "<group>"; };
		7733F25D1FCE51430085B43F /* BRComView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRComView.h; sourceTree = "<group>"; };
		7733F25E1FCE51430085B43F /* BRComView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRComView.m; sourceTree = "<group>"; };
		7733F2601FCE51530085B43F /* BRHistoryView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRHistoryView.h; sourceTree = "<group>"; };
		7733F2611FCE51530085B43F /* BRHistoryView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRHistoryView.m; sourceTree = "<group>"; };
		7733F2661FCEC9890085B43F /* BRHistoryTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRHistoryTableViewCell.h; sourceTree = "<group>"; };
		7733F2671FCEC9890085B43F /* BRHistoryTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRHistoryTableViewCell.m; sourceTree = "<group>"; };
		7733F2681FCEC9890085B43F /* BRHistoryTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = BRHistoryTableViewCell.xib; sourceTree = "<group>"; };
		7734EF0C2CDC95110037B345 /* QuickPrescribeSessionViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuickPrescribeSessionViewController.h; sourceTree = "<group>"; };
		7734EF0D2CDC95110037B345 /* QuickPrescribeSessionViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuickPrescribeSessionViewController.m; sourceTree = "<group>"; };
		77351B1520037DF500E266F7 /* DataInitLoadingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DataInitLoadingViewController.h; sourceTree = "<group>"; };
		77351B1620037DF500E266F7 /* DataInitLoadingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DataInitLoadingViewController.m; sourceTree = "<group>"; };
		7735EF301F62A713003CE02D /* ChatViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ChatViewController.h; sourceTree = "<group>"; };
		7735EF311F62A713003CE02D /* ChatViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ChatViewController.m; sourceTree = "<group>"; };
		7735EF331F62A720003CE02D /* SessionViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SessionViewController.h; sourceTree = "<group>"; };
		7735EF341F62A720003CE02D /* SessionViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SessionViewController.m; sourceTree = "<group>"; };
		7735EF361F62A9D4003CE02D /* PatientDocumentViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PatientDocumentViewController.h; sourceTree = "<group>"; };
		7735EF371F62A9D4003CE02D /* PatientDocumentViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PatientDocumentViewController.m; sourceTree = "<group>"; };
		7735EF421F62BFEF003CE02D /* InvitePatientViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InvitePatientViewController.h; sourceTree = "<group>"; };
		7735EF431F62BFEF003CE02D /* InvitePatientViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InvitePatientViewController.m; sourceTree = "<group>"; };
		7735EF451F62C00C003CE02D /* InviteDoctorViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InviteDoctorViewController.h; sourceTree = "<group>"; };
		7735EF461F62C00C003CE02D /* InviteDoctorViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InviteDoctorViewController.m; sourceTree = "<group>"; };
		7736B9421FE100F700C55A50 /* QuestionAddOrEditListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuestionAddOrEditListCell.h; sourceTree = "<group>"; };
		7736B9431FE100F700C55A50 /* QuestionAddOrEditListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuestionAddOrEditListCell.m; sourceTree = "<group>"; };
		7736B9451FE1257C00C55A50 /* QuestionItemModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuestionItemModel.h; sourceTree = "<group>"; };
		7736B9461FE1257C00C55A50 /* QuestionItemModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuestionItemModel.m; sourceTree = "<group>"; };
		7736B9481FE1260700C55A50 /* QuestionInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuestionInfoModel.h; sourceTree = "<group>"; };
		7736B9491FE1260700C55A50 /* QuestionInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuestionInfoModel.m; sourceTree = "<group>"; };
		7736B94B1FE144D700C55A50 /* FrequentlyQuestionContentAddViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FrequentlyQuestionContentAddViewController.h; sourceTree = "<group>"; };
		7736B94C1FE144D700C55A50 /* FrequentlyQuestionContentAddViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FrequentlyQuestionContentAddViewController.m; sourceTree = "<group>"; };
		77399A0B20060B8F00CA2277 /* BRDataEmptyView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRDataEmptyView.h; sourceTree = "<group>"; };
		77399A0C20060B8F00CA2277 /* BRDataEmptyView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRDataEmptyView.m; sourceTree = "<group>"; };
		773D1D092094218C000467A6 /* BRTemporaryPrescription+WCTTableCoding.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "BRTemporaryPrescription+WCTTableCoding.h"; sourceTree = "<group>"; };
		773D913D2CF5D63D00547A91 /* BRTencentCloudAPISignature.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRTencentCloudAPISignature.h; sourceTree = "<group>"; };
		773D913E2CF5D63D00547A91 /* BRTencentCloudAPISignature.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRTencentCloudAPISignature.m; sourceTree = "<group>"; };
		773D91402CF5D8B100547A91 /* BRTencentOCRRequest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRTencentOCRRequest.h; sourceTree = "<group>"; };
		773D91412CF5D8B100547A91 /* BRTencentOCRRequest.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRTencentOCRRequest.m; sourceTree = "<group>"; };
		773D91452CF5D99200547A91 /* OCRPoint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OCRPoint.h; sourceTree = "<group>"; };
		773D91462CF5D99200547A91 /* OCRPoint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OCRPoint.m; sourceTree = "<group>"; };
		773D91482CF5DA1400547A91 /* OCRWordPolygon.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OCRWordPolygon.h; sourceTree = "<group>"; };
		773D91492CF5DA1400547A91 /* OCRWordPolygon.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OCRWordPolygon.m; sourceTree = "<group>"; };
		773D914B2CF5DA3300547A91 /* OCRTextDetection.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OCRTextDetection.h; sourceTree = "<group>"; };
		773D914C2CF5DA3300547A91 /* OCRTextDetection.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OCRTextDetection.m; sourceTree = "<group>"; };
		773D914E2CF5DA6500547A91 /* OCRResponse.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OCRResponse.h; sourceTree = "<group>"; };
		773D914F2CF5DA6500547A91 /* OCRResponse.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OCRResponse.m; sourceTree = "<group>"; };
		773E8AF62CD9E62B0096F0A5 /* BRAdjustByMultipleContentView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRAdjustByMultipleContentView.h; sourceTree = "<group>"; };
		773E8AF72CD9E62B0096F0A5 /* BRAdjustByMultipleContentView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRAdjustByMultipleContentView.m; sourceTree = "<group>"; };
		774162D8279EE2E50028FC2C /* BRWechatBindResultModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRWechatBindResultModel.h; sourceTree = "<group>"; };
		774162D9279EE2E50028FC2C /* BRWechatBindResultModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRWechatBindResultModel.m; sourceTree = "<group>"; };
		774162DB279EE7300028FC2C /* BRWithdrawInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRWithdrawInfoModel.h; sourceTree = "<group>"; };
		774162DC279EE7300028FC2C /* BRWithdrawInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRWithdrawInfoModel.m; sourceTree = "<group>"; };
		7742ECAA1F4BD7ED00A9B110 /* BRZY.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = BRZY.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7742ECAE1F4BD7ED00A9B110 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		7742ECB01F4BD7ED00A9B110 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = BRZY/AppDelegate.h; sourceTree = SOURCE_ROOT; };
		7742ECB11F4BD7ED00A9B110 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = AppDelegate.m; path = BRZY/AppDelegate.m; sourceTree = SOURCE_ROOT; };
		7742ECB91F4BD7ED00A9B110 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		7742ECBE1F4BD7ED00A9B110 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		7742ECD61F4C089600A9B110 /* PrefixHeader.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PrefixHeader.pch; sourceTree = "<group>"; };
		7742ECE31F4C183B00A9B110 /* BaseTabBarController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BaseTabBarController.h; sourceTree = "<group>"; };
		7742ECE41F4C183B00A9B110 /* BaseTabBarController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BaseTabBarController.m; sourceTree = "<group>"; wrapsLines = 0; };
		7742ECED1F4C22EC00A9B110 /* Utils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Utils.h; sourceTree = "<group>"; };
		7742ECEE1F4C22EC00A9B110 /* Utils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Utils.m; sourceTree = "<group>"; };
		7742ECF01F4C234F00A9B110 /* ViewTools.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ViewTools.h; sourceTree = "<group>"; };
		7742ECF11F4C234F00A9B110 /* ViewTools.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ViewTools.m; sourceTree = "<group>"; };
		7742ECFE1F4C26E400A9B110 /* MessageListViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MessageListViewController.h; sourceTree = "<group>"; };
		7742ECFF1F4C26E400A9B110 /* MessageListViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MessageListViewController.m; sourceTree = "<group>"; };
		7742ED011F4C272200A9B110 /* PatientViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PatientViewController.h; sourceTree = "<group>"; };
		7742ED021F4C272200A9B110 /* PatientViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PatientViewController.m; sourceTree = "<group>"; };
		7742ED041F4C273200A9B110 /* InviteViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InviteViewController.h; sourceTree = "<group>"; };
		7742ED051F4C273200A9B110 /* InviteViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InviteViewController.m; sourceTree = "<group>"; };
		7742ED071F4C274F00A9B110 /* UserCenterViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserCenterViewController.h; sourceTree = "<group>"; };
		7742ED081F4C274F00A9B110 /* UserCenterViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserCenterViewController.m; sourceTree = "<group>"; };
		7742ED0A1F4C2A1E00A9B110 /* BaseNavigationController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BaseNavigationController.h; sourceTree = "<group>"; };
		7742ED0B1F4C2A1E00A9B110 /* BaseNavigationController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BaseNavigationController.m; sourceTree = "<group>"; };
		7742ED0D1F4C2F1600A9B110 /* AppMacro.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppMacro.h; sourceTree = "<group>"; };
		7742ED0E1F4C346800A9B110 /* UIColor+Util.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIColor+Util.h"; sourceTree = "<group>"; };
		7742ED0F1F4C346800A9B110 /* UIColor+Util.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIColor+Util.m"; sourceTree = "<group>"; };
		774494431FE0C2A900522A88 /* BRNoDataView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRNoDataView.h; sourceTree = "<group>"; };
		774494441FE0C2A900522A88 /* BRNoDataView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRNoDataView.m; sourceTree = "<group>"; };
		7744A61C1FEA059100AE7ABD /* PatientInfoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientInfoViewController.h; sourceTree = "<group>"; };
		7744A61D1FEA059100AE7ABD /* PatientInfoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientInfoViewController.m; sourceTree = "<group>"; };
		7744A61F1FEA068700AE7ABD /* LogisticInfoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LogisticInfoViewController.h; sourceTree = "<group>"; };
		7744A6201FEA068700AE7ABD /* LogisticInfoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LogisticInfoViewController.m; sourceTree = "<group>"; };
		7744A6221FEA07D000AE7ABD /* PatientInfoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientInfoView.h; sourceTree = "<group>"; };
		7744A6231FEA07D000AE7ABD /* PatientInfoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientInfoView.m; sourceTree = "<group>"; };
		7744A6261FEA3C8300AE7ABD /* PatientInfoListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientInfoListCell.h; sourceTree = "<group>"; };
		7744A6271FEA3C8300AE7ABD /* PatientInfoListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientInfoListCell.m; sourceTree = "<group>"; };
		774518181FDE5C6800DAB506 /* BRXiaoRanChatInputMoreContainerView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRXiaoRanChatInputMoreContainerView.h; sourceTree = "<group>"; };
		774518191FDE5C6800DAB506 /* BRXiaoRanChatInputMoreContainerView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRXiaoRanChatInputMoreContainerView.m; sourceTree = "<group>"; };
		77475AE11FF26D740066F5C8 /* HistorySelectPatientView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HistorySelectPatientView.h; sourceTree = "<group>"; };
		77475AE21FF26D740066F5C8 /* HistorySelectPatientView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HistorySelectPatientView.m; sourceTree = "<group>"; };
		77494F2828A71AEE0000132A /* BRQuickPrescribeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRQuickPrescribeViewController.h; sourceTree = "<group>"; };
		77494F2928A71AEE0000132A /* BRQuickPrescribeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRQuickPrescribeViewController.m; sourceTree = "<group>"; };
		77494F2F28A722FF0000132A /* BRWritePatientInfoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRWritePatientInfoView.h; sourceTree = "<group>"; };
		77494F3028A722FF0000132A /* BRWritePatientInfoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRWritePatientInfoView.m; sourceTree = "<group>"; };
		7749E19F1F7DE23B00E93881 /* ButtonInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ButtonInfoModel.h; sourceTree = "<group>"; };
		7749E1A01F7DE23B00E93881 /* ButtonInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ButtonInfoModel.m; sourceTree = "<group>"; };
		774AE86B1FD7FDCE009AFEF9 /* BRBankCardViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRBankCardViewController.h; sourceTree = "<group>"; };
		774AE86C1FD7FDCE009AFEF9 /* BRBankCardViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRBankCardViewController.m; sourceTree = "<group>"; };
		774C2DD71FEE2E03004CD7E2 /* PatientInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientInfoModel.h; sourceTree = "<group>"; };
		774C2DD81FEE2E03004CD7E2 /* PatientInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientInfoModel.m; sourceTree = "<group>"; };
		774C2DDA1FEE5E87004CD7E2 /* PatientModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientModel.h; sourceTree = "<group>"; };
		774C2DDB1FEE5E87004CD7E2 /* PatientModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientModel.m; sourceTree = "<group>"; };
		774DC662212EB17F008F4931 /* ShareContentView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ShareContentView.h; sourceTree = "<group>"; };
		774DC663212EB17F008F4931 /* ShareContentView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ShareContentView.m; sourceTree = "<group>"; };
		7751DD5C1F4EAF8800C10C3C /* LoginViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LoginViewController.h; sourceTree = "<group>"; };
		7751DD5D1F4EAF8800C10C3C /* LoginViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LoginViewController.m; sourceTree = "<group>"; };
		7751DD5F1F4EE06400C10C3C /* LoginInputView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LoginInputView.h; sourceTree = "<group>"; };
		7751DD601F4EE06400C10C3C /* LoginInputView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LoginInputView.m; sourceTree = "<group>"; };
		7752437D1FC977CB00EA2ED3 /* BRVisitsArrangementModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRVisitsArrangementModel.h; sourceTree = "<group>"; };
		7752437E1FC977CB00EA2ED3 /* BRVisitsArrangementModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRVisitsArrangementModel.m; sourceTree = "<group>"; };
		775243811FC978EE00EA2ED3 /* BRDHBaseModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRDHBaseModel.h; sourceTree = "<group>"; };
		775243821FC978EE00EA2ED3 /* BRDHBaseModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRDHBaseModel.m; sourceTree = "<group>"; };
		7755B71A20A5A49F00162617 /* MyPurseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyPurseViewController.h; sourceTree = "<group>"; };
		7755B71B20A5A49F00162617 /* MyPurseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyPurseViewController.m; sourceTree = "<group>"; };
		775643122DF7381900392EFE /* BRAuthConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRAuthConfig.h; sourceTree = "<group>"; };
		775643132DF7381900392EFE /* BRAuthConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRAuthConfig.m; sourceTree = "<group>"; };
		7756569A1F6282EC00F779C2 /* SessionSearchViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SessionSearchViewController.h; sourceTree = "<group>"; };
		7756569B1F6282EC00F779C2 /* SessionSearchViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SessionSearchViewController.m; sourceTree = "<group>"; };
		7756569D1F62849000F779C2 /* PatientSearchViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PatientSearchViewController.h; sourceTree = "<group>"; };
		7756569E1F62849000F779C2 /* PatientSearchViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PatientSearchViewController.m; sourceTree = "<group>"; };
		775656A31F628BF800F779C2 /* SessionListCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SessionListCell.h; sourceTree = "<group>"; };
		775656A41F628BF800F779C2 /* SessionListCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SessionListCell.m; sourceTree = "<group>"; };
		7757A81D1FE4BE6C00B065A5 /* SystemMessageViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SystemMessageViewController.h; sourceTree = "<group>"; };
		7757A81E1FE4BE6C00B065A5 /* SystemMessageViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SystemMessageViewController.m; sourceTree = "<group>"; };
		7757A8211FE4C04400B065A5 /* SystemMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SystemMessageCell.h; sourceTree = "<group>"; };
		7757A8221FE4C04400B065A5 /* SystemMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SystemMessageCell.m; sourceTree = "<group>"; };
		7757A8241FE4C11F00B065A5 /* SystemDateCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SystemDateCell.h; sourceTree = "<group>"; };
		7757A8251FE4C11F00B065A5 /* SystemDateCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SystemDateCell.m; sourceTree = "<group>"; };
		7757A8271FE50FDE00B065A5 /* MessageTransmitViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MessageTransmitViewController.h; sourceTree = "<group>"; };
		7757A8281FE50FDE00B065A5 /* MessageTransmitViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MessageTransmitViewController.m; sourceTree = "<group>"; };
		7758EFCB1FE7CE5C0084D11E /* BRUpDateView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRUpDateView.h; sourceTree = "<group>"; };
		7758EFCC1FE7CE5C0084D11E /* BRUpDateView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRUpDateView.m; sourceTree = "<group>"; };
		775967331FFB280700ADC587 /* DoctorAuthentiationModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DoctorAuthentiationModel.h; sourceTree = "<group>"; };
		775967341FFB280700ADC587 /* DoctorAuthentiationModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DoctorAuthentiationModel.m; sourceTree = "<group>"; };
		775E751020A999D500EAA5DB /* MyPurseHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyPurseHeaderView.h; sourceTree = "<group>"; };
		775E751120A999D500EAA5DB /* MyPurseHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyPurseHeaderView.m; sourceTree = "<group>"; };
		775E751320AA76B600EAA5DB /* BillDetailsHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsHeaderView.h; sourceTree = "<group>"; };
		775E751420AA76B600EAA5DB /* BillDetailsHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsHeaderView.m; sourceTree = "<group>"; };
		775E751620AADE4B00EAA5DB /* BillDetailsModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsModel.h; sourceTree = "<group>"; };
		775E751720AADE4B00EAA5DB /* BillDetailsModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsModel.m; sourceTree = "<group>"; };
		775E751920AAEB0600EAA5DB /* BillDetailsBaseCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsBaseCell.h; sourceTree = "<group>"; };
		775E751A20AAEB0600EAA5DB /* BillDetailsBaseCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsBaseCell.m; sourceTree = "<group>"; };
		7761534B1FCD30D9006A2FAC /* PatientDocumentModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientDocumentModel.h; sourceTree = "<group>"; };
		7761534C1FCD30D9006A2FAC /* PatientDocumentModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientDocumentModel.m; sourceTree = "<group>"; };
		776153521FCD74D0006A2FAC /* BRComPrescriptionViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRComPrescriptionViewController.h; sourceTree = "<group>"; };
		776153531FCD74D0006A2FAC /* BRComPrescriptionViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRComPrescriptionViewController.m; sourceTree = "<group>"; };
		7761772A209958F400C35E77 /* ChatTitleView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChatTitleView.h; sourceTree = "<group>"; };
		7761772B209958F400C35E77 /* ChatTitleView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChatTitleView.m; sourceTree = "<group>"; };
		7761772D20995B2300C35E77 /* AgeGenderButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AgeGenderButton.h; sourceTree = "<group>"; };
		7761772E20995B2300C35E77 /* AgeGenderButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AgeGenderButton.m; sourceTree = "<group>"; };
		776191561F5A436100A19B77 /* UIImageView+Badge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+Badge.h"; sourceTree = "<group>"; };
		776191571F5A436100A19B77 /* UIImageView+Badge.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+Badge.m"; sourceTree = "<group>"; };
		776191621F5A491100A19B77 /* UIButton+Badge.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIButton+Badge.h"; sourceTree = "<group>"; };
		776191631F5A491100A19B77 /* UIButton+Badge.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIButton+Badge.m"; sourceTree = "<group>"; };
		7762973A1FDA69CC00E480F2 /* SessionHistoryViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SessionHistoryViewController.h; sourceTree = "<group>"; };
		7762973B1FDA69CC00E480F2 /* SessionHistoryViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SessionHistoryViewController.m; sourceTree = "<group>"; };
		7762B04120CE1A8600273CD5 /* BRMedicationWarningAlertView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRMedicationWarningAlertView.h; sourceTree = "<group>"; };
		7762B04220CE1A8700273CD5 /* BRMedicationWarningAlertView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRMedicationWarningAlertView.m; sourceTree = "<group>"; };
		776365F81F4FC18A00AF92A1 /* UITextView+Placeholder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UITextView+Placeholder.h"; sourceTree = "<group>"; };
		776365F91F4FC18A00AF92A1 /* UITextView+Placeholder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UITextView+Placeholder.m"; sourceTree = "<group>"; };
		776365FB1F4FC19300AF92A1 /* UIButton+HotPointButtonCategory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIButton+HotPointButtonCategory.h"; sourceTree = "<group>"; };
		776365FC1F4FC19300AF92A1 /* UIButton+HotPointButtonCategory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIButton+HotPointButtonCategory.m"; sourceTree = "<group>"; };
		776365FE1F4FC2D800AF92A1 /* UITextField+Max.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UITextField+Max.h"; sourceTree = "<group>"; };
		776365FF1F4FC2D800AF92A1 /* UITextField+Max.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UITextField+Max.m"; sourceTree = "<group>"; };
		776366011F4FD00100AF92A1 /* UserInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserInfo.h; sourceTree = "<group>"; };
		776366021F4FD00100AF92A1 /* UserInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserInfo.m; sourceTree = "<group>"; };
		7764AB0F1F60081800B26332 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		776633911F5E97F500CF7029 /* IMTableMessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMTableMessage.h; sourceTree = "<group>"; };
		776633921F5E97F500CF7029 /* IMTableMessage.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = IMTableMessage.mm; sourceTree = "<group>"; };
		776633941F5E980200CF7029 /* IMTableSession.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMTableSession.h; sourceTree = "<group>"; };
		776633951F5E980200CF7029 /* IMTableSession.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = IMTableSession.mm; sourceTree = "<group>"; };
		776633971F5E981B00CF7029 /* IMTableContact.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMTableContact.h; sourceTree = "<group>"; };
		776633981F5E981B00CF7029 /* IMTableContact.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = IMTableContact.mm; sourceTree = "<group>"; };
		776A6767200EE4F100412402 /* BRGuideView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRGuideView.h; sourceTree = "<group>"; };
		776A6768200EE4F100412402 /* BRGuideView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRGuideView.m; sourceTree = "<group>"; };
		776BDF081FE522F600CBD754 /* BRMedicationWarning.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRMedicationWarning.h; sourceTree = "<group>"; };
		776BDF091FE522F600CBD754 /* BRMedicationWarning.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRMedicationWarning.m; sourceTree = "<group>"; };
		776F86001FF496D600CAAE33 /* WzdWebViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WzdWebViewController.h; sourceTree = "<group>"; };
		776F86011FF496D600CAAE33 /* WzdWebViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WzdWebViewController.m; sourceTree = "<group>"; };
		776FF328212C2CB300B9F71A /* DrugStoreHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DrugStoreHeaderView.h; sourceTree = "<group>"; };
		776FF329212C2CB300B9F71A /* DrugStoreHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DrugStoreHeaderView.m; sourceTree = "<group>"; };
		776FF32B212C348E00B9F71A /* DrugHeaderButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DrugHeaderButton.h; sourceTree = "<group>"; };
		776FF32C212C348E00B9F71A /* DrugHeaderButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DrugHeaderButton.m; sourceTree = "<group>"; };
		7770FD571F53FCA000651814 /* IMClient.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMClient.h; sourceTree = "<group>"; };
		7770FD581F53FCA000651814 /* IMClient.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMClient.m; sourceTree = "<group>"; };
		7770FD5E1F53FEA100651814 /* IMMessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMMessage.h; sourceTree = "<group>"; };
		7770FD5F1F53FEA100651814 /* IMMessage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMMessage.m; sourceTree = "<group>"; };
		7770FD671F54089A00651814 /* IMContact.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMContact.h; sourceTree = "<group>"; };
		7770FD681F54089A00651814 /* IMContact.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMContact.m; sourceTree = "<group>"; };
		7770FD6A1F5408AD00651814 /* IMContactManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMContactManager.h; sourceTree = "<group>"; };
		7770FD6B1F5408AD00651814 /* IMContactManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMContactManager.m; sourceTree = "<group>"; };
		7770FD6E1F540D7D00651814 /* IMSDKHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMSDKHelper.h; sourceTree = "<group>"; };
		7770FD6F1F540D7D00651814 /* IMSDKHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMSDKHelper.m; sourceTree = "<group>"; };
		77723E041FD635BE00E1BD5A /* AddCommonlyPrescriptionViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AddCommonlyPrescriptionViewController.h; sourceTree = "<group>"; };
		77723E051FD635BE00E1BD5A /* AddCommonlyPrescriptionViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AddCommonlyPrescriptionViewController.m; sourceTree = "<group>"; };
		777418962CEF208B00392B18 /* BRIntelligentEntryViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRIntelligentEntryViewController.h; sourceTree = "<group>"; };
		777418972CEF208B00392B18 /* BRIntelligentEntryViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRIntelligentEntryViewController.m; sourceTree = "<group>"; };
		7774865E27B79B1100B49BB4 /* PatientComplainViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientComplainViewController.h; sourceTree = "<group>"; };
		7774865F27B79B1100B49BB4 /* PatientComplainViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientComplainViewController.m; sourceTree = "<group>"; };
		7774866127B7F39D00B49BB4 /* AccountTerminateViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AccountTerminateViewController.h; sourceTree = "<group>"; };
		7774866227B7F39D00B49BB4 /* AccountTerminateViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AccountTerminateViewController.m; sourceTree = "<group>"; };
		7775A1D81FFF4C77009D2131 /* BRDebugInfoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRDebugInfoViewController.h; sourceTree = "<group>"; };
		7775A1D91FFF4C77009D2131 /* BRDebugInfoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRDebugInfoViewController.m; sourceTree = "<group>"; };
		777727D0276DA66000DA7FD4 /* EbookListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EbookListViewController.h; sourceTree = "<group>"; };
		777727D1276DA66000DA7FD4 /* EbookListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EbookListViewController.m; sourceTree = "<group>"; };
		777727D3276DA73400DA7FD4 /* EbookMallListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EbookMallListViewController.h; sourceTree = "<group>"; };
		777727D4276DA73400DA7FD4 /* EbookMallListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EbookMallListViewController.m; sourceTree = "<group>"; };
		777727D9276DDF7200DA7FD4 /* EbookListItemCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EbookListItemCell.h; sourceTree = "<group>"; };
		777727DA276DDF7200DA7FD4 /* EbookListItemCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EbookListItemCell.m; sourceTree = "<group>"; };
		777727DC276DE62C00DA7FD4 /* EbookMallListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EbookMallListCell.h; sourceTree = "<group>"; };
		777727DD276DE62C00DA7FD4 /* EbookMallListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EbookMallListCell.m; sourceTree = "<group>"; };
		7777422F1FD56A710060BEF2 /* PatientsListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientsListCell.h; sourceTree = "<group>"; };
		777742301FD56A710060BEF2 /* PatientsListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientsListCell.m; sourceTree = "<group>"; };
		777742381FD587150060BEF2 /* FrequentlyQuestionAddViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FrequentlyQuestionAddViewController.h; sourceTree = "<group>"; };
		777742391FD587150060BEF2 /* FrequentlyQuestionAddViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FrequentlyQuestionAddViewController.m; sourceTree = "<group>"; };
		777A23982CF80E2300C0853F /* BRHerbInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRHerbInfo.h; sourceTree = "<group>"; };
		777A23992CF80E2300C0853F /* BRHerbInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRHerbInfo.m; sourceTree = "<group>"; };
		777B87982CFC15B900004875 /* CustomBoilyWayViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CustomBoilyWayViewController.h; sourceTree = "<group>"; };
		777B87992CFC15B900004875 /* CustomBoilyWayViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CustomBoilyWayViewController.m; sourceTree = "<group>"; };
		777B879B2CFC1A7C00004875 /* CustomBoilyWayCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CustomBoilyWayCell.h; sourceTree = "<group>"; };
		777B879C2CFC1A7C00004875 /* CustomBoilyWayCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CustomBoilyWayCell.m; sourceTree = "<group>"; };
		777DC1F5273D687C00A594D7 /* JTPrivacyWebViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JTPrivacyWebViewController.h; sourceTree = "<group>"; };
		777DC1F6273D687D00A594D7 /* JTPrivacyWebViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JTPrivacyWebViewController.m; sourceTree = "<group>"; };
		777E588020526B1C000336AC /* ClassicPrescriptionViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ClassicPrescriptionViewController.h; sourceTree = "<group>"; };
		777E588120526B1C000336AC /* ClassicPrescriptionViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ClassicPrescriptionViewController.m; sourceTree = "<group>"; };
		778139211FF2521700852B27 /* PatientsDocumentModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientsDocumentModel.h; sourceTree = "<group>"; };
		778139221FF2521700852B27 /* PatientsDocumentModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientsDocumentModel.m; sourceTree = "<group>"; };
		7781E28E1F4D19CE00DDD047 /* Config.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Config.h; sourceTree = "<group>"; };
		7781E28F1F4D19CE00DDD047 /* Config.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Config.m; sourceTree = "<group>"; };
		7781E2911F4D1AB900DDD047 /* SocketManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SocketManager.h; path = ../Helpers/SocketManager.h; sourceTree = "<group>"; };
		7781E2921F4D1AB900DDD047 /* SocketManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SocketManager.m; path = ../Helpers/SocketManager.m; sourceTree = "<group>"; };
		7781E2A01F4D1D4700DDD047 /* IMDataBaseManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMDataBaseManager.h; sourceTree = "<group>"; };
		7781E2A11F4D1D4700DDD047 /* IMDataBaseManager.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = IMDataBaseManager.mm; sourceTree = "<group>"; };
		7785B28F2E224E9F006F68FC /* BRPackageSpecModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPackageSpecModel.h; sourceTree = "<group>"; };
		7785B2902E224E9F006F68FC /* BRPackageSpecModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPackageSpecModel.m; sourceTree = "<group>"; };
		7786ECE71FF0AACD008BEEE7 /* PatientSelectedInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientSelectedInfoModel.h; sourceTree = "<group>"; };
		7786ECE81FF0AACD008BEEE7 /* PatientSelectedInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientSelectedInfoModel.m; sourceTree = "<group>"; };
		778E0B63279BCBD700F9BA6B /* BRWechatPocketMoneyViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRWechatPocketMoneyViewController.h; sourceTree = "<group>"; };
		778E0B64279BCBD700F9BA6B /* BRWechatPocketMoneyViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRWechatPocketMoneyViewController.m; sourceTree = "<group>"; };
		778E0B66279BD0BD00F9BA6B /* WechatWithdrawChooseCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WechatWithdrawChooseCell.h; sourceTree = "<group>"; };
		778E0B67279BD0BD00F9BA6B /* WechatWithdrawChooseCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WechatWithdrawChooseCell.m; sourceTree = "<group>"; };
		778E0B69279BD15A00F9BA6B /* WechatWithDrawSumCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WechatWithDrawSumCell.h; sourceTree = "<group>"; };
		778E0B6A279BD15A00F9BA6B /* WechatWithDrawSumCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WechatWithDrawSumCell.m; sourceTree = "<group>"; };
		778E0B6C279BD9BE00F9BA6B /* WechatWithdrawBottomView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WechatWithdrawBottomView.h; sourceTree = "<group>"; };
		778E0B6D279BD9BE00F9BA6B /* WechatWithdrawBottomView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WechatWithdrawBottomView.m; sourceTree = "<group>"; };
		778E0B6F279BE0D900F9BA6B /* WechatWithdrawInputNumCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WechatWithdrawInputNumCell.h; sourceTree = "<group>"; };
		778E0B70279BE0D900F9BA6B /* WechatWithdrawInputNumCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WechatWithdrawInputNumCell.m; sourceTree = "<group>"; };
		778E0B73279D341100F9BA6B /* BRPrivacyPopView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRPrivacyPopView.h; sourceTree = "<group>"; };
		778E0B74279D341100F9BA6B /* BRPrivacyPopView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRPrivacyPopView.m; sourceTree = "<group>"; };
		778E0B76279D6C7600F9BA6B /* FirstOpenViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FirstOpenViewController.h; sourceTree = "<group>"; };
		778E0B77279D6C7600F9BA6B /* FirstOpenViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FirstOpenViewController.m; sourceTree = "<group>"; };
		7790B2062E06594800A08108 /* WithdrawPasswordViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WithdrawPasswordViewController.h; sourceTree = "<group>"; };
		7790B2072E06594800A08108 /* WithdrawPasswordViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WithdrawPasswordViewController.m; sourceTree = "<group>"; };
		7793B9622C15F1DD006AD7FF /* BRMessagePrescribeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRMessagePrescribeViewController.h; sourceTree = "<group>"; };
		7793B9632C15F1DD006AD7FF /* BRMessagePrescribeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRMessagePrescribeViewController.m; sourceTree = "<group>"; };
		7793D29D2A236B9400971D97 /* BRTakerInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRTakerInfoModel.h; sourceTree = "<group>"; };
		7793D29E2A236B9400971D97 /* BRTakerInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRTakerInfoModel.m; sourceTree = "<group>"; };
		7793D2A02A2371D600971D97 /* BRQuickPrescribePatientModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRQuickPrescribePatientModel.h; sourceTree = "<group>"; };
		7793D2A12A2371D600971D97 /* BRQuickPrescribePatientModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRQuickPrescribePatientModel.m; sourceTree = "<group>"; };
		7795B576213E214E006A2A9E /* BillDetailsDrugStoreRewardCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsDrugStoreRewardCell.h; sourceTree = "<group>"; };
		7795B577213E214E006A2A9E /* BillDetailsDrugStoreRewardCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsDrugStoreRewardCell.m; sourceTree = "<group>"; };
		779642781FC40B9A00AA0976 /* BRSelectPatientView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRSelectPatientView.h; sourceTree = "<group>"; };
		779642791FC40B9A00AA0976 /* BRSelectPatientView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRSelectPatientView.m; sourceTree = "<group>"; };
		7796427A1FC40B9A00AA0976 /* UILabel+myLabel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UILabel+myLabel.h"; sourceTree = "<group>"; };
		7796427B1FC40B9A00AA0976 /* UILabel+myLabel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UILabel+myLabel.m"; sourceTree = "<group>"; };
		7796427D1FC40B9A00AA0976 /* BRSelectCityZone.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRSelectCityZone.h; sourceTree = "<group>"; };
		7796427E1FC40B9A00AA0976 /* BRSelectCityZone.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRSelectCityZone.m; sourceTree = "<group>"; };
		779642801FC40B9A00AA0976 /* BRDHBaseViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRDHBaseViewController.h; sourceTree = "<group>"; };
		779642811FC40B9A00AA0976 /* BRDHBaseViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRDHBaseViewController.m; sourceTree = "<group>"; };
		779642821FC40B9A00AA0976 /* Header.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Header.h; sourceTree = "<group>"; };
		779642901FC4214500AA0976 /* BRAddVisitsViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRAddVisitsViewController.h; sourceTree = "<group>"; };
		779642911FC4214500AA0976 /* BRAddVisitsViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRAddVisitsViewController.m; sourceTree = "<group>"; };
		779642921FC4214500AA0976 /* BRVisitsArrangementViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRVisitsArrangementViewController.h; sourceTree = "<group>"; };
		779642931FC4214500AA0976 /* BRVisitsArrangementViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRVisitsArrangementViewController.m; sourceTree = "<group>"; };
		779642951FC4214500AA0976 /* BRCalendarCollectionViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRCalendarCollectionViewCell.h; sourceTree = "<group>"; };
		779642961FC4214500AA0976 /* BRCalendarCollectionViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRCalendarCollectionViewCell.m; sourceTree = "<group>"; };
		779642971FC4214500AA0976 /* BRCalendarCollectionViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BRCalendarCollectionViewCell.xib; sourceTree = "<group>"; };
		779642981FC4214500AA0976 /* BRCalendarView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRCalendarView.h; sourceTree = "<group>"; };
		779642991FC4214500AA0976 /* BRCalendarView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRCalendarView.m; sourceTree = "<group>"; };
		7796429C1FC4214500AA0976 /* BRMyPurseViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRMyPurseViewController.h; sourceTree = "<group>"; };
		7796429D1FC4214500AA0976 /* BRMyPurseViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRMyPurseViewController.m; sourceTree = "<group>"; };
		779F1797213CDD2700F44BE3 /* PatientFooterView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientFooterView.h; sourceTree = "<group>"; };
		779F1798213CDD2700F44BE3 /* PatientFooterView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientFooterView.m; sourceTree = "<group>"; };
		779FC8282DF5C7F4001CA5D3 /* ATAuthSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ATAuthSDK.framework; sourceTree = "<group>"; };
		779FC8292DF5C7F4001CA5D3 /* YTXMonitor.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTXMonitor.framework; sourceTree = "<group>"; };
		779FC82A2DF5C7F4001CA5D3 /* YTXOperators.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTXOperators.framework; sourceTree = "<group>"; };
		77A3BB5B2D09D65F0074A786 /* BRUpdateUserInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRUpdateUserInfoModel.h; sourceTree = "<group>"; };
		77A3BB5C2D09D65F0074A786 /* BRUpdateUserInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRUpdateUserInfoModel.m; sourceTree = "<group>"; };
		77A5461F1FFE46020086EFD8 /* InterrogationAndVisitViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InterrogationAndVisitViewController.h; sourceTree = "<group>"; };
		77A546201FFE46020086EFD8 /* InterrogationAndVisitViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InterrogationAndVisitViewController.m; sourceTree = "<group>"; };
		77A546221FFE52A80086EFD8 /* BRNameTextField.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRNameTextField.h; sourceTree = "<group>"; };
		77A546231FFE52A80086EFD8 /* BRNameTextField.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRNameTextField.m; sourceTree = "<group>"; };
		77A7DF971FA09F520012C18C /* EWVoiceHUD.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EWVoiceHUD.h; sourceTree = "<group>"; };
		77A7DF981FA09F520012C18C /* EWVoiceHUD.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EWVoiceHUD.m; sourceTree = "<group>"; };
		77A7DF9A1FA0A10F0012C18C /* AACRecord.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AACRecord.h; sourceTree = "<group>"; };
		77A7DF9B1FA0A10F0012C18C /* AACRecord.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AACRecord.m; sourceTree = "<group>"; };
		77A8C4BE1F5FE2960056597E /* IMTableContact+WCTTableCoding.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IMTableContact+WCTTableCoding.h"; sourceTree = "<group>"; };
		77ABE80528B0A8BD00176DC3 /* QuickReplyShowView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuickReplyShowView.h; sourceTree = "<group>"; };
		77ABE80628B0A8BD00176DC3 /* QuickReplyShowView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuickReplyShowView.m; sourceTree = "<group>"; };
		77ABE80928B0B57200176DC3 /* QuickReplyContentModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuickReplyContentModel.h; sourceTree = "<group>"; };
		77ABE80A28B0B57200176DC3 /* QuickReplyContentModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuickReplyContentModel.m; sourceTree = "<group>"; };
		77ABE80C28B0B5EE00176DC3 /* QuickReplyTypeInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuickReplyTypeInfoModel.h; sourceTree = "<group>"; };
		77ABE80D28B0B5EE00176DC3 /* QuickReplyTypeInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuickReplyTypeInfoModel.m; sourceTree = "<group>"; };
		77ABE80F28B0E3C900176DC3 /* QuickReplyTopView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuickReplyTopView.h; sourceTree = "<group>"; };
		77ABE81028B0E3C900176DC3 /* QuickReplyTopView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuickReplyTopView.m; sourceTree = "<group>"; };
		77ABE81228B0EB9900176DC3 /* QuickReplyContentListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuickReplyContentListCell.h; sourceTree = "<group>"; };
		77ABE81328B0EB9900176DC3 /* QuickReplyContentListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuickReplyContentListCell.m; sourceTree = "<group>"; };
		77ABE81528B0F74800176DC3 /* QuickReplyQuesAddViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuickReplyQuesAddViewController.h; sourceTree = "<group>"; };
		77ABE81628B0F74800176DC3 /* QuickReplyQuesAddViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuickReplyQuesAddViewController.m; sourceTree = "<group>"; };
		77B13F8E1F5FD948005B9755 /* IMTableSession+WCTTableCoding.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IMTableSession+WCTTableCoding.h"; sourceTree = "<group>"; };
		77B3A54B2D24465000097E8A /* BRDrugSpecSelectView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRDrugSpecSelectView.h; sourceTree = "<group>"; };
		77B3A54C2D24465000097E8A /* BRDrugSpecSelectView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRDrugSpecSelectView.m; sourceTree = "<group>"; };
		77B3A54E2D24479A00097E8A /* TCMMatchResult.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TCMMatchResult.h; sourceTree = "<group>"; };
		77B3A54F2D24479A00097E8A /* TCMMatchResult.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TCMMatchResult.m; sourceTree = "<group>"; };
		77B3A5512D2447C000097E8A /* TCPMappingParser.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TCPMappingParser.h; sourceTree = "<group>"; };
		77B3A5522D2447C000097E8A /* TCPMappingParser.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TCPMappingParser.m; sourceTree = "<group>"; };
		77B481721F6638FF00466F3B /* IMChatCollectionViewLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMChatCollectionViewLayout.h; sourceTree = "<group>"; };
		77B481731F6638FF00466F3B /* IMChatCollectionViewLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMChatCollectionViewLayout.m; sourceTree = "<group>"; };
		77B481751F663A8D00466F3B /* IMChatContainerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMChatContainerView.h; sourceTree = "<group>"; };
		77B481761F663A8D00466F3B /* IMChatContainerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMChatContainerView.m; sourceTree = "<group>"; };
		77B481781F663B8400466F3B /* IMChatInputPanel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMChatInputPanel.h; sourceTree = "<group>"; };
		77B481791F663B8400466F3B /* IMChatInputPanel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMChatInputPanel.m; sourceTree = "<group>"; };
		77B4817B1F665DDB00466F3B /* IMChatItemCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMChatItemCell.h; sourceTree = "<group>"; };
		77B4817C1F665DDB00466F3B /* IMChatItemCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMChatItemCell.m; sourceTree = "<group>"; };
		77B4817E1F665EDA00466F3B /* IMChatCollectionView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMChatCollectionView.h; sourceTree = "<group>"; };
		77B4817F1F665EDA00466F3B /* IMChatCollectionView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMChatCollectionView.m; sourceTree = "<group>"; };
		77B481821F6660E600466F3B /* IMChatViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMChatViewController.h; sourceTree = "<group>"; };
		77B481831F6660E600466F3B /* IMChatViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMChatViewController.m; sourceTree = "<group>"; };
		77B481871F66662200466F3B /* IMChat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IMChat.h; sourceTree = "<group>"; };
		77B481891F66740A00466F3B /* HPGrowingTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HPGrowingTextView.h; sourceTree = "<group>"; };
		77B4818A1F66740A00466F3B /* HPGrowingTextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HPGrowingTextView.m; sourceTree = "<group>"; };
		77B4818B1F66740A00466F3B /* HPTextViewInternal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HPTextViewInternal.h; sourceTree = "<group>"; };
		77B4818C1F66740A00466F3B /* HPTextViewInternal.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HPTextViewInternal.m; sourceTree = "<group>"; };
		77B481941F6676E100466F3B /* BRChatInputTextPanel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRChatInputTextPanel.h; sourceTree = "<group>"; };
		77B481951F6676E100466F3B /* BRChatInputTextPanel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRChatInputTextPanel.m; sourceTree = "<group>"; };
		77B481971F667A3700466F3B /* BRMessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRMessage.h; sourceTree = "<group>"; };
		77B481981F667A3700466F3B /* BRMessage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRMessage.m; sourceTree = "<group>"; };
		77B4819A1F6682C800466F3B /* BRBaseMessageCellLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRBaseMessageCellLayout.h; sourceTree = "<group>"; };
		77B4819B1F6682C800466F3B /* BRBaseMessageCellLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRBaseMessageCellLayout.m; sourceTree = "<group>"; };
		77B4819D1F6683F200466F3B /* BRBaseMessageCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRBaseMessageCell.h; sourceTree = "<group>"; };
		77B4819E1F6683F200466F3B /* BRBaseMessageCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRBaseMessageCell.m; sourceTree = "<group>"; };
		77B481A01F66849E00466F3B /* BRTextMessageCellLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRTextMessageCellLayout.h; sourceTree = "<group>"; };
		77B481A11F66849E00466F3B /* BRTextMessageCellLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRTextMessageCellLayout.m; sourceTree = "<group>"; };
		77B481A31F6689E700466F3B /* BRTextMessageCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRTextMessageCell.h; sourceTree = "<group>"; };
		77B481A41F6689E700466F3B /* BRTextMessageCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRTextMessageCell.m; sourceTree = "<group>"; };
		77B481A61F668B5D00466F3B /* BRSystemMessageCellLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRSystemMessageCellLayout.h; sourceTree = "<group>"; };
		77B481A71F668B5D00466F3B /* BRSystemMessageCellLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRSystemMessageCellLayout.m; sourceTree = "<group>"; };
		77B481AC1F668CC300466F3B /* NSAttributedString+BR.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSAttributedString+BR.h"; sourceTree = "<group>"; };
		77B481AD1F668CC300466F3B /* NSAttributedString+BR.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSAttributedString+BR.m"; sourceTree = "<group>"; };
		77B481AF1F668DC900466F3B /* BRSystemMessageCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRSystemMessageCell.h; sourceTree = "<group>"; };
		77B481B01F668DC900466F3B /* BRSystemMessageCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRSystemMessageCell.m; sourceTree = "<group>"; };
		77B481B21F668E6300466F3B /* BRDateMessageCellLayout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRDateMessageCellLayout.h; sourceTree = "<group>"; };
		77B481B31F668E6300466F3B /* BRDateMessageCellLayout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRDateMessageCellLayout.m; sourceTree = "<group>"; };
		77B481B81F668FC100466F3B /* BRDateMessageCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRDateMessageCell.h; sourceTree = "<group>"; };
		77B481B91F668FC100466F3B /* BRDateMessageCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRDateMessageCell.m; sourceTree = "<group>"; };
		77B684961FFFAC2F00A1FFF8 /* BRSessionStartPatientMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSessionStartPatientMessageCell.h; sourceTree = "<group>"; };
		77B684971FFFAC2F00A1FFF8 /* BRSessionStartPatientMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSessionStartPatientMessageCell.m; sourceTree = "<group>"; };
		77B684991FFFAC4900A1FFF8 /* BRSessionStartPatientMessageCellLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRSessionStartPatientMessageCellLayout.h; sourceTree = "<group>"; };
		77B6849A1FFFAC4900A1FFF8 /* BRSessionStartPatientMessageCellLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRSessionStartPatientMessageCellLayout.m; sourceTree = "<group>"; };
		77BA08E226DA6D100054371B /* BRZYRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = BRZYRelease.entitlements; sourceTree = "<group>"; };
		77BC46B32000DCCE0066CE29 /* BRVisitsHospitalName.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRVisitsHospitalName.h; sourceTree = "<group>"; };
		77BC46B42000DCCE0066CE29 /* BRVisitsHospitalName.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRVisitsHospitalName.m; sourceTree = "<group>"; };
		77BC46B62000DD280066CE29 /* BRVisitsHospitalAddress.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRVisitsHospitalAddress.h; sourceTree = "<group>"; };
		77BC46B72000DD280066CE29 /* BRVisitsHospitalAddress.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRVisitsHospitalAddress.m; sourceTree = "<group>"; };
		77BDAF4826CE990F000470BE /* JTAreaInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = JTAreaInfoModel.h; sourceTree = "<group>"; };
		77BDAF4926CE990F000470BE /* JTAreaInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = JTAreaInfoModel.m; sourceTree = "<group>"; };
		77BDD74420AC0CB400762078 /* BillDetailsPrescriptionOrderCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsPrescriptionOrderCell.h; sourceTree = "<group>"; };
		77BDD74520AC0CB400762078 /* BillDetailsPrescriptionOrderCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsPrescriptionOrderCell.m; sourceTree = "<group>"; };
		77BDD74720AC0CC800762078 /* BillDetailsWithdrawCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsWithdrawCell.h; sourceTree = "<group>"; };
		77BDD74820AC0CC800762078 /* BillDetailsWithdrawCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsWithdrawCell.m; sourceTree = "<group>"; };
		77BDD74A20AC10AE00762078 /* BillDetailsWithdrawFailedCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsWithdrawFailedCell.h; sourceTree = "<group>"; };
		77BDD74B20AC10AE00762078 /* BillDetailsWithdrawFailedCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsWithdrawFailedCell.m; sourceTree = "<group>"; };
		77BDD74D20AC10E400762078 /* BillDetailsConsultFeeCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsConsultFeeCell.h; sourceTree = "<group>"; };
		77BDD74E20AC10E400762078 /* BillDetailsConsultFeeCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsConsultFeeCell.m; sourceTree = "<group>"; };
		77BDD75020AC113F00762078 /* BillDetailsPatientRewardCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsPatientRewardCell.h; sourceTree = "<group>"; };
		77BDD75120AC113F00762078 /* BillDetailsPatientRewardCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsPatientRewardCell.m; sourceTree = "<group>"; };
		77BDD75320AC116A00762078 /* BillDetailsPlateformRewardCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsPlateformRewardCell.h; sourceTree = "<group>"; };
		77BDD75420AC116A00762078 /* BillDetailsPlateformRewardCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsPlateformRewardCell.m; sourceTree = "<group>"; };
		77BDD75620AC2ECF00762078 /* BillDetailsWithdrawServiceChargeCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsWithdrawServiceChargeCell.h; sourceTree = "<group>"; };
		77BDD75720AC2ECF00762078 /* BillDetailsWithdrawServiceChargeCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsWithdrawServiceChargeCell.m; sourceTree = "<group>"; };
		77BDD75920AC2F3A00762078 /* BillDetailsWithdrawFailedServiceChargeCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsWithdrawFailedServiceChargeCell.h; sourceTree = "<group>"; };
		77BDD75A20AC2F3A00762078 /* BillDetailsWithdrawFailedServiceChargeCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsWithdrawFailedServiceChargeCell.m; sourceTree = "<group>"; };
		77BDD75C20AC506600762078 /* BillDetailsInviteCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BillDetailsInviteCell.h; sourceTree = "<group>"; };
		77BDD75D20AC506600762078 /* BillDetailsInviteCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BillDetailsInviteCell.m; sourceTree = "<group>"; };
		77BEE65F1F5565BE00597489 /* BRSocketReceiveBaseModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRSocketReceiveBaseModel.h; sourceTree = "<group>"; };
		77BEE6601F5565BE00597489 /* BRSocketReceiveBaseModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRSocketReceiveBaseModel.m; sourceTree = "<group>"; };
		77BEE6631F55690A00597489 /* BRLoginModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BRLoginModel.h; sourceTree = "<group>"; };
		77BEE6641F55690A00597489 /* BRLoginModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BRLoginModel.m; sourceTree = "<group>"; };
		77BEE6671F556EC900597489 /* BRError.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = BRError.h; path = ../General/BRError.h; sourceTree = "<group>"; };
		77BEE6681F556EC900597489 /* BRError.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = BRError.m; path = ../General/BRError.m; sourceTree = "<group>"; };
		77BEE66A1F55748200597489 /* BRDefines.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRDefines.h; sourceTree = "<group>"; };
		77C021F328BAF4E000F551AF /* EnterQuickOrderViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EnterQuickOrderViewController.h; sourceTree = "<group>"; };
		77C021F428BAF4E000F551AF /* EnterQuickOrderViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EnterQuickOrderViewController.m; sourceTree = "<group>"; };
		77C1EA17212C036100256EA8 /* DrugStoreViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DrugStoreViewController.h; sourceTree = "<group>"; };
		77C1EA18212C036100256EA8 /* DrugStoreViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DrugStoreViewController.m; sourceTree = "<group>"; };
		77C29325293B0995001A5B45 /* DrugDetailListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DrugDetailListViewController.h; sourceTree = "<group>"; };
		77C29326293B0995001A5B45 /* DrugDetailListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DrugDetailListViewController.m; sourceTree = "<group>"; };
		77C29329293C29EA001A5B45 /* DrugInfoListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DrugInfoListCell.h; sourceTree = "<group>"; };
		77C2932A293C29EA001A5B45 /* DrugInfoListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DrugInfoListCell.m; sourceTree = "<group>"; };
		77C5D3C41F7A499500E836BE /* ManagerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ManagerViewController.h; sourceTree = "<group>"; };
		77C5D3C51F7A499500E836BE /* ManagerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ManagerViewController.m; sourceTree = "<group>"; };
		77C5D3C71F7A4D6800E836BE /* HTTPRequest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HTTPRequest.h; sourceTree = "<group>"; };
		77C5D3C81F7A4D6800E836BE /* HTTPRequest.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HTTPRequest.m; sourceTree = "<group>"; };
		77C7D24C1F5815C300C6F31A /* UserCenterFooterView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserCenterFooterView.h; sourceTree = "<group>"; };
		77C7D24D1F5815C300C6F31A /* UserCenterFooterView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserCenterFooterView.m; sourceTree = "<group>"; };
		77CA9C3A1F5CFEBF00B6CDE5 /* IMSession.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMSession.h; sourceTree = "<group>"; };
		77CA9C3B1F5CFEBF00B6CDE5 /* IMSession.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMSession.m; sourceTree = "<group>"; };
		77CB751122D5CAF000262B0C /* BrokerOrderListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BrokerOrderListViewController.h; sourceTree = "<group>"; };
		77CB751222D5CAF000262B0C /* BrokerOrderListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BrokerOrderListViewController.m; sourceTree = "<group>"; };
		77CB751522D5DD4800262B0C /* BrokerOrderListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BrokerOrderListCell.h; sourceTree = "<group>"; };
		77CB751622D5DD4800262B0C /* BrokerOrderListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BrokerOrderListCell.m; sourceTree = "<group>"; };
		77CB751922D5E7D500262B0C /* BrokerInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BrokerInfoModel.h; sourceTree = "<group>"; };
		77CB751A22D5E7D500262B0C /* BrokerInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BrokerInfoModel.m; sourceTree = "<group>"; };
		77CFB9442AD4D77600A1EA90 /* BeianViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BeianViewController.h; sourceTree = "<group>"; };
		77CFB9452AD4D77600A1EA90 /* BeianViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BeianViewController.m; sourceTree = "<group>"; };
		77CFB9472AD5031900A1EA90 /* InviteScanPrescribeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InviteScanPrescribeViewController.h; sourceTree = "<group>"; };
		77CFB9482AD5031900A1EA90 /* InviteScanPrescribeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InviteScanPrescribeViewController.m; sourceTree = "<group>"; };
		77D14A8B22D6EA150013CC7D /* BrokerOrderSearchViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BrokerOrderSearchViewController.h; sourceTree = "<group>"; };
		77D14A8C22D6EA150013CC7D /* BrokerOrderSearchViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BrokerOrderSearchViewController.m; sourceTree = "<group>"; };
		77D39E4C1FD2A5EF004A01E0 /* UITextView+APSUIControlTargetAction.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UITextView+APSUIControlTargetAction.m"; sourceTree = "<group>"; };
		77D39E4D1FD2A5EF004A01E0 /* UITextView+MaxLength.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UITextView+MaxLength.m"; sourceTree = "<group>"; };
		77D39E4E1FD2A5F0004A01E0 /* UITextView+APSUIControlTargetAction.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UITextView+APSUIControlTargetAction.h"; sourceTree = "<group>"; };
		77D39E4F1FD2A5F0004A01E0 /* UITextView+MaxLength.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UITextView+MaxLength.h"; sourceTree = "<group>"; };
		77D778281FEDEB3100CB1A6F /* BRAnnouncementView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRAnnouncementView.h; sourceTree = "<group>"; };
		77D778291FEDEB3100CB1A6F /* BRAnnouncementView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRAnnouncementView.m; sourceTree = "<group>"; };
		77DB713A1F56E07F00D07919 /* UserCenterPanelCollectionViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserCenterPanelCollectionViewCell.h; sourceTree = "<group>"; };
		77DB713B1F56E07F00D07919 /* UserCenterPanelCollectionViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserCenterPanelCollectionViewCell.m; sourceTree = "<group>"; };
		77DB713D1F56F34E00D07919 /* UserCenterHeaderView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserCenterHeaderView.h; sourceTree = "<group>"; };
		77DB713E1F56F34E00D07919 /* UserCenterHeaderView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserCenterHeaderView.m; sourceTree = "<group>"; };
		77E59D982C8C19B000CB2662 /* BRCustomPopupView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRCustomPopupView.h; sourceTree = "<group>"; };
		77E59D992C8C19B000CB2662 /* BRCustomPopupView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRCustomPopupView.m; sourceTree = "<group>"; };
		77E870131FE3C4AC005E0F74 /* BRContactModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRContactModel.h; sourceTree = "<group>"; };
		77E870141FE3C4AC005E0F74 /* BRContactModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRContactModel.m; sourceTree = "<group>"; };
		77E8B6C522F2977D00D5C08C /* BrokerUserModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BrokerUserModel.h; sourceTree = "<group>"; };
		77E8B6C622F2977D00D5C08C /* BrokerUserModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BrokerUserModel.m; sourceTree = "<group>"; };
		77E8B6C822F298E900D5C08C /* BrokerOrderUserCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BrokerOrderUserCell.h; sourceTree = "<group>"; };
		77E8B6C922F298E900D5C08C /* BrokerOrderUserCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BrokerOrderUserCell.m; sourceTree = "<group>"; };
		77E94C021F5FA1380010812A /* IMTableMessage+WCTTableCoding.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "IMTableMessage+WCTTableCoding.h"; sourceTree = "<group>"; };
		77E976EA1FF1E44F00404F69 /* BRTablePharmacopeia+WCTTableCoding.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "BRTablePharmacopeia+WCTTableCoding.h"; sourceTree = "<group>"; };
		77EA2616295DA047000D1359 /* BRMessageTitleView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRMessageTitleView.h; sourceTree = "<group>"; };
		77EA2617295DA047000D1359 /* BRMessageTitleView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRMessageTitleView.m; sourceTree = "<group>"; };
		77EA2619295EDE64000D1359 /* BRCashTypeInfoModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRCashTypeInfoModel.h; sourceTree = "<group>"; };
		77EA261A295EDE64000D1359 /* BRCashTypeInfoModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRCashTypeInfoModel.m; sourceTree = "<group>"; };
		77EF33DA1FD8D84B0008B712 /* BRGuiZeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BRGuiZeViewController.h; sourceTree = "<group>"; };
		77EF33DB1FD8D84B0008B712 /* BRGuiZeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BRGuiZeViewController.m; sourceTree = "<group>"; };
		77EF505E2DA2294000F085B1 /* AuthCheckHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AuthCheckHelper.h; sourceTree = "<group>"; };
		77EF505F2DA2294000F085B1 /* AuthCheckHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AuthCheckHelper.m; sourceTree = "<group>"; };
		77EF7D6D1FEB549800232BC0 /* PatientsListXiaoRanView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientsListXiaoRanView.h; sourceTree = "<group>"; };
		77EF7D6E1FEB549800232BC0 /* PatientsListXiaoRanView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientsListXiaoRanView.m; sourceTree = "<group>"; };
		77F4E76F1F6BBC9D00897C4A /* IMChatInputMoreView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IMChatInputMoreView.h; sourceTree = "<group>"; };
		77F4E7701F6BBC9D00897C4A /* IMChatInputMoreView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IMChatInputMoreView.m; sourceTree = "<group>"; };
		77F6D00C2D12878E00247F4E /* .env */ = {isa = PBXFileReference; lastKnownFileType = text; path = .env; sourceTree = "<group>"; };
		77F7A1441F5E3D6600EA5FC1 /* UserCenterListCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UserCenterListCell.h; sourceTree = "<group>"; };
		77F7A1451F5E3D6600EA5FC1 /* UserCenterListCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UserCenterListCell.m; sourceTree = "<group>"; };
		77F7A1471F5E3DCF00EA5FC1 /* ListCellModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ListCellModel.h; sourceTree = "<group>"; };
		77F7A1481F5E3DCF00EA5FC1 /* ListCellModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ListCellModel.m; sourceTree = "<group>"; };
		77F7A14A1F5E3FCD00EA5FC1 /* AboutHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AboutHeader.h; sourceTree = "<group>"; };
		77F7A14B1F5E3FCD00EA5FC1 /* AboutHeader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AboutHeader.m; sourceTree = "<group>"; };
		77F7A14D1F5E51AB00EA5FC1 /* AboutFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AboutFooter.h; sourceTree = "<group>"; };
		77F7A14E1F5E51AB00EA5FC1 /* AboutFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AboutFooter.m; sourceTree = "<group>"; };
		77FCA5D11F60EC5400369CB9 /* PopListCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PopListCell.h; sourceTree = "<group>"; };
		77FCA5D21F60EC5400369CB9 /* PopListCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PopListCell.m; sourceTree = "<group>"; };
		77FCA5D51F6136D400369CB9 /* PopoverAction.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PopoverAction.h; sourceTree = "<group>"; };
		77FCA5D61F6136D400369CB9 /* PopoverAction.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PopoverAction.m; sourceTree = "<group>"; };
		77FCA5D71F6136D400369CB9 /* PopoverView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PopoverView.h; sourceTree = "<group>"; };
		77FCA5D81F6136D400369CB9 /* PopoverView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PopoverView.m; sourceTree = "<group>"; };
		77FCA5D91F6136D400369CB9 /* PopoverViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PopoverViewCell.h; sourceTree = "<group>"; };
		77FCA5DA1F6136D400369CB9 /* PopoverViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PopoverViewCell.m; sourceTree = "<group>"; };
		77FCA5DE1F61512000369CB9 /* TitleBarView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TitleBarView.h; sourceTree = "<group>"; };
		77FCA5DF1F61512000369CB9 /* TitleBarView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TitleBarView.m; sourceTree = "<group>"; };
		89DB2C7A093AA53BF55F5BBE /* Pods_BRZY.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_BRZY.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A60629B21FFC77A9004BA00A /* HeadCollectionReusableView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HeadCollectionReusableView.h; sourceTree = "<group>"; };
		A60629B31FFC77A9004BA00A /* HeadCollectionReusableView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HeadCollectionReusableView.m; sourceTree = "<group>"; };
		A60629B51FFCD39A004BA00A /* PharmacopeiaCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PharmacopeiaCache.h; sourceTree = "<group>"; };
		A60629B61FFCD39A004BA00A /* PharmacopeiaCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PharmacopeiaCache.m; sourceTree = "<group>"; };
		A60629B81FFCDD16004BA00A /* MedicineParticularsViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MedicineParticularsViewController.m; sourceTree = "<group>"; };
		A60629B91FFCDD16004BA00A /* MedicineParticularsViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MedicineParticularsViewController.h; sourceTree = "<group>"; };
		A60629BB1FFCDDA9004BA00A /* MedicinePCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MedicinePCell.h; sourceTree = "<group>"; };
		A60629BC1FFCDDA9004BA00A /* MedicinePCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MedicinePCell.m; sourceTree = "<group>"; };
		A60629BE1FFCFD4F004BA00A /* ProblemSolvingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ProblemSolvingViewController.h; sourceTree = "<group>"; };
		A60629BF1FFCFD4F004BA00A /* ProblemSolvingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ProblemSolvingViewController.m; sourceTree = "<group>"; };
		A60629C11FFD050D004BA00A /* SearchArchitectureViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SearchArchitectureViewController.h; sourceTree = "<group>"; };
		A60629C21FFD050D004BA00A /* SearchArchitectureViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SearchArchitectureViewController.m; sourceTree = "<group>"; };
		A60629C41FFD0E74004BA00A /* SearchArchitectureCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SearchArchitectureCell.h; sourceTree = "<group>"; };
		A60629C51FFD0E74004BA00A /* SearchArchitectureCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SearchArchitectureCell.m; sourceTree = "<group>"; };
		A60B54501F95AB5500123620 /* ResultSummaryViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ResultSummaryViewController.h; sourceTree = "<group>"; };
		A60B54511F95AB5500123620 /* ResultSummaryViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ResultSummaryViewController.m; sourceTree = "<group>"; };
		A60B54531F95B07400123620 /* ResultSummaryCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ResultSummaryCell.h; sourceTree = "<group>"; };
		A60B54541F95B07400123620 /* ResultSummaryCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ResultSummaryCell.m; sourceTree = "<group>"; };
		A60DECED1FE3684100FE2C42 /* ArchitectureCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ArchitectureCell.h; sourceTree = "<group>"; };
		A60DECEE1FE3684100FE2C42 /* ArchitectureCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ArchitectureCell.m; sourceTree = "<group>"; };
		A60DECF01FE3718700FE2C42 /* ArchitectureTabCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ArchitectureTabCell.h; sourceTree = "<group>"; };
		A60DECF11FE3718700FE2C42 /* ArchitectureTabCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ArchitectureTabCell.m; sourceTree = "<group>"; };
		A60DECF31FE3DB7E00FE2C42 /* CommonlyUsedPartyModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommonlyUsedPartyModel.h; sourceTree = "<group>"; };
		A60DECF41FE3DB7E00FE2C42 /* CommonlyUsedPartyModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommonlyUsedPartyModel.m; sourceTree = "<group>"; };
		A60FFDED1FC2CD360097C11D /* InputDataViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InputDataViewController.h; sourceTree = "<group>"; };
		A60FFDEE1FC2CD360097C11D /* InputDataViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InputDataViewController.m; sourceTree = "<group>"; };
		A61044DF2057F4A20055B6B2 /* SearchDownloadViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SearchDownloadViewController.h; sourceTree = "<group>"; };
		A61044E02057F4A20055B6B2 /* SearchDownloadViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SearchDownloadViewController.m; sourceTree = "<group>"; };
		A6106C1C1FF34CB7006E5D32 /* SchedulingModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SchedulingModel.h; sourceTree = "<group>"; };
		A6106C1D1FF34CB7006E5D32 /* SchedulingModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SchedulingModel.m; sourceTree = "<group>"; };
		A6106C201FF39543006E5D32 /* MyDoctorModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyDoctorModel.h; sourceTree = "<group>"; };
		A6106C211FF39543006E5D32 /* MyDoctorModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyDoctorModel.m; sourceTree = "<group>"; };
		A6106C231FF3AAC2006E5D32 /* QueryModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QueryModel.h; sourceTree = "<group>"; };
		A6106C241FF3AAC2006E5D32 /* QueryModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QueryModel.m; sourceTree = "<group>"; };
		A6106C261FF3CA4B006E5D32 /* ResultSummaryModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ResultSummaryModel.h; sourceTree = "<group>"; };
		A6106C271FF3CA4B006E5D32 /* ResultSummaryModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ResultSummaryModel.m; sourceTree = "<group>"; };
		A61BCE3B1FF24E84006C2EF1 /* ArchitectureModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ArchitectureModel.h; sourceTree = "<group>"; };
		A61BCE3C1FF24E84006C2EF1 /* ArchitectureModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ArchitectureModel.m; sourceTree = "<group>"; };
		A61BCE3E1FF25129006C2EF1 /* TeamListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TeamListModel.h; sourceTree = "<group>"; };
		A61BCE3F1FF25129006C2EF1 /* TeamListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TeamListModel.m; sourceTree = "<group>"; };
		A61BCE411FF2574B006C2EF1 /* UserListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserListModel.h; sourceTree = "<group>"; };
		A61BCE421FF2574B006C2EF1 /* UserListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserListModel.m; sourceTree = "<group>"; };
		A61C693F1FCE56C100E7AB32 /* CommonlyUsedPartyViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommonlyUsedPartyViewController.h; sourceTree = "<group>"; };
		A61C69401FCE56C100E7AB32 /* CommonlyUsedPartyViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommonlyUsedPartyViewController.m; sourceTree = "<group>"; };
		A61C69451FCE59C500E7AB32 /* ServiceSettingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ServiceSettingViewController.h; sourceTree = "<group>"; };
		A61C69461FCE59C500E7AB32 /* ServiceSettingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ServiceSettingViewController.m; sourceTree = "<group>"; };
		A61C69481FCE5A8400E7AB32 /* MyAnnouncementViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyAnnouncementViewController.h; sourceTree = "<group>"; };
		A61C69491FCE5A8400E7AB32 /* MyAnnouncementViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyAnnouncementViewController.m; sourceTree = "<group>"; };
		A61C694B1FCE5B4300E7AB32 /* BlacklistViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlacklistViewController.h; sourceTree = "<group>"; };
		A61C694C1FCE5B4300E7AB32 /* BlacklistViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlacklistViewController.m; sourceTree = "<group>"; };
		A61C694E1FCE5BF400E7AB32 /* PharmacopoeiaViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PharmacopoeiaViewController.h; sourceTree = "<group>"; };
		A61C694F1FCE5BF400E7AB32 /* PharmacopoeiaViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PharmacopoeiaViewController.m; sourceTree = "<group>"; };
		A61C69511FCE975600E7AB32 /* PharmacopeiaModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PharmacopeiaModel.h; sourceTree = "<group>"; };
		A61C69521FCE975600E7AB32 /* PharmacopeiaModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PharmacopeiaModel.m; sourceTree = "<group>"; };
		A61C69551FCEAA8000E7AB32 /* CommonlyUsedPartyCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommonlyUsedPartyCell.h; sourceTree = "<group>"; };
		A61C69561FCEAA8000E7AB32 /* CommonlyUsedPartyCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommonlyUsedPartyCell.m; sourceTree = "<group>"; };
		A61F8E7F1FC6BB8500AB928E /* CameraManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CameraManager.h; sourceTree = "<group>"; };
		A61F8E801FC6BB8500AB928E /* CameraManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CameraManager.m; sourceTree = "<group>"; };
		A62003CF20901A9400D1B82F /* AvplayerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AvplayerViewController.h; sourceTree = "<group>"; };
		A62003D020901A9400D1B82F /* AvplayerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AvplayerViewController.m; sourceTree = "<group>"; };
		A620D4E920EF13FC0022A052 /* PhotoPrescAgreementViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PhotoPrescAgreementViewController.h; sourceTree = "<group>"; };
		A620D4EA20EF13FC0022A052 /* PhotoPrescAgreementViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PhotoPrescAgreementViewController.m; sourceTree = "<group>"; };
		A620D4EC20EF20370022A052 /* PhotoPrescViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PhotoPrescViewController.h; sourceTree = "<group>"; };
		A620D4ED20EF20370022A052 /* PhotoPrescViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PhotoPrescViewController.m; sourceTree = "<group>"; };
		A62459731F8F5AE900C88DD4 /* AboutBRZYViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AboutBRZYViewController.h; sourceTree = "<group>"; };
		A62459741F8F5AE900C88DD4 /* AboutBRZYViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AboutBRZYViewController.m; sourceTree = "<group>"; };
		A62459761F8F5C6100C88DD4 /* ToPushViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ToPushViewController.h; sourceTree = "<group>"; };
		A62459771F8F5C6100C88DD4 /* ToPushViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ToPushViewController.m; sourceTree = "<group>"; };
		A624597C1F8F640800C88DD4 /* WaitAcceptDoctorViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WaitAcceptDoctorViewController.h; sourceTree = "<group>"; };
		A624597D1F8F640800C88DD4 /* WaitAcceptDoctorViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WaitAcceptDoctorViewController.m; sourceTree = "<group>"; };
		A624597F1F8F64A300C88DD4 /* MyDoctorViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyDoctorViewController.h; sourceTree = "<group>"; };
		A62459801F8F64A300C88DD4 /* MyDoctorViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyDoctorViewController.m; sourceTree = "<group>"; };
		A62459821F8F664100C88DD4 /* ResultsQueryViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ResultsQueryViewController.h; sourceTree = "<group>"; };
		A62459831F8F664100C88DD4 /* ResultsQueryViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ResultsQueryViewController.m; sourceTree = "<group>"; };
		A62459851F8F673D00C88DD4 /* TaskAllocationViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TaskAllocationViewController.h; sourceTree = "<group>"; };
		A62459861F8F673D00C88DD4 /* TaskAllocationViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TaskAllocationViewController.m; sourceTree = "<group>"; };
		A62459881F8F715900C88DD4 /* WaitAcceptDoctorModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WaitAcceptDoctorModel.h; sourceTree = "<group>"; };
		A62459891F8F715900C88DD4 /* WaitAcceptDoctorModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WaitAcceptDoctorModel.m; sourceTree = "<group>"; };
		A624598B1F8F71CE00C88DD4 /* WaitAcceptDoctorCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WaitAcceptDoctorCell.h; sourceTree = "<group>"; };
		A624598C1F8F71CE00C88DD4 /* WaitAcceptDoctorCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WaitAcceptDoctorCell.m; sourceTree = "<group>"; };
		A625873E2004CB3A00259622 /* RegisterTF.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RegisterTF.h; sourceTree = "<group>"; };
		A625873F2004CB3A00259622 /* RegisterTF.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RegisterTF.m; sourceTree = "<group>"; };
		A6274F231FEE405D000BE82A /* CompleteModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CompleteModel.h; sourceTree = "<group>"; };
		A6274F241FEE405D000BE82A /* CompleteModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CompleteModel.m; sourceTree = "<group>"; };
		A6295B281FC959CF00BD1A53 /* OrderCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OrderCell.h; sourceTree = "<group>"; };
		A6295B291FC959CF00BD1A53 /* OrderCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OrderCell.m; sourceTree = "<group>"; };
		A62ABA8D1FD12F8D00668558 /* ReplaceTelViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReplaceTelViewController.h; sourceTree = "<group>"; };
		A62ABA8E1FD12F8D00668558 /* ReplaceTelViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReplaceTelViewController.m; sourceTree = "<group>"; };
		A635E7F91FBD2C810085E1D0 /* FindPasswordViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FindPasswordViewController.h; sourceTree = "<group>"; };
		A635E7FA1FBD2C810085E1D0 /* FindPasswordViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FindPasswordViewController.m; sourceTree = "<group>"; };
		A63695971FE7A79D002DFB0A /* SearchCommonViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SearchCommonViewController.h; sourceTree = "<group>"; };
		A63695981FE7A79D002DFB0A /* SearchCommonViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SearchCommonViewController.m; sourceTree = "<group>"; };
		A636959A1FE7DF5C002DFB0A /* BlackListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlackListCell.h; sourceTree = "<group>"; };
		A636959B1FE7DF5C002DFB0A /* BlackListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlackListCell.m; sourceTree = "<group>"; };
		A636959D1FE7E87C002DFB0A /* BlackListModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlackListModel.h; sourceTree = "<group>"; };
		A636959E1FE7E87C002DFB0A /* BlackListModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlackListModel.m; sourceTree = "<group>"; };
		A63F1FFC1FF5ED6B004A0DFA /* DownloadCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DownloadCell.h; sourceTree = "<group>"; };
		A63F1FFD1FF5ED6B004A0DFA /* DownloadCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DownloadCell.m; sourceTree = "<group>"; };
		A63F1FFF1FF62ED5004A0DFA /* DownloadModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DownloadModel.h; sourceTree = "<group>"; };
		A63F20001FF62ED5004A0DFA /* DownloadModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DownloadModel.m; sourceTree = "<group>"; };
		A642A8612137E8EF00C8FA8A /* GoodsContentViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GoodsContentViewController.h; sourceTree = "<group>"; };
		A642A8622137E8EF00C8FA8A /* GoodsContentViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GoodsContentViewController.m; sourceTree = "<group>"; };
		A64703B7204E302800F33B3D /* InvitaUnregisteredViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InvitaUnregisteredViewController.h; sourceTree = "<group>"; };
		A64703B8204E302800F33B3D /* InvitaUnregisteredViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InvitaUnregisteredViewController.m; sourceTree = "<group>"; };
		A64703BA204E310900F33B3D /* RegisterUnauthorizedViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RegisterUnauthorizedViewController.h; sourceTree = "<group>"; };
		A64703BB204E310A00F33B3D /* RegisterUnauthorizedViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RegisterUnauthorizedViewController.m; sourceTree = "<group>"; };
		A64703BD204E323C00F33B3D /* CertificationProcessViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CertificationProcessViewController.h; sourceTree = "<group>"; };
		A64703BE204E323C00F33B3D /* CertificationProcessViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CertificationProcessViewController.m; sourceTree = "<group>"; };
		A64703C0204E32CB00F33B3D /* CertificationFailViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CertificationFailViewController.h; sourceTree = "<group>"; };
		A64703C1204E32CB00F33B3D /* CertificationFailViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CertificationFailViewController.m; sourceTree = "<group>"; };
		A64703C3204E33AA00F33B3D /* CertificationNoPatientViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CertificationNoPatientViewController.h; sourceTree = "<group>"; };
		A64703C4204E33AA00F33B3D /* CertificationNoPatientViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CertificationNoPatientViewController.m; sourceTree = "<group>"; };
		A64703C6204E340F00F33B3D /* NoPrescriptionViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NoPrescriptionViewController.h; sourceTree = "<group>"; };
		A64703C7204E340F00F33B3D /* NoPrescriptionViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NoPrescriptionViewController.m; sourceTree = "<group>"; };
		A64703C9204E357F00F33B3D /* PrescriptionNoPayViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PrescriptionNoPayViewController.h; sourceTree = "<group>"; };
		A64703CA204E357F00F33B3D /* PrescriptionNoPayViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PrescriptionNoPayViewController.m; sourceTree = "<group>"; };
		A64703CC204E35E600F33B3D /* CertificationViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CertificationViewController.h; sourceTree = "<group>"; };
		A64703CD204E35E600F33B3D /* CertificationViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CertificationViewController.m; sourceTree = "<group>"; };
		A64703CF204E3F7700F33B3D /* AbnormalDoctorViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AbnormalDoctorViewController.h; sourceTree = "<group>"; };
		A64703D0204E3F7700F33B3D /* AbnormalDoctorViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AbnormalDoctorViewController.m; sourceTree = "<group>"; };
		A64703D3204E8E0A00F33B3D /* InvitaUnregisteredCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InvitaUnregisteredCell.h; sourceTree = "<group>"; };
		A64703D4204E8E0A00F33B3D /* InvitaUnregisteredCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InvitaUnregisteredCell.m; sourceTree = "<group>"; };
		A64703D6204E92F500F33B3D /* RegisterUnauthorizedCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RegisterUnauthorizedCell.h; sourceTree = "<group>"; };
		A64703D7204E92F500F33B3D /* RegisterUnauthorizedCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RegisterUnauthorizedCell.m; sourceTree = "<group>"; };
		A64703D9204EA20F00F33B3D /* CertificationProcessCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CertificationProcessCell.h; sourceTree = "<group>"; };
		A64703DA204EA20F00F33B3D /* CertificationProcessCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CertificationProcessCell.m; sourceTree = "<group>"; };
		A64761531F94B00000C84511 /* PatientNumberViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientNumberViewController.h; sourceTree = "<group>"; };
		A64761541F94B00000C84511 /* PatientNumberViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientNumberViewController.m; sourceTree = "<group>"; };
		A64761621F94B91800C84511 /* PatientNumberCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PatientNumberCell.h; sourceTree = "<group>"; };
		A64761631F94B91800C84511 /* PatientNumberCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PatientNumberCell.m; sourceTree = "<group>"; };
		A649543D1F8B72D600F16C42 /* QualificationCertificationViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QualificationCertificationViewController.h; sourceTree = "<group>"; };
		A649543E1F8B72D600F16C42 /* QualificationCertificationViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QualificationCertificationViewController.m; sourceTree = "<group>"; };
		A64954401F8B75E600F16C42 /* SecuritySettingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SecuritySettingViewController.h; sourceTree = "<group>"; };
		A64954411F8B75E600F16C42 /* SecuritySettingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SecuritySettingViewController.m; sourceTree = "<group>"; };
		A64954431F8B76DF00F16C42 /* CommonProblemsViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommonProblemsViewController.h; sourceTree = "<group>"; };
		A64954441F8B76DF00F16C42 /* CommonProblemsViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommonProblemsViewController.m; sourceTree = "<group>"; };
		A64954461F8B77B100F16C42 /* ProblemFeedbackViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ProblemFeedbackViewController.h; sourceTree = "<group>"; };
		A64954471F8B77B100F16C42 /* ProblemFeedbackViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ProblemFeedbackViewController.m; sourceTree = "<group>"; };
		A649544C1F8B791B00F16C42 /* AboutViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AboutViewController.h; sourceTree = "<group>"; };
		A649544D1F8B791B00F16C42 /* AboutViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AboutViewController.m; sourceTree = "<group>"; };
		A64C6CBD1FC0464500138EB1 /* PersonalDataHeaderCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PersonalDataHeaderCell.h; sourceTree = "<group>"; };
		A64C6CBE1FC0464500138EB1 /* PersonalDataHeaderCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PersonalDataHeaderCell.m; sourceTree = "<group>"; };
		A64C6CC01FC0476800138EB1 /* PersonalDataListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PersonalDataListCell.h; sourceTree = "<group>"; };
		A64C6CC11FC0476800138EB1 /* PersonalDataListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PersonalDataListCell.m; sourceTree = "<group>"; };
		A64E491A1F8C6BC50060CCDF /* PersonalDataViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PersonalDataViewController.h; sourceTree = "<group>"; };
		A64E491B1F8C6BC50060CCDF /* PersonalDataViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PersonalDataViewController.m; sourceTree = "<group>"; };
		A64F46F4204F806C004C5FAB /* DoctorStateModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DoctorStateModel.h; sourceTree = "<group>"; };
		A64F46F5204F806C004C5FAB /* DoctorStateModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DoctorStateModel.m; sourceTree = "<group>"; };
		A64F46F72050060F004C5FAB /* CertificationFailCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CertificationFailCell.h; sourceTree = "<group>"; };
		A64F46F82050060F004C5FAB /* CertificationFailCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CertificationFailCell.m; sourceTree = "<group>"; };
		A6504DE920ADA4D4001C00F2 /* SearchSummaryViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SearchSummaryViewController.h; sourceTree = "<group>"; };
		A6504DEA20ADA4D4001C00F2 /* SearchSummaryViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SearchSummaryViewController.m; sourceTree = "<group>"; };
		A660C9021FCBF25800D36C7C /* CompleteTotalOrGapCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CompleteTotalOrGapCell.h; sourceTree = "<group>"; };
		A660C9031FCBF25800D36C7C /* CompleteTotalOrGapCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CompleteTotalOrGapCell.m; sourceTree = "<group>"; };
		A660C9051FCBFE3C00D36C7C /* CompleteDetailCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CompleteDetailCell.h; sourceTree = "<group>"; };
		A660C9061FCBFE3C00D36C7C /* CompleteDetailCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CompleteDetailCell.m; sourceTree = "<group>"; };
		A660C9081FCC06D900D36C7C /* CompleteTotalCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CompleteTotalCell.h; sourceTree = "<group>"; };
		A660C9091FCC06D900D36C7C /* CompleteTotalCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CompleteTotalCell.m; sourceTree = "<group>"; };
		A661A7B420EE1D9B009CFD40 /* UIImageView+ZFCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+ZFCache.h"; sourceTree = "<group>"; };
		A661A7B520EE1D9B009CFD40 /* UIImageView+ZFCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+ZFCache.m"; sourceTree = "<group>"; };
		A661A7B620EE1D9B009CFD40 /* UIView+ZFFrame.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+ZFFrame.h"; sourceTree = "<group>"; };
		A661A7B720EE1D9B009CFD40 /* UIView+ZFFrame.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+ZFFrame.m"; sourceTree = "<group>"; };
		A661A7B820EE1D9B009CFD40 /* ZFLandScapeControlView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFLandScapeControlView.h; sourceTree = "<group>"; };
		A661A7B920EE1D9B009CFD40 /* ZFLandScapeControlView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFLandScapeControlView.m; sourceTree = "<group>"; };
		A661A7BA20EE1D9B009CFD40 /* ZFLoadingView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFLoadingView.h; sourceTree = "<group>"; };
		A661A7BB20EE1D9B009CFD40 /* ZFLoadingView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFLoadingView.m; sourceTree = "<group>"; };
		A661A7BC20EE1D9B009CFD40 /* ZFNetworkSpeedMonitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFNetworkSpeedMonitor.h; sourceTree = "<group>"; };
		A661A7BD20EE1D9B009CFD40 /* ZFNetworkSpeedMonitor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFNetworkSpeedMonitor.m; sourceTree = "<group>"; };
		A661A7BE20EE1D9B009CFD40 /* ZFPlayer.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = ZFPlayer.bundle; sourceTree = "<group>"; };
		A661A7BF20EE1D9B009CFD40 /* ZFPlayerControlView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFPlayerControlView.h; sourceTree = "<group>"; };
		A661A7C020EE1D9B009CFD40 /* ZFPlayerControlView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFPlayerControlView.m; sourceTree = "<group>"; };
		A661A7C120EE1D9B009CFD40 /* ZFPortraitControlView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFPortraitControlView.h; sourceTree = "<group>"; };
		A661A7C220EE1D9B009CFD40 /* ZFPortraitControlView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFPortraitControlView.m; sourceTree = "<group>"; };
		A661A7C320EE1D9B009CFD40 /* ZFSliderView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFSliderView.h; sourceTree = "<group>"; };
		A661A7C420EE1D9B009CFD40 /* ZFSliderView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFSliderView.m; sourceTree = "<group>"; };
		A661A7C520EE1D9B009CFD40 /* ZFSmallFloatControlView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFSmallFloatControlView.h; sourceTree = "<group>"; };
		A661A7C620EE1D9B009CFD40 /* ZFSmallFloatControlView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFSmallFloatControlView.m; sourceTree = "<group>"; };
		A661A7C720EE1D9B009CFD40 /* ZFSpeedLoadingView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFSpeedLoadingView.h; sourceTree = "<group>"; };
		A661A7C820EE1D9B009CFD40 /* ZFSpeedLoadingView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFSpeedLoadingView.m; sourceTree = "<group>"; };
		A661A7C920EE1D9B009CFD40 /* ZFUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFUtilities.h; sourceTree = "<group>"; };
		A661A7CA20EE1D9B009CFD40 /* ZFUtilities.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFUtilities.m; sourceTree = "<group>"; };
		A661A7CB20EE1D9B009CFD40 /* ZFVolumeBrightnessView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFVolumeBrightnessView.h; sourceTree = "<group>"; };
		A661A7CC20EE1D9B009CFD40 /* ZFVolumeBrightnessView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFVolumeBrightnessView.m; sourceTree = "<group>"; };
		A661A7CE20EE1D9C009CFD40 /* ZFIJKPlayerManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFIJKPlayerManager.h; sourceTree = "<group>"; };
		A661A7CF20EE1D9C009CFD40 /* ZFIJKPlayerManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFIJKPlayerManager.m; sourceTree = "<group>"; };
		A661A7D120EE1D9C009CFD40 /* KSMediaPlayerManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = KSMediaPlayerManager.h; sourceTree = "<group>"; };
		A661A7D220EE1D9C009CFD40 /* KSMediaPlayerManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = KSMediaPlayerManager.m; sourceTree = "<group>"; };
		A661A7D420EE1D9C009CFD40 /* UIScrollView+ZFPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIScrollView+ZFPlayer.h"; sourceTree = "<group>"; };
		A661A7D520EE1D9C009CFD40 /* UIScrollView+ZFPlayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIScrollView+ZFPlayer.m"; sourceTree = "<group>"; };
		A661A7D620EE1D9C009CFD40 /* UIViewController+ZFPlayerRotation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIViewController+ZFPlayerRotation.h"; sourceTree = "<group>"; };
		A661A7D720EE1D9C009CFD40 /* UIViewController+ZFPlayerRotation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIViewController+ZFPlayerRotation.m"; sourceTree = "<group>"; };
		A661A7D820EE1D9C009CFD40 /* ZFFloatView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFFloatView.h; sourceTree = "<group>"; };
		A661A7D920EE1D9C009CFD40 /* ZFFloatView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFFloatView.m; sourceTree = "<group>"; };
		A661A7DA20EE1D9C009CFD40 /* ZFKVOController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFKVOController.h; sourceTree = "<group>"; };
		A661A7DB20EE1D9C009CFD40 /* ZFKVOController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFKVOController.m; sourceTree = "<group>"; };
		A661A7DC20EE1D9C009CFD40 /* ZFOrientationObserver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFOrientationObserver.h; sourceTree = "<group>"; };
		A661A7DD20EE1D9C009CFD40 /* ZFOrientationObserver.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFOrientationObserver.m; sourceTree = "<group>"; };
		A661A7DE20EE1D9C009CFD40 /* ZFPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFPlayer.h; sourceTree = "<group>"; };
		A661A7DF20EE1D9C009CFD40 /* ZFPlayerController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFPlayerController.h; sourceTree = "<group>"; };
		A661A7E020EE1D9C009CFD40 /* ZFPlayerController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFPlayerController.m; sourceTree = "<group>"; };
		A661A7E120EE1D9C009CFD40 /* ZFPlayerGestureControl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFPlayerGestureControl.h; sourceTree = "<group>"; };
		A661A7E220EE1D9C009CFD40 /* ZFPlayerGestureControl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFPlayerGestureControl.m; sourceTree = "<group>"; };
		A661A7E320EE1D9C009CFD40 /* ZFPlayerLogManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFPlayerLogManager.h; sourceTree = "<group>"; };
		A661A7E420EE1D9C009CFD40 /* ZFPlayerLogManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFPlayerLogManager.m; sourceTree = "<group>"; };
		A661A7E520EE1D9C009CFD40 /* ZFPlayerMediaControl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFPlayerMediaControl.h; sourceTree = "<group>"; };
		A661A7E620EE1D9C009CFD40 /* ZFPlayerMediaPlayback.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFPlayerMediaPlayback.h; sourceTree = "<group>"; };
		A661A7E720EE1D9C009CFD40 /* ZFPlayerNotification.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFPlayerNotification.h; sourceTree = "<group>"; };
		A661A7E820EE1D9C009CFD40 /* ZFPlayerNotification.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFPlayerNotification.m; sourceTree = "<group>"; };
		A661A7E920EE1D9C009CFD40 /* ZFPlayerView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFPlayerView.h; sourceTree = "<group>"; };
		A661A7EA20EE1D9C009CFD40 /* ZFPlayerView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFPlayerView.m; sourceTree = "<group>"; };
		A661A7EB20EE1D9C009CFD40 /* ZFReachabilityManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFReachabilityManager.h; sourceTree = "<group>"; };
		A661A7EC20EE1D9C009CFD40 /* ZFReachabilityManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFReachabilityManager.m; sourceTree = "<group>"; };
		A661A7EE20EE1D9C009CFD40 /* ZFAVPlayerManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZFAVPlayerManager.h; sourceTree = "<group>"; };
		A661A7EF20EE1D9C009CFD40 /* ZFAVPlayerManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZFAVPlayerManager.m; sourceTree = "<group>"; };
		A66EAD301F9063AB0065ADC3 /* TaskAllocationCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TaskAllocationCell.h; sourceTree = "<group>"; };
		A66EAD311F9063AB0065ADC3 /* TaskAllocationCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TaskAllocationCell.m; sourceTree = "<group>"; };
		A66EAD331F90979E0065ADC3 /* MyDoctorHeaderCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyDoctorHeaderCell.h; sourceTree = "<group>"; };
		A66EAD341F90979E0065ADC3 /* MyDoctorHeaderCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyDoctorHeaderCell.m; sourceTree = "<group>"; };
		A66EAD361F909A400065ADC3 /* MyDoctorListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyDoctorListCell.h; sourceTree = "<group>"; };
		A66EAD371F909A400065ADC3 /* MyDoctorListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyDoctorListCell.m; sourceTree = "<group>"; };
		A66EAD391F90AABD0065ADC3 /* ResultsQueryCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ResultsQueryCell.h; sourceTree = "<group>"; };
		A66EAD3A1F90AABD0065ADC3 /* ResultsQueryCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ResultsQueryCell.m; sourceTree = "<group>"; };
		A67511DB1FD6CA4E00B6DDD3 /* CertificationSuccessViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CertificationSuccessViewController.h; sourceTree = "<group>"; };
		A67511DC1FD6CA4E00B6DDD3 /* CertificationSuccessViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CertificationSuccessViewController.m; sourceTree = "<group>"; };
		A67B75851FC51FC4005C637F /* OrderViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OrderViewController.h; sourceTree = "<group>"; };
		A67B75861FC51FC4005C637F /* OrderViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OrderViewController.m; sourceTree = "<group>"; };
		A67CF4B82058B9B900E2DEEA /* TwoWeeksNotPrescViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TwoWeeksNotPrescViewController.h; sourceTree = "<group>"; };
		A67CF4B92058B9B900E2DEEA /* TwoWeeksNotPrescViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TwoWeeksNotPrescViewController.m; sourceTree = "<group>"; };
		A67CF4BB2058BA0700E2DEEA /* PrescLessOneHundredViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PrescLessOneHundredViewController.h; sourceTree = "<group>"; };
		A67CF4BC2058BA0700E2DEEA /* PrescLessOneHundredViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PrescLessOneHundredViewController.m; sourceTree = "<group>"; };
		A67CF4BE205949B500E2DEEA /* SearchChooseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SearchChooseViewController.h; sourceTree = "<group>"; };
		A67CF4BF205949B500E2DEEA /* SearchChooseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SearchChooseViewController.m; sourceTree = "<group>"; };
		A68CE8A81FE22289008E5F05 /* OrdersModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OrdersModel.h; sourceTree = "<group>"; };
		A68CE8A91FE22289008E5F05 /* OrdersModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OrdersModel.m; sourceTree = "<group>"; };
		A68CE8AB1FE2684A008E5F05 /* DownloadViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DownloadViewController.h; sourceTree = "<group>"; };
		A68CE8AC1FE2684A008E5F05 /* DownloadViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DownloadViewController.m; sourceTree = "<group>"; };
		A68CE8AE1FE272C7008E5F05 /* CMOAssistantViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CMOAssistantViewController.h; sourceTree = "<group>"; };
		A68CE8AF1FE272C7008E5F05 /* CMOAssistantViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CMOAssistantViewController.m; sourceTree = "<group>"; };
		A68CE8B11FE275A1008E5F05 /* SchedulingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SchedulingViewController.h; sourceTree = "<group>"; };
		A68CE8B21FE275A1008E5F05 /* SchedulingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SchedulingViewController.m; sourceTree = "<group>"; };
		A68CE8B41FE275F8008E5F05 /* ArchitectureViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ArchitectureViewController.h; sourceTree = "<group>"; };
		A68CE8B51FE275F8008E5F05 /* ArchitectureViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ArchitectureViewController.m; sourceTree = "<group>"; };
		A68CE8B81FE277A8008E5F05 /* SchedulingCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SchedulingCell.h; sourceTree = "<group>"; };
		A68CE8B91FE277A8008E5F05 /* SchedulingCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SchedulingCell.m; sourceTree = "<group>"; };
		A68FA2791FD5435800DAF689 /* SeeTheSampleViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SeeTheSampleViewController.h; sourceTree = "<group>"; };
		A68FA27A1FD5435800DAF689 /* SeeTheSampleViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SeeTheSampleViewController.m; sourceTree = "<group>"; };
		A691B0751FD8E29300D29516 /* IsReviewingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IsReviewingViewController.h; sourceTree = "<group>"; };
		A691B0761FD8E29300D29516 /* IsReviewingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IsReviewingViewController.m; sourceTree = "<group>"; };
		A69A30131FFB90AD007E9413 /* ScanQrcodeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ScanQrcodeViewController.h; sourceTree = "<group>"; };
		A69A30141FFB90AD007E9413 /* ScanQrcodeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ScanQrcodeViewController.m; sourceTree = "<group>"; };
		A69A30161FFBA7FA007E9413 /* AllocationViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AllocationViewController.h; sourceTree = "<group>"; };
		A69A30171FFBA7FA007E9413 /* AllocationViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AllocationViewController.m; sourceTree = "<group>"; };
		A69A30191FFBAEDB007E9413 /* AllocationCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AllocationCell.h; sourceTree = "<group>"; };
		A69A301A1FFBAEDB007E9413 /* AllocationCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AllocationCell.m; sourceTree = "<group>"; };
		A69A301C1FFBB9C0007E9413 /* ArchitectureCollectionViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ArchitectureCollectionViewCell.h; sourceTree = "<group>"; };
		A69A301D1FFBB9C0007E9413 /* ArchitectureCollectionViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ArchitectureCollectionViewCell.m; sourceTree = "<group>"; };
		A69F62D71F7CF5F9004B7AE8 /* UserCenterHeaderCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserCenterHeaderCell.h; sourceTree = "<group>"; };
		A69F62D81F7CF5F9004B7AE8 /* UserCenterHeaderCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserCenterHeaderCell.m; sourceTree = "<group>"; };
		A6A1CD511FEA073D00044E3C /* CommonProblemsModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommonProblemsModel.h; sourceTree = "<group>"; };
		A6A1CD521FEA073D00044E3C /* CommonProblemsModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommonProblemsModel.m; sourceTree = "<group>"; };
		A6A4CF911FB942D300B545F6 /* BrzyAgreementViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BrzyAgreementViewController.h; sourceTree = "<group>"; };
		A6A4CF921FB942D300B545F6 /* BrzyAgreementViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BrzyAgreementViewController.m; sourceTree = "<group>"; };
		A6A574D8213528FD007F90AE /* OrderContentViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OrderContentViewController.h; sourceTree = "<group>"; };
		A6A574D9213528FD007F90AE /* OrderContentViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OrderContentViewController.m; sourceTree = "<group>"; };
		A6A6A82E1FE93FBD00249235 /* CommonProblemsCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommonProblemsCell.h; sourceTree = "<group>"; };
		A6A6A82F1FE93FBD00249235 /* CommonProblemsCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommonProblemsCell.m; sourceTree = "<group>"; };
		A6C45FAB1F96F64900F85928 /* DoctorDetailsViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DoctorDetailsViewController.h; sourceTree = "<group>"; };
		A6C45FAC1F96F64900F85928 /* DoctorDetailsViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DoctorDetailsViewController.m; sourceTree = "<group>"; };
		A6C45FAE1F96FFAC00F85928 /* DoctorDetailCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DoctorDetailCell.h; sourceTree = "<group>"; };
		A6C45FAF1F96FFAC00F85928 /* DoctorDetailCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DoctorDetailCell.m; sourceTree = "<group>"; };
		A6C45FB11F9735B100F85928 /* RegisterViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RegisterViewController.h; sourceTree = "<group>"; };
		A6C45FB21F9735B100F85928 /* RegisterViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RegisterViewController.m; sourceTree = "<group>"; };
		A6C74F24212D07D800BC2AD7 /* OrderRecordViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OrderRecordViewController.h; sourceTree = "<group>"; };
		A6C74F25212D07D800BC2AD7 /* OrderRecordViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OrderRecordViewController.m; sourceTree = "<group>"; };
		A6C74F27212D495F00BC2AD7 /* OrderRecordView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OrderRecordView.h; sourceTree = "<group>"; };
		A6C74F28212D495F00BC2AD7 /* OrderRecordView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OrderRecordView.m; sourceTree = "<group>"; };
		A6C74F2A212D5C2600BC2AD7 /* OrderRecordCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OrderRecordCell.h; sourceTree = "<group>"; };
		A6C74F2B212D5C2600BC2AD7 /* OrderRecordCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OrderRecordCell.m; sourceTree = "<group>"; };
		A6D9E7D41FFE4BF600694EC4 /* UITabBar+bagde.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UITabBar+bagde.h"; sourceTree = "<group>"; };
		A6D9E7D51FFE4BF600694EC4 /* UITabBar+bagde.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UITabBar+bagde.m"; sourceTree = "<group>"; };
		A6E4212720AE7AB700321AB4 /* SummaryMonthDetailViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SummaryMonthDetailViewController.h; sourceTree = "<group>"; };
		A6E4212820AE7AB700321AB4 /* SummaryMonthDetailViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SummaryMonthDetailViewController.m; sourceTree = "<group>"; };
		A6E4212A20AE82CB00321AB4 /* SummaryMonthViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SummaryMonthViewController.h; sourceTree = "<group>"; };
		A6E4212B20AE82CB00321AB4 /* SummaryMonthViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SummaryMonthViewController.m; sourceTree = "<group>"; };
		A6F85C58212E6D2E00780DA6 /* OrderRecordModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OrderRecordModel.h; sourceTree = "<group>"; };
		A6F85C59212E6D2E00780DA6 /* OrderRecordModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OrderRecordModel.m; sourceTree = "<group>"; };
		A6F85C5B212EB6FA00780DA6 /* CompletedView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CompletedView.h; sourceTree = "<group>"; };
		A6F85C5C212EB6FA00780DA6 /* CompletedView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CompletedView.m; sourceTree = "<group>"; };
		A6F85C5E212EBCEE00780DA6 /* CompletedCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CompletedCell.h; sourceTree = "<group>"; };
		A6F85C5F212EBCEE00780DA6 /* CompletedCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CompletedCell.m; sourceTree = "<group>"; };
		A6FC85251FF4915D005B6CA8 /* TaskAllocationModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TaskAllocationModel.h; sourceTree = "<group>"; };
		A6FC85261FF4915D005B6CA8 /* TaskAllocationModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TaskAllocationModel.m; sourceTree = "<group>"; };
		A6FC85281FF4E35A005B6CA8 /* ChooseAgentViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChooseAgentViewController.h; sourceTree = "<group>"; };
		A6FC85291FF4E35A005B6CA8 /* ChooseAgentViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChooseAgentViewController.m; sourceTree = "<group>"; };
		A6FC852B1FF4E416005B6CA8 /* ChooseAgentCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChooseAgentCell.h; sourceTree = "<group>"; };
		A6FC852C1FF4E416005B6CA8 /* ChooseAgentCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChooseAgentCell.m; sourceTree = "<group>"; };
		A6FC852E1FF4EB15005B6CA8 /* ChooseAgentmodel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ChooseAgentmodel.h; sourceTree = "<group>"; };
		A6FC852F1FF4EB15005B6CA8 /* ChooseAgentmodel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ChooseAgentmodel.m; sourceTree = "<group>"; };
		A6FF5D8E204D38FE00261A8F /* DoctorStateCollecCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DoctorStateCollecCell.h; sourceTree = "<group>"; };
		A6FF5D8F204D38FE00261A8F /* DoctorStateCollecCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DoctorStateCollecCell.m; sourceTree = "<group>"; };
		BCF38E31BD45249EAE704F9D /* Pods-BRZY.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-BRZY.debug.xcconfig"; path = "Pods/Target Support Files/Pods-BRZY/Pods-BRZY.debug.xcconfig"; sourceTree = "<group>"; };
		F3DFD5232C69E696D14262AA /* Pods-BRZY.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-BRZY.release.xcconfig"; path = "Pods/Target Support Files/Pods-BRZY/Pods-BRZY.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7742ECA71F4BD7ED00A9B110 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				0A31246120049A1D00D56D36 /* SystemConfiguration.framework in Frameworks */,
				77089A922CF09B3E00C9E6C3 /* PhotosUI.framework in Frameworks */,
				779FC82B2DF5C7F4001CA5D3 /* YTXMonitor.framework in Frameworks */,
				779FC82C2DF5C7F4001CA5D3 /* YTXOperators.framework in Frameworks */,
				779FC82D2DF5C7F4001CA5D3 /* ATAuthSDK.framework in Frameworks */,
				77089A932CF09B4700C9E6C3 /* CoreML.framework in Frameworks */,
				77089A912CF09B3E00C9E6C3 /* Photos.framework in Frameworks */,
				0A31246720049AD400D56D36 /* libz.tbd in Frameworks */,
				77089A882CF0996900C9E6C3 /* CoreML.framework in Frameworks */,
				0A31245F20049A0E00D56D36 /* CoreTelephony.framework in Frameworks */,
				0A31246A20049AF300D56D36 /* libresolv.tbd in Frameworks */,
				0A31245D200499D000D56D36 /* CoreFoundation.framework in Frameworks */,
				0A31245B200499B100D56D36 /* CFNetwork.framework in Frameworks */,
				FB82BBB2CE596EA49456D763 /* Pods_BRZY.framework in Frameworks */,
				77089A822CF0994D00C9E6C3 /* Accelerate.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0A29991F1FCBB344003C5CAA /* Add drug */ = {
			isa = PBXGroup;
			children = (
				0A2999201FCBB38B003C5CAA /* BRSearchModel.h */,
				0A2999211FCBB38B003C5CAA /* BRSearchModel.m */,
				0A097E411FCFDF4F00A1BFEF /* BRPatientModel.h */,
				0A097E421FCFDF4F00A1BFEF /* BRPatientModel.m */,
				0A9A1BFA1FE13CB600F5DA0E /* BRPresInfoModel.h */,
				0A9A1BFB1FE13CB600F5DA0E /* BRPresInfoModel.m */,
				0A73F46E1FE51A9000125F2D /* BRRiskTipModel.h */,
				0A73F46F1FE51A9000125F2D /* BRRiskTipModel.m */,
			);
			name = "Add drug";
			sourceTree = "<group>";
		};
		0A32B6F01F580CEB00E51EE0 /* Prescription */ = {
			isa = PBXGroup;
			children = (
				0A9CED161F7B39AF0072CF4A /* Model */,
				0A9CED151F7B39A20072CF4A /* View */,
				0A9CED141F7B39810072CF4A /* Controller */,
			);
			name = Prescription;
			sourceTree = "<group>";
		};
		0A69BE1C1FD2AC76009F8630 /* BRPresSelectTypeView */ = {
			isa = PBXGroup;
			children = (
				0A69BE191FD29E48009F8630 /* BRPresSelectTypeView.h */,
				0A69BE1A1FD29E48009F8630 /* BRPresSelectTypeView.m */,
				0A69BE1D1FD2ACD8009F8630 /* BRPresSelectTypeMasterCell.h */,
				0A69BE1E1FD2ACD8009F8630 /* BRPresSelectTypeMasterCell.m */,
				0A42DD141FD7DEFA0060D4F3 /* BRPresSelectTypeDetailCell.h */,
				0A42DD151FD7DEFA0060D4F3 /* BRPresSelectTypeDetailCell.m */,
			);
			name = BRPresSelectTypeView;
			sourceTree = "<group>";
		};
		0A71DB121F9466B200BF831F /* AddDrugVC */ = {
			isa = PBXGroup;
			children = (
				773E8AF52CD9E6030096F0A5 /* AdjustByMultiple */,
				0AA894DB1FDEAB7D0034C3A2 /* BRPresReplaceDrugView */,
				0A71DB131F948F1800BF831F /* BRPresInputView.h */,
				0A71DB141F948F1800BF831F /* BRPresInputView.m */,
				0A71DB1F1F976DDF00BF831F /* BRPresNoticeView.h */,
				0A71DB201F976DDF00BF831F /* BRPresNoticeView.m */,
				0A6F253F1FB3FF8D001D4FC9 /* BRPrescriptionInputPanel.h */,
				0A6F25401FB3FF8D001D4FC9 /* BRPrescriptionInputPanel.m */,
				0F57A577204D27FA002914A9 /* BRPrescriptionToolBar.h */,
				0F57A578204D27FA002914A9 /* BRPrescriptionToolBar.m */,
				0ABEE6531FC672A6006FE61B /* BRZYTextField.h */,
				0ABEE6541FC672A6006FE61B /* BRZYTextField.m */,
				0ABEE6501FC67073006FE61B /* BRPresDrugCell.h */,
				0ABEE6511FC67073006FE61B /* BRPresDrugCell.m */,
				0ACF4F231FC943A400A3B87F /* BRPresHaveNoticeCell.h */,
				0ACF4F241FC943A400A3B87F /* BRPresHaveNoticeCell.m */,
				0A69BE161FD246D5009F8630 /* BRPresInfoView.h */,
				0A69BE171FD246D5009F8630 /* BRPresInfoView.m */,
				77B3A54B2D24465000097E8A /* BRDrugSpecSelectView.h */,
				77B3A54C2D24465000097E8A /* BRDrugSpecSelectView.m */,
			);
			name = AddDrugVC;
			sourceTree = "<group>";
		};
		0A9CED141F7B39810072CF4A /* Controller */ = {
			isa = PBXGroup;
			children = (
				77B3A54E2D24479A00097E8A /* TCMMatchResult.h */,
				77B3A54F2D24479A00097E8A /* TCMMatchResult.m */,
				77C29328293C29CC001A5B45 /* View */,
				7700F34E1FCBE4580059BA14 /* MedicatedInfo */,
				7735EF361F62A9D4003CE02D /* PatientDocumentViewController.h */,
				7735EF371F62A9D4003CE02D /* PatientDocumentViewController.m */,
				0A5F4D4F1FBC3B5F00491FE7 /* PrescriptionViewController.h */,
				0A5F4D501FBC3B5F00491FE7 /* PrescriptionViewController.m */,
				0A71DB0F1F94668C00BF831F /* AddDrugViewController.h */,
				0A71DB101F94668C00BF831F /* AddDrugViewController.m */,
				0A89E71D1FF0A24100834D48 /* FactoryInfoViewController.h */,
				0A89E71E1FF0A24100834D48 /* FactoryInfoViewController.m */,
				77A5461F1FFE46020086EFD8 /* InterrogationAndVisitViewController.h */,
				77A546201FFE46020086EFD8 /* InterrogationAndVisitViewController.m */,
				77494F2828A71AEE0000132A /* BRQuickPrescribeViewController.h */,
				77494F2928A71AEE0000132A /* BRQuickPrescribeViewController.m */,
				77C29325293B0995001A5B45 /* DrugDetailListViewController.h */,
				77C29326293B0995001A5B45 /* DrugDetailListViewController.m */,
				7793B9622C15F1DD006AD7FF /* BRMessagePrescribeViewController.h */,
				7793B9632C15F1DD006AD7FF /* BRMessagePrescribeViewController.m */,
				777418962CEF208B00392B18 /* BRIntelligentEntryViewController.h */,
				777418972CEF208B00392B18 /* BRIntelligentEntryViewController.m */,
				777B87982CFC15B900004875 /* CustomBoilyWayViewController.h */,
				777B87992CFC15B900004875 /* CustomBoilyWayViewController.m */,
			);
			name = Controller;
			sourceTree = "<group>";
		};
		0A9CED151F7B39A20072CF4A /* View */ = {
			isa = PBXGroup;
			children = (
				77494F2B28A7225B0000132A /* QuickPrescribe */,
				0FB534A820F4884300E084B7 /* MedicatedInfo */,
				771F66EC1FC808CB002AAA5C /* PatientDocument */,
				0A71DB121F9466B200BF831F /* AddDrugVC */,
				0A9CED171F7B39DD0072CF4A /* PrescriptionVC */,
			);
			name = View;
			sourceTree = "<group>";
		};
		0A9CED161F7B39AF0072CF4A /* Model */ = {
			isa = PBXGroup;
			children = (
				773D91442CF5D97700547A91 /* BRIntelligent */,
				7761534A1FCD30A7006A2FAC /* PatientDocumentModel */,
				0A29991F1FCBB344003C5CAA /* Add drug */,
				0A9CED271F7CE3760072CF4A /* PrescriptionVC */,
			);
			name = Model;
			sourceTree = "<group>";
		};
		0A9CED171F7B39DD0072CF4A /* PrescriptionVC */ = {
			isa = PBXGroup;
			children = (
				0A69BE1C1FD2AC76009F8630 /* BRPresSelectTypeView */,
				0A9CED181F7B3A380072CF4A /* BRPrescriptionTitleView.h */,
				0A9CED191F7B3A380072CF4A /* BRPrescriptionTitleView.m */,
				0A5F4D521FBC486800491FE7 /* BRPrescriptionPatientView.h */,
				0A5F4D531FBC486800491FE7 /* BRPrescriptionPatientView.m */,
				0A9CED1B1F7B7BFF0072CF4A /* BRPrescriptionDiagnosesView.h */,
				0A9CED1C1F7B7BFF0072CF4A /* BRPrescriptionDiagnosesView.m */,
				0A9CED2B1F7CE6C40072CF4A /* BRPrescriptionDrugCell.h */,
				0A9CED2C1F7CE6C40072CF4A /* BRPrescriptionDrugCell.m */,
				0A9CED211F7CE1F20072CF4A /* BRPrescriptionDrugListView.h */,
				0A9CED221F7CE1F20072CF4A /* BRPrescriptionDrugListView.m */,
				0AF203241F8B6E8F00432DD6 /* BRPresDescTitleView.h */,
				0AF203251F8B6E8F00432DD6 /* BRPresDescTitleView.m */,
				0AF203211F8B699700432DD6 /* BRPrescriptionDescView.h */,
				0AF203221F8B699700432DD6 /* BRPrescriptionDescView.m */,
				0A9CED1E1F7CD28C0072CF4A /* BRPrescriptionDisplayView.h */,
				0A9CED1F1F7CD28C0072CF4A /* BRPrescriptionDisplayView.m */,
				0A5F4D551FBEF18500491FE7 /* BRPresUsageView.h */,
				0A5F4D561FBEF18500491FE7 /* BRPresUsageView.m */,
				0AF203271F8CBB0300432DD6 /* BRUnderlineTextField.h */,
				0AF203281F8CBB0300432DD6 /* BRUnderlineTextField.m */,
				0A9202071F8F6DC10053B7D2 /* BRUnderlineRedTextField.h */,
				0A9202081F8F6DC10053B7D2 /* BRUnderlineRedTextField.m */,
				0A5F4D581FC0313800491FE7 /* BRPresOtherView.h */,
				0A5F4D591FC0313800491FE7 /* BRPresOtherView.m */,
				0ABEE64D1FC2B551006FE61B /* BRPresNoteView.h */,
				0ABEE64E1FC2B551006FE61B /* BRPresNoteView.m */,
				0AF2032A1F8CBF8800432DD6 /* BRPrescriptionChargeView.h */,
				0AF2032B1F8CBF8800432DD6 /* BRPrescriptionChargeView.m */,
				0AF2032D1F8CD48C00432DD6 /* BRPresOfflineView.h */,
				0AF2032E1F8CD48C00432DD6 /* BRPresOfflineView.m */,
				0A8CFB3A1FC7B704006A449E /* BRPresContraindicationView.h */,
				0A8CFB3B1FC7B704006A449E /* BRPresContraindicationView.m */,
				0A99260F1FCD6800003B537E /* BRPresAddPatientView.h */,
				0A9926101FCD6800003B537E /* BRPresAddPatientView.m */,
				0A7FD4661FCE8DCC00318462 /* BRDatePicker.h */,
				0A7FD4671FCE8DCC00318462 /* BRDatePicker.m */,
			);
			name = PrescriptionVC;
			sourceTree = "<group>";
		};
		0A9CED271F7CE3760072CF4A /* PrescriptionVC */ = {
			isa = PBXGroup;
			children = (
				7785B28F2E224E9F006F68FC /* BRPackageSpecModel.h */,
				7785B2902E224E9F006F68FC /* BRPackageSpecModel.m */,
				0A9CED241F7CE36F0072CF4A /* BRPrescriptionModel.h */,
				0A9CED251F7CE36F0072CF4A /* BRPrescriptionModel.m */,
				0A9CED281F7CE5EE0072CF4A /* BRMedicineModel.h */,
				0A9CED291F7CE5EE0072CF4A /* BRMedicineModel.m */,
				0A71DB1C1F959E6300BF831F /* BRSubMedicineModel.h */,
				0A71DB1D1F959E6300BF831F /* BRSubMedicineModel.m */,
				0A69E5BA1FD940BE003342A6 /* BRFactoryModel.h */,
				0A69E5BB1FD940BE003342A6 /* BRFactoryModel.m */,
				0A69E5BD1FD946E7003342A6 /* BRSubFactoryModel.h */,
				0A69E5BE1FD946E7003342A6 /* BRSubFactoryModel.m */,
				0AADC48B1FFF9A96008F29D6 /* BRContraindicationModel.h */,
				0AADC48C1FFF9A96008F29D6 /* BRContraindicationModel.m */,
				772F70D5205139FE004BFE4D /* BRStockInfoModel.h */,
				772F70D6205139FE004BFE4D /* BRStockInfoModel.m */,
			);
			name = PrescriptionVC;
			sourceTree = "<group>";
		};
		0AA18C751FF33A9D00CC3C61 /* ZYActivitiesView */ = {
			isa = PBXGroup;
			children = (
				0AA18C761FF33A9D00CC3C61 /* ZYActivitiesView.h */,
				0AA18C771FF33A9D00CC3C61 /* ZYActivitiesView.m */,
				0AA18C781FF33A9D00CC3C61 /* ZYActivitiesViewController.h */,
				0AA18C791FF33A9D00CC3C61 /* ZYActivitiesViewController.m */,
				0AA18C7A1FF33A9D00CC3C61 /* ZYActivityImgView.h */,
				0AA18C7B1FF33A9D00CC3C61 /* ZYActivityImgView.m */,
				0AA18C7C1FF33A9D00CC3C61 /* ZYActivityModel.h */,
				0AA18C7D1FF33A9D00CC3C61 /* ZYActivityModel.m */,
				0AA18C7E1FF33A9D00CC3C61 /* ZYPageControl.h */,
				0AA18C7F1FF33A9D00CC3C61 /* ZYPageControl.m */,
				0AA18C801FF33A9D00CC3C61 /* ZYScrollView.h */,
				0AA18C811FF33A9D00CC3C61 /* ZYScrollView.m */,
			);
			path = ZYActivitiesView;
			sourceTree = "<group>";
		};
		0AA894DB1FDEAB7D0034C3A2 /* BRPresReplaceDrugView */ = {
			isa = PBXGroup;
			children = (
				0AA894D81FDEA2B60034C3A2 /* BRPresReplaceDrugView.h */,
				0AA894D91FDEA2B60034C3A2 /* BRPresReplaceDrugView.m */,
				0AA894DC1FDEABAF0034C3A2 /* BRPresReplaceDrugCell.h */,
				0AA894DD1FDEABAF0034C3A2 /* BRPresReplaceDrugCell.m */,
				0A97FEDB1FE263F50043D14D /* BRPresReplaceDrugTitleCell.h */,
				0A97FEDC1FE263F50043D14D /* BRPresReplaceDrugTitleCell.m */,
			);
			name = BRPresReplaceDrugView;
			sourceTree = "<group>";
		};
		0ACF4F261FC974A500A3B87F /* MMNumberKeyboard */ = {
			isa = PBXGroup;
			children = (
				0ACF4F271FC974A500A3B87F /* MMNumberKeyboard.h */,
				0ACF4F281FC974A500A3B87F /* MMNumberKeyboard.m */,
			);
			path = MMNumberKeyboard;
			sourceTree = "<group>";
		};
		0F41901D204FEBC7005B81D3 /* BRGuidePageView */ = {
			isa = PBXGroup;
			children = (
				0F41901E204FEC73005B81D3 /* BRGuidePageView.h */,
				0F41901F204FEC73005B81D3 /* BRGuidePageView.m */,
			);
			path = BRGuidePageView;
			sourceTree = "<group>";
		};
		0FB534A820F4884300E084B7 /* MedicatedInfo */ = {
			isa = PBXGroup;
			children = (
				0FB534A920F488B200E084B7 /* BRCanTapImgView.h */,
				0FB534AA20F488B200E084B7 /* BRCanTapImgView.m */,
			);
			name = MedicatedInfo;
			sourceTree = "<group>";
		};
		0FB534AF20F4A3A600E084B7 /* photoPres */ = {
			isa = PBXGroup;
			children = (
				0FB534B020F4A40100E084B7 /* PhotoPresCell.h */,
				0FB534B120F4A40100E084B7 /* PhotoPresCell.m */,
				0F43AEEF20FF352500D568B5 /* PhotoPresWaitCell.h */,
				0F43AEF020FF352500D568B5 /* PhotoPresWaitCell.m */,
				0FB534B620F4B61600E084B7 /* PhotoTableView.h */,
				0FB534B720F4B61600E084B7 /* PhotoTableView.m */,
				0FB534BF20F5A2FD00E084B7 /* BRSegmentView.h */,
				0FB534C020F5A2FD00E084B7 /* BRSegmentView.m */,
			);
			name = photoPres;
			sourceTree = "<group>";
		};
		1506106EEDCD02E407BEA481 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				77089A872CF0996900C9E6C3 /* CoreML.framework */,
				77089A852CF0996200C9E6C3 /* PhotosUI.framework */,
				77089A832CF0995A00C9E6C3 /* Photos.framework */,
				77089A812CF0994D00C9E6C3 /* Accelerate.framework */,
				77089A7B2CF098E700C9E6C3 /* OcrSDK.bundle */,
				77089A712CF098C100C9E6C3 /* OcrSDKKit.framework */,
				77089A6E2CF098C100C9E6C3 /* tiny_opencv2.framework */,
				77089A702CF098C100C9E6C3 /* tnn.framework */,
				77089A6F2CF098C100C9E6C3 /* YTImageRefiner.framework */,
				0A31246920049AF200D56D36 /* libresolv.tbd */,
				0A31246820049AE600D56D36 /* UserNotifications.framework */,
				0A31246620049AD400D56D36 /* libz.tbd */,
				0A31246520049AC800D56D36 /* Security.framework */,
				0A31246420049A6200D56D36 /* UIKit.framework */,
				0A31246320049A5300D56D36 /* CoreGraphics.framework */,
				0A31246220049A4100D56D36 /* Foundation.framework */,
				0A31246020049A1700D56D36 /* SystemConfiguration.framework */,
				0A31245E200499DE00D56D36 /* CoreTelephony.framework */,
				0A31245C200499BE00D56D36 /* CoreFoundation.framework */,
				0A31245A2004999E00D56D36 /* CFNetwork.framework */,
				89DB2C7A093AA53BF55F5BBE /* Pods_BRZY.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		277921CA94E312263655F635 /* Pods */ = {
			isa = PBXGroup;
			children = (
				BCF38E31BD45249EAE704F9D /* Pods-BRZY.debug.xcconfig */,
				F3DFD5232C69E696D14262AA /* Pods-BRZY.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		7700F34E1FCBE4580059BA14 /* MedicatedInfo */ = {
			isa = PBXGroup;
			children = (
				7700F34F1FCBE4920059BA14 /* MedicatedInfoViewController.h */,
				7700F3501FCBE4920059BA14 /* MedicatedInfoViewController.m */,
			);
			path = MedicatedInfo;
			sourceTree = "<group>";
		};
		77089A7E2CF0990900C9E6C3 /* TencentORC */ = {
			isa = PBXGroup;
			children = (
				77089A892CF09AC400C9E6C3 /* OcrSDKKit.framework */,
				77089A8A2CF09AC400C9E6C3 /* tiny_opencv2.framework */,
				77089A8B2CF09AC400C9E6C3 /* tnn.framework */,
				77089A8C2CF09AC400C9E6C3 /* YTImageRefiner.framework */,
				77089A7F2CF0991C00C9E6C3 /* OcrSDK.bundle */,
			);
			path = TencentORC;
			sourceTree = "<group>";
		};
		770CBCE21FDFCFFD00C27AB3 /* frequent question */ = {
			isa = PBXGroup;
			children = (
				770CBCE91FDFDC2300C27AB3 /* FrequentQuestionShowView.h */,
				770CBCEA1FDFDC2300C27AB3 /* FrequentQuestionShowView.m */,
				770CBCE31FDFD02400C27AB3 /* QuestionTypeCell.h */,
				770CBCE41FDFD02400C27AB3 /* QuestionTypeCell.m */,
				770CBCE61FDFD04100C27AB3 /* QuestionListCell.h */,
				770CBCE71FDFD04100C27AB3 /* QuestionListCell.m */,
				7736B9421FE100F700C55A50 /* QuestionAddOrEditListCell.h */,
				7736B9431FE100F700C55A50 /* QuestionAddOrEditListCell.m */,
			);
			name = "frequent question";
			sourceTree = "<group>";
		};
		770D3E17200753AF00D48C5A /* ShareView */ = {
			isa = PBXGroup;
			children = (
				770D3E18200753CF00D48C5A /* BRShareView.h */,
				770D3E19200753CF00D48C5A /* BRShareView.m */,
			);
			path = ShareView;
			sourceTree = "<group>";
		};
		770FE9591FF33CFE00008C78 /* ShowMyPurseSMSCode */ = {
			isa = PBXGroup;
			children = (
				770FE95A1FF33D1800008C78 /* ShowMyPurseSMSCode.h */,
				770FE95B1FF33D1800008C78 /* ShowMyPurseSMSCode.m */,
			);
			path = ShowMyPurseSMSCode;
			sourceTree = "<group>";
		};
		7710B0961FA09AA300C9CBE5 /* AMR */ = {
			isa = PBXGroup;
			children = (
				77A7DF9A1FA0A10F0012C18C /* AACRecord.h */,
				77A7DF9B1FA0A10F0012C18C /* AACRecord.m */,
				77A7DF971FA09F520012C18C /* EWVoiceHUD.h */,
				77A7DF981FA09F520012C18C /* EWVoiceHUD.m */,
				7733342D1FA2FE6F00F996A0 /* BRPlayer.h */,
				7733342E1FA2FE6F00F996A0 /* BRPlayer.m */,
				7710B0A01FA09ABE00C9CBE5 /* lib */,
				7710B09F1FA09AB800C9CBE5 /* include */,
			);
			path = AMR;
			sourceTree = "<group>";
		};
		7710B09F1FA09AB800C9CBE5 /* include */ = {
			isa = PBXGroup;
			children = (
				7710B0A21FA09AD900C9CBE5 /* opencore-amrwb */,
				7710B0A11FA09ACA00C9CBE5 /* opencore-amrnb */,
			);
			path = include;
			sourceTree = "<group>";
		};
		7710B0A01FA09ABE00C9CBE5 /* lib */ = {
			isa = PBXGroup;
			children = (
				7710B0A71FA09B3100C9CBE5 /* libopencore-amrnb.a */,
				7710B0A81FA09B3100C9CBE5 /* libopencore-amrwb.a */,
			);
			path = lib;
			sourceTree = "<group>";
		};
		7710B0A11FA09ACA00C9CBE5 /* opencore-amrnb */ = {
			isa = PBXGroup;
			children = (
				7710B0A31FA09AED00C9CBE5 /* interf_dec.h */,
				7710B0A41FA09AED00C9CBE5 /* interf_enc.h */,
			);
			path = "opencore-amrnb";
			sourceTree = "<group>";
		};
		7710B0A21FA09AD900C9CBE5 /* opencore-amrwb */ = {
			isa = PBXGroup;
			children = (
				7710B0A61FA09AF700C9CBE5 /* dec_if.h */,
				7710B0A51FA09AF700C9CBE5 /* if_rom.h */,
			);
			path = "opencore-amrwb";
			sourceTree = "<group>";
		};
		771138D81FC7B47B0084D9CB /* BRAlertView */ = {
			isa = PBXGroup;
			children = (
				771138D91FC7B4920084D9CB /* BRAlertView.h */,
				771138DA1FC7B4920084D9CB /* BRAlertView.m */,
			);
			path = BRAlertView;
			sourceTree = "<group>";
		};
		7712BE0C1FD915950064B0F6 /* WzdQuestion */ = {
			isa = PBXGroup;
			children = (
				7712BE161FD91FC60064B0F6 /* BRWzdQuestionMessageCell.h */,
				7712BE171FD91FC60064B0F6 /* BRWzdQuestionMessageCell.m */,
				7712BE191FD91FEA0064B0F6 /* BRWzdQuestionMessageCellLayout.h */,
				7712BE1A1FD91FEA0064B0F6 /* BRWzdQuestionMessageCellLayout.m */,
			);
			name = WzdQuestion;
			sourceTree = "<group>";
		};
		7712BE0D1FD915AE0064B0F6 /* WzdAnswer */ = {
			isa = PBXGroup;
			children = (
				7712BE101FD919E00064B0F6 /* BRWzdAnswerMessageCell.h */,
				7712BE111FD919E00064B0F6 /* BRWzdAnswerMessageCell.m */,
				7712BE131FD91A2A0064B0F6 /* BRWzdAnswerMessageCellLayout.h */,
				7712BE141FD91A2A0064B0F6 /* BRWzdAnswerMessageCellLayout.m */,
			);
			name = WzdAnswer;
			sourceTree = "<group>";
		};
		7712BE0E1FD915D70064B0F6 /* FzdAnswer */ = {
			isa = PBXGroup;
			children = (
				7712BE1C1FD920130064B0F6 /* BRFzdAnswerMessageCell.h */,
				7712BE1D1FD920130064B0F6 /* BRFzdAnswerMessageCell.m */,
				7712BE251FD920720064B0F6 /* BRFzdAnswerMessageCellLayout.h */,
				7712BE261FD920720064B0F6 /* BRFzdAnswerMessageCellLayout.m */,
			);
			name = FzdAnswer;
			sourceTree = "<group>";
		};
		7712BE0F1FD915E00064B0F6 /* FzdQuestion */ = {
			isa = PBXGroup;
			children = (
				7712BE1F1FD920330064B0F6 /* BRFzdQuestionMessageCell.h */,
				7712BE201FD920330064B0F6 /* BRFzdQuestionMessageCell.m */,
				7712BE221FD9205C0064B0F6 /* BRFzdQuestionMessageCellLayout.h */,
				7712BE231FD9205C0064B0F6 /* BRFzdQuestionMessageCellLayout.m */,
			);
			name = FzdQuestion;
			sourceTree = "<group>";
		};
		7712BE281FD9268B0064B0F6 /* SupplementQuestion */ = {
			isa = PBXGroup;
			children = (
				7712BE2A1FD927B10064B0F6 /* BRSupplementQuestionMessageCell.h */,
				7712BE2B1FD927B10064B0F6 /* BRSupplementQuestionMessageCell.m */,
				7712BE301FD927ED0064B0F6 /* BRSupplementQuestionMessageCellLayout.h */,
				7712BE311FD927ED0064B0F6 /* BRSupplementQuestionMessageCellLayout.m */,
			);
			name = SupplementQuestion;
			sourceTree = "<group>";
		};
		7712BE291FD9271D0064B0F6 /* SupplementAnswer */ = {
			isa = PBXGroup;
			children = (
				7712BE331FD928080064B0F6 /* BRSupplementAnswerMessageCell.h */,
				7712BE341FD928080064B0F6 /* BRSupplementAnswerMessageCell.m */,
				7712BE2D1FD927CD0064B0F6 /* BRSupplementAnswerMessageCellLayout.h */,
				7712BE2E1FD927CD0064B0F6 /* BRSupplementAnswerMessageCellLayout.m */,
			);
			name = SupplementAnswer;
			sourceTree = "<group>";
		};
		77131EA81F4D79DD006C9F79 /* login&register */ = {
			isa = PBXGroup;
			children = (
				7751DD5F1F4EE06400C10C3C /* LoginInputView.h */,
				7751DD601F4EE06400C10C3C /* LoginInputView.m */,
			);
			name = "login&register";
			sourceTree = "<group>";
		};
		771608A51F662CF200D3A03C /* sessionList */ = {
			isa = PBXGroup;
			children = (
				77EA2615295DA01A000D1359 /* titleView */,
				775656A31F628BF800F779C2 /* SessionListCell.h */,
				775656A41F628BF800F779C2 /* SessionListCell.m */,
			);
			name = sessionList;
			sourceTree = "<group>";
		};
		771608A61F662D1300D3A03C /* UI */ = {
			isa = PBXGroup;
			children = (
				77B481861F6664E800466F3B /* Modal */,
				77B481851F6664DB00466F3B /* ViewController */,
				77B481811F665F8A00466F3B /* View */,
				77B481871F66662200466F3B /* IMChat.h */,
			);
			name = UI;
			sourceTree = "<group>";
		};
		771736CD1FA96A8A00ADE932 /* Messages */ = {
			isa = PBXGroup;
			children = (
				771736CA1FA96A7E00ADE932 /* BRMessagesModel.h */,
				771736CB1FA96A7E00ADE932 /* BRMessagesModel.m */,
				771736C41FA9689500ADE932 /* BRMessageModel.h */,
				771736C51FA9689500ADE932 /* BRMessageModel.m */,
				771736C71FA968A200ADE932 /* BRMessageContentModel.h */,
				771736C81FA968A200ADE932 /* BRMessageContentModel.m */,
			);
			name = Messages;
			sourceTree = "<group>";
		};
		771F66EC1FC808CB002AAA5C /* PatientDocument */ = {
			isa = PBXGroup;
			children = (
				771F66ED1FC8090F002AAA5C /* BRPatientDocumentTableViewCell.h */,
				771F66EE1FC8090F002AAA5C /* BRPatientDocumentTableViewCell.m */,
				771F66EF1FC8090F002AAA5C /* BRPatientDocumentTableViewCell.xib */,
			);
			path = PatientDocument;
			sourceTree = "<group>";
		};
		7720F1921FE8EF6C009DC35C /* Login & Register */ = {
			isa = PBXGroup;
			children = (
				77BEE6631F55690A00597489 /* BRLoginModel.h */,
				77BEE6641F55690A00597489 /* BRLoginModel.m */,
			);
			name = "Login & Register";
			sourceTree = "<group>";
		};
		7720F1931FE8EFE2009DC35C /* SessionList */ = {
			isa = PBXGroup;
			children = (
				7720F1941FE8F626009DC35C /* BRSessionListModel.h */,
				7720F1951FE8F626009DC35C /* BRSessionListModel.m */,
				7720F19A1FE8F9B2009DC35C /* BRSessionListItemModel.h */,
				7720F19B1FE8F9B2009DC35C /* BRSessionListItemModel.m */,
				7720F1971FE8F658009DC35C /* BRSessionRosterUserModel.h */,
				7720F1981FE8F658009DC35C /* BRSessionRosterUserModel.m */,
			);
			name = SessionList;
			sourceTree = "<group>";
		};
		7721B3F21FC4321D000A6DFA /* BRActionSheet */ = {
			isa = PBXGroup;
			children = (
				7721B3F31FC43299000A6DFA /* BRActionSheetView.h */,
				7721B3F41FC43299000A6DFA /* BRActionSheetView.m */,
			);
			path = BRActionSheet;
			sourceTree = "<group>";
		};
		7724003E1F564EF30015E0E3 /* Network */ = {
			isa = PBXGroup;
			children = (
				77C5D3C71F7A4D6800E836BE /* HTTPRequest.h */,
				77C5D3C81F7A4D6800E836BE /* HTTPRequest.m */,
				7781E2911F4D1AB900DDD047 /* SocketManager.h */,
				7781E2921F4D1AB900DDD047 /* SocketManager.m */,
				773D91402CF5D8B100547A91 /* BRTencentOCRRequest.h */,
				773D91412CF5D8B100547A91 /* BRTencentOCRRequest.m */,
			);
			name = Network;
			sourceTree = "<group>";
		};
		7726F6071F95E1C000D2E07B /* Audio */ = {
			isa = PBXGroup;
			children = (
				772A7D041FA1903C001AE0BB /* BRAudioMessageCell.h */,
				772A7D051FA1903C001AE0BB /* BRAudioMessageCell.m */,
				772A7D071FA19066001AE0BB /* BRAudioMessageCellLayout.h */,
				772A7D081FA19066001AE0BB /* BRAudioMessageCellLayout.m */,
			);
			name = Audio;
			sourceTree = "<group>";
		};
		7726F6081F95E86100D2E07B /* WzdBase */ = {
			isa = PBXGroup;
			children = (
				7721B3E81FC422E7000A6DFA /* BRWzdBaseMessageCell.h */,
				7721B3E91FC422E7000A6DFA /* BRWzdBaseMessageCell.m */,
				7721B3EB1FC42310000A6DFA /* BRWzdBaseMessageCellLayout.h */,
				7721B3EC1FC42310000A6DFA /* BRWzdBaseMessageCellLayout.m */,
			);
			name = WzdBase;
			sourceTree = "<group>";
		};
		77293F9C1FC9659F00329DC2 /* RRFPSBar */ = {
			isa = PBXGroup;
			children = (
				77293F9D1FC9659F00329DC2 /* RRFPSBar.h */,
				77293F9E1FC9659F00329DC2 /* RRFPSBar.m */,
			);
			path = RRFPSBar;
			sourceTree = "<group>";
		};
		772B59B81FE7DA4A009EA260 /* MessageRevoke */ = {
			isa = PBXGroup;
			children = (
				772B59B91FE7DA84009EA260 /* BRRevokeMessageCell.h */,
				772B59BA1FE7DA84009EA260 /* BRRevokeMessageCell.m */,
				772B59BC1FE7DACC009EA260 /* BRRevokeMessageCellLayout.h */,
				772B59BD1FE7DACC009EA260 /* BRRevokeMessageCellLayout.m */,
			);
			name = MessageRevoke;
			sourceTree = "<group>";
		};
		77327D6F1FC269620023939F /* SessiionStart */ = {
			isa = PBXGroup;
			children = (
				77327D741FC269F50023939F /* BRSessionStartMessageCell.h */,
				77327D751FC269F50023939F /* BRSessionStartMessageCell.m */,
				77327D711FC269B90023939F /* BRSessionStartMessageCellLayout.h */,
				77327D721FC269B90023939F /* BRSessionStartMessageCellLayout.m */,
			);
			name = SessiionStart;
			sourceTree = "<group>";
		};
		77327D701FC2696E0023939F /* SessionEnd */ = {
			isa = PBXGroup;
			children = (
				77327D771FC2757C0023939F /* BRSessionEndMessageCell.h */,
				77327D781FC2757C0023939F /* BRSessionEndMessageCell.m */,
				77327D7A1FC275A00023939F /* BRSessionEndMessageCellLayout.h */,
				77327D7B1FC275A00023939F /* BRSessionEndMessageCellLayout.m */,
			);
			name = SessionEnd;
			sourceTree = "<group>";
		};
		77327D7D1FC281060023939F /* CustomSystem */ = {
			isa = PBXGroup;
			children = (
				77327D7E1FC2830F0023939F /* BRCustomSystemMessageCell.h */,
				77327D7F1FC2830F0023939F /* BRCustomSystemMessageCell.m */,
				77327D811FC283380023939F /* BRCustomSystemMessageCellLayout.h */,
				77327D821FC283380023939F /* BRCustomSystemMessageCellLayout.m */,
			);
			name = CustomSystem;
			sourceTree = "<group>";
		};
		77351B1420037D9A00E266F7 /* Login&Register */ = {
			isa = PBXGroup;
			children = (
				7751DD5C1F4EAF8800C10C3C /* LoginViewController.h */,
				7751DD5D1F4EAF8800C10C3C /* LoginViewController.m */,
				A6C45FB11F9735B100F85928 /* RegisterViewController.h */,
				A6C45FB21F9735B100F85928 /* RegisterViewController.m */,
				77351B1520037DF500E266F7 /* DataInitLoadingViewController.h */,
				77351B1620037DF500E266F7 /* DataInitLoadingViewController.m */,
				A625873E2004CB3A00259622 /* RegisterTF.h */,
				A625873F2004CB3A00259622 /* RegisterTF.m */,
				777DC1F5273D687C00A594D7 /* JTPrivacyWebViewController.h */,
				777DC1F6273D687D00A594D7 /* JTPrivacyWebViewController.m */,
			);
			name = "Login&Register";
			sourceTree = "<group>";
		};
		77399A0A20060B5900CA2277 /* BRDataEmptyView */ = {
			isa = PBXGroup;
			children = (
				77399A0B20060B8F00CA2277 /* BRDataEmptyView.h */,
				77399A0C20060B8F00CA2277 /* BRDataEmptyView.m */,
			);
			path = BRDataEmptyView;
			sourceTree = "<group>";
		};
		773D91442CF5D97700547A91 /* BRIntelligent */ = {
			isa = PBXGroup;
			children = (
				773D91452CF5D99200547A91 /* OCRPoint.h */,
				773D91462CF5D99200547A91 /* OCRPoint.m */,
				773D91482CF5DA1400547A91 /* OCRWordPolygon.h */,
				773D91492CF5DA1400547A91 /* OCRWordPolygon.m */,
				773D914B2CF5DA3300547A91 /* OCRTextDetection.h */,
				773D914C2CF5DA3300547A91 /* OCRTextDetection.m */,
				773D914E2CF5DA6500547A91 /* OCRResponse.h */,
				773D914F2CF5DA6500547A91 /* OCRResponse.m */,
				777A23982CF80E2300C0853F /* BRHerbInfo.h */,
				777A23992CF80E2300C0853F /* BRHerbInfo.m */,
			);
			path = BRIntelligent;
			sourceTree = "<group>";
		};
		773E8AF52CD9E6030096F0A5 /* AdjustByMultiple */ = {
			isa = PBXGroup;
			children = (
				773E8AF62CD9E62B0096F0A5 /* BRAdjustByMultipleContentView.h */,
				773E8AF72CD9E62B0096F0A5 /* BRAdjustByMultipleContentView.m */,
			);
			path = AdjustByMultiple;
			sourceTree = "<group>";
		};
		774162D7279EE2D20028FC2C /* Model */ = {
			isa = PBXGroup;
			children = (
				774162D8279EE2E50028FC2C /* BRWechatBindResultModel.h */,
				774162D9279EE2E50028FC2C /* BRWechatBindResultModel.m */,
				774162DB279EE7300028FC2C /* BRWithdrawInfoModel.h */,
				774162DC279EE7300028FC2C /* BRWithdrawInfoModel.m */,
				77EA2619295EDE64000D1359 /* BRCashTypeInfoModel.h */,
				77EA261A295EDE64000D1359 /* BRCashTypeInfoModel.m */,
			);
			name = Model;
			sourceTree = "<group>";
		};
		7742ECA11F4BD7ED00A9B110 = {
			isa = PBXGroup;
			children = (
				7742ECAC1F4BD7ED00A9B110 /* BRZY */,
				7742ECAB1F4BD7ED00A9B110 /* Products */,
				277921CA94E312263655F635 /* Pods */,
				1506106EEDCD02E407BEA481 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		7742ECAB1F4BD7ED00A9B110 /* Products */ = {
			isa = PBXGroup;
			children = (
				7742ECAA1F4BD7ED00A9B110 /* BRZY.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7742ECAC1F4BD7ED00A9B110 /* BRZY */ = {
			isa = PBXGroup;
			children = (
				77F6D00C2D12878E00247F4E /* .env */,
				77BA08E226DA6D100054371B /* BRZYRelease.entitlements */,
				77275FE21F516918000A76BA /* BRZY.entitlements */,
				7742ECC51F4C05CB00A9B110 /* AppDelegate */,
				7742ECCC1F4C070F00A9B110 /* Classes */,
				7742ECBE1F4BD7ED00A9B110 /* Info.plist */,
				7764AB101F60081800B26332 /* Localizable.strings */,
				7742ECB91F4BD7ED00A9B110 /* Assets.xcassets */,
				7742ECAD1F4BD7ED00A9B110 /* Supporting Files */,
			);
			path = BRZY;
			sourceTree = "<group>";
		};
		7742ECAD1F4BD7ED00A9B110 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				7742ECAE1F4BD7ED00A9B110 /* main.m */,
				7742ECD61F4C089600A9B110 /* PrefixHeader.pch */,
				7724004E1F5659600015E0E3 /* ModulesRegister.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		7742ECC51F4C05CB00A9B110 /* AppDelegate */ = {
			isa = PBXGroup;
			children = (
				7742ECB01F4BD7ED00A9B110 /* AppDelegate.h */,
				7742ECB11F4BD7ED00A9B110 /* AppDelegate.m */,
				7724004B1F5658B10015E0E3 /* BRThirdPartModule.h */,
				7724004C1F5658B10015E0E3 /* BRThirdPartModule.m */,
				772400511F5665850015E0E3 /* BRAppStartModule.h */,
				772400521F5665850015E0E3 /* BRAppStartModule.m */,
				0A3124572004974700D56D36 /* BRAPNsModule.h */,
				0A3124582004974700D56D36 /* BRAPNsModule.m */,
			);
			path = AppDelegate;
			sourceTree = "<group>";
		};
		7742ECCC1F4C070F00A9B110 /* Classes */ = {
			isa = PBXGroup;
			children = (
				775643122DF7381900392EFE /* BRAuthConfig.h */,
				775643132DF7381900392EFE /* BRAuthConfig.m */,
				7742ECD41F4C086E00A9B110 /* Sections */,
				7742ECD11F4C086E00A9B110 /* General */,
				7742ECD21F4C086E00A9B110 /* Utils */,
				7742ECD31F4C086E00A9B110 /* Macro */,
				7742ECD51F4C086E00A9B110 /* Library */,
			);
			path = Classes;
			sourceTree = "<group>";
		};
		7742ECD11F4C086E00A9B110 /* General */ = {
			isa = PBXGroup;
			children = (
				7742ECD71F4C0D1200A9B110 /* Base */,
				7724003E1F564EF30015E0E3 /* Network */,
				7781E2951F4D1B3500DDD047 /* IM */,
				7742ECD81F4C0D1A00A9B110 /* Categories */,
				7742ECDA1F4C0D3200A9B110 /* Models */,
				7742ECD91F4C0D2500A9B110 /* Views */,
			);
			path = General;
			sourceTree = "<group>";
		};
		7742ECD21F4C086E00A9B110 /* Utils */ = {
			isa = PBXGroup;
			children = (
				77EF505E2DA2294000F085B1 /* AuthCheckHelper.h */,
				77EF505F2DA2294000F085B1 /* AuthCheckHelper.m */,
				77B3A5512D2447C000097E8A /* TCPMappingParser.h */,
				77B3A5522D2447C000097E8A /* TCPMappingParser.m */,
				77BEE6671F556EC900597489 /* BRError.h */,
				77BEE6681F556EC900597489 /* BRError.m */,
				7742ECF01F4C234F00A9B110 /* ViewTools.h */,
				7742ECF11F4C234F00A9B110 /* ViewTools.m */,
				771C15CA1F4D2B4100099626 /* UserManager.h */,
				771C15CB1F4D2B4100099626 /* UserManager.m */,
				770605861FDBEAC60091FA60 /* FileManager.h */,
				770605871FDBEAC60091FA60 /* FileManager.m */,
				7742ECED1F4C22EC00A9B110 /* Utils.h */,
				7742ECEE1F4C22EC00A9B110 /* Utils.m */,
				7781E28E1F4D19CE00DDD047 /* Config.h */,
				7781E28F1F4D19CE00DDD047 /* Config.m */,
				7775A1D81FFF4C77009D2131 /* BRDebugInfoViewController.h */,
				7775A1D91FFF4C77009D2131 /* BRDebugInfoViewController.m */,
				773D913D2CF5D63D00547A91 /* BRTencentCloudAPISignature.h */,
				773D913E2CF5D63D00547A91 /* BRTencentCloudAPISignature.m */,
				77273AEC2CF70E1C0018A9CD /* EnvironmentConfig.h */,
				77273AED2CF70E1C0018A9CD /* EnvironmentConfig.m */,
			);
			name = Utils;
			path = Helpers;
			sourceTree = "<group>";
		};
		7742ECD31F4C086E00A9B110 /* Macro */ = {
			isa = PBXGroup;
			children = (
				7742ED0D1F4C2F1600A9B110 /* AppMacro.h */,
				772DFD421F55589600350AED /* enumList.h */,
				77BEE66A1F55748200597489 /* BRDefines.h */,
				7731F8852E42CA5400A7CC99 /* mapping.txt */,
			);
			path = Macro;
			sourceTree = "<group>";
		};
		7742ECD41F4C086E00A9B110 /* Sections */ = {
			isa = PBXGroup;
			children = (
				77110C8C2E069F8000F002FC /* BRWithdrawPasswordView.h */,
				77110C8D2E069F8000F002FC /* BRWithdrawPasswordView.m */,
				7790B2062E06594800A08108 /* WithdrawPasswordViewController.h */,
				7790B2072E06594800A08108 /* WithdrawPasswordViewController.m */,
				777727CF276DA63200DA7FD4 /* ebook */,
				0A32B6F01F580CEB00E51EE0 /* Prescription */,
				7742ECE61F4C206E00A9B110 /* Message */,
				7742ECEA1F4C20A900A9B110 /* Patient */,
				77C5D3C01F7A490800E836BE /* Manager */,
				7742ECF31F4C264900A9B110 /* Invite */,
				7742ECF41F4C265300A9B110 /* UserCenter */,
				77351B1420037D9A00E266F7 /* Login&Register */,
			);
			path = Sections;
			sourceTree = "<group>";
		};
		7742ECD51F4C086E00A9B110 /* Library */ = {
			isa = PBXGroup;
			children = (
				779FC8282DF5C7F4001CA5D3 /* ATAuthSDK.framework */,
				779FC8292DF5C7F4001CA5D3 /* YTXMonitor.framework */,
				779FC82A2DF5C7F4001CA5D3 /* YTXOperators.framework */,
				77089A7E2CF0990900C9E6C3 /* TencentORC */,
				778E0B72279D33DB00F9BA6B /* 弹窗 */,
				0F41901D204FEBC7005B81D3 /* BRGuidePageView */,
				77399A0A20060B5900CA2277 /* BRDataEmptyView */,
				0AA18C751FF33A9D00CC3C61 /* ZYActivitiesView */,
				775243801FC978CB00EA2ED3 /* DHBaseModel */,
				0ACF4F261FC974A500A3B87F /* MMNumberKeyboard */,
				77293F9C1FC9659F00329DC2 /* RRFPSBar */,
				771138D81FC7B47B0084D9CB /* BRAlertView */,
				7721B3F21FC4321D000A6DFA /* BRActionSheet */,
				779642761FC40B9A00AA0976 /* CustomView */,
				7796427F1FC40B9A00AA0976 /* DHBaseViewController */,
				7710B0961FA09AA300C9CBE5 /* AMR */,
				77B481881F66740A00466F3B /* GrowingTextView */,
				77FCA5D41F6136D400369CB9 /* PopoverView */,
				772400421F56549C0015E0E3 /* BRModuleManager.h */,
				772400431F56549C0015E0E3 /* BRModuleManager.m */,
				77131EA91F4D7D6B006C9F79 /* Reachability.h */,
				77131EAA1F4D7D6B006C9F79 /* Reachability.m */,
				77FCA5DE1F61512000369CB9 /* TitleBarView.h */,
				77FCA5DF1F61512000369CB9 /* TitleBarView.m */,
				7724EE681FBE8DE100309E92 /* UINavigationBar+Addition.h */,
				7724EE691FBE8DE100309E92 /* UINavigationBar+Addition.m */,
			);
			name = Library;
			path = Vendors;
			sourceTree = "<group>";
		};
		7742ECD71F4C0D1200A9B110 /* Base */ = {
			isa = PBXGroup;
			children = (
				7742ECE31F4C183B00A9B110 /* BaseTabBarController.h */,
				7742ECE41F4C183B00A9B110 /* BaseTabBarController.m */,
				7742ED0A1F4C2A1E00A9B110 /* BaseNavigationController.h */,
				7742ED0B1F4C2A1E00A9B110 /* BaseNavigationController.m */,
				77264C971F590452002C9CBD /* BaseViewController.h */,
				77264C981F590452002C9CBD /* BaseViewController.m */,
				A61F8E7F1FC6BB8500AB928E /* CameraManager.h */,
				A61F8E801FC6BB8500AB928E /* CameraManager.m */,
				778E0B76279D6C7600F9BA6B /* FirstOpenViewController.h */,
				778E0B77279D6C7600F9BA6B /* FirstOpenViewController.m */,
			);
			name = Base;
			sourceTree = "<group>";
		};
		7742ECD81F4C0D1A00A9B110 /* Categories */ = {
			isa = PBXGroup;
			children = (
				77D39E4E1FD2A5F0004A01E0 /* UITextView+APSUIControlTargetAction.h */,
				77D39E4C1FD2A5EF004A01E0 /* UITextView+APSUIControlTargetAction.m */,
				77D39E4F1FD2A5F0004A01E0 /* UITextView+MaxLength.h */,
				77D39E4D1FD2A5EF004A01E0 /* UITextView+MaxLength.m */,
				7742ED0E1F4C346800A9B110 /* UIColor+Util.h */,
				7742ED0F1F4C346800A9B110 /* UIColor+Util.m */,
				771C15CD1F4D709200099626 /* UIView+Util.h */,
				771C15CE1F4D709200099626 /* UIView+Util.m */,
				7719417C1F7B402D00E11088 /* UIFont+Util.h */,
				7719417D1F7B402D00E11088 /* UIFont+Util.m */,
				77131E9C1F4D7503006C9F79 /* NSDateFormatter+Singleton.h */,
				77131E9D1F4D7503006C9F79 /* NSDateFormatter+Singleton.m */,
				77131EA21F4D758D006C9F79 /* NSDate+Message.h */,
				77131EA31F4D758D006C9F79 /* NSDate+Message.m */,
				77131EA51F4D76D2006C9F79 /* UINavigationBar+BackgroundColor.h */,
				77131EA61F4D76D2006C9F79 /* UINavigationBar+BackgroundColor.m */,
				776365F81F4FC18A00AF92A1 /* UITextView+Placeholder.h */,
				776365F91F4FC18A00AF92A1 /* UITextView+Placeholder.m */,
				776365FB1F4FC19300AF92A1 /* UIButton+HotPointButtonCategory.h */,
				776365FC1F4FC19300AF92A1 /* UIButton+HotPointButtonCategory.m */,
				776365FE1F4FC2D800AF92A1 /* UITextField+Max.h */,
				776365FF1F4FC2D800AF92A1 /* UITextField+Max.m */,
				776191561F5A436100A19B77 /* UIImageView+Badge.h */,
				776191571F5A436100A19B77 /* UIImageView+Badge.m */,
				776191621F5A491100A19B77 /* UIButton+Badge.h */,
				776191631F5A491100A19B77 /* UIButton+Badge.m */,
				772901941F5D385A008C51F6 /* UIView+BR.h */,
				772901951F5D385A008C51F6 /* UIView+BR.m */,
				77B481AC1F668CC300466F3B /* NSAttributedString+BR.h */,
				77B481AD1F668CC300466F3B /* NSAttributedString+BR.m */,
				A6D9E7D41FFE4BF600694EC4 /* UITabBar+bagde.h */,
				A6D9E7D51FFE4BF600694EC4 /* UITabBar+bagde.m */,
				7719B97120206233005504C6 /* UIImage+BR.h */,
				7719B97220206233005504C6 /* UIImage+BR.m */,
			);
			name = Categories;
			sourceTree = "<group>";
		};
		7742ECD91F4C0D2500A9B110 /* Views */ = {
			isa = PBXGroup;
			children = (
				772901971F5D3C96008C51F6 /* BRBadgeView.h */,
				772901981F5D3C96008C51F6 /* BRBadgeView.m */,
				77E59D982C8C19B000CB2662 /* BRCustomPopupView.h */,
				77E59D992C8C19B000CB2662 /* BRCustomPopupView.m */,
			);
			name = Views;
			sourceTree = "<group>";
		};
		7742ECDA1F4C0D3200A9B110 /* Models */ = {
			isa = PBXGroup;
			children = (
				77BDAF4726CE98E1000470BE /* 地区模型 */,
				771911A91FE21A6000070347 /* BRBaseTextLinePositionModifier.h */,
				771911AA1FE21A6000070347 /* BRBaseTextLinePositionModifier.m */,
				7749E19F1F7DE23B00E93881 /* ButtonInfoModel.h */,
				7749E1A01F7DE23B00E93881 /* ButtonInfoModel.m */,
				776366011F4FD00100AF92A1 /* UserInfo.h */,
				776366021F4FD00100AF92A1 /* UserInfo.m */,
				770B62EE1FCD565500F6380C /* ConfigInfo.h */,
				770B62EF1FCD565500F6380C /* ConfigInfo.m */,
			);
			name = Models;
			sourceTree = "<group>";
		};
		7742ECE61F4C206E00A9B110 /* Message */ = {
			isa = PBXGroup;
			children = (
				7742ECE91F4C20A000A9B110 /* Controller */,
				7742ECE71F4C209300A9B110 /* Model */,
				7742ECE81F4C209B00A9B110 /* View */,
			);
			name = Message;
			sourceTree = "<group>";
		};
		7742ECE71F4C209300A9B110 /* Model */ = {
			isa = PBXGroup;
			children = (
				77ABE80828B0B55400176DC3 /* QuickReply */,
				77B481971F667A3700466F3B /* BRMessage.h */,
				77B481981F667A3700466F3B /* BRMessage.m */,
				7736B9481FE1260700C55A50 /* QuestionInfoModel.h */,
				7736B9491FE1260700C55A50 /* QuestionInfoModel.m */,
				7736B9451FE1257C00C55A50 /* QuestionItemModel.h */,
				7736B9461FE1257C00C55A50 /* QuestionItemModel.m */,
				775967331FFB280700ADC587 /* DoctorAuthentiationModel.h */,
				775967341FFB280700ADC587 /* DoctorAuthentiationModel.m */,
				77A3BB5B2D09D65F0074A786 /* BRUpdateUserInfoModel.h */,
				77A3BB5C2D09D65F0074A786 /* BRUpdateUserInfoModel.m */,
			);
			name = Model;
			sourceTree = "<group>";
		};
		7742ECE81F4C209B00A9B110 /* View */ = {
			isa = PBXGroup;
			children = (
				77ABE80428B0A88300176DC3 /* quickReply */,
				7757A8201FE4BE7500B065A5 /* system message */,
				770CBCE21FDFCFFD00C27AB3 /* frequent question */,
				77B4818F1F66763000466F3B /* Input Panel */,
				771608A51F662CF200D3A03C /* sessionList */,
				77FCA5D01F60EC2D00369CB9 /* popover */,
				7742ECEC1F4C20DF00A9B110 /* chat */,
				7742ECEB1F4C20D400A9B110 /* message */,
			);
			name = View;
			sourceTree = "<group>";
		};
		7742ECE91F4C20A000A9B110 /* Controller */ = {
			isa = PBXGroup;
			children = (
				7742ECFE1F4C26E400A9B110 /* MessageListViewController.h */,
				7742ECFF1F4C26E400A9B110 /* MessageListViewController.m */,
				7756569A1F6282EC00F779C2 /* SessionSearchViewController.h */,
				7756569B1F6282EC00F779C2 /* SessionSearchViewController.m */,
				7735EF301F62A713003CE02D /* ChatViewController.h */,
				7735EF311F62A713003CE02D /* ChatViewController.m */,
				7735EF331F62A720003CE02D /* SessionViewController.h */,
				7735EF341F62A720003CE02D /* SessionViewController.m */,
				7762973A1FDA69CC00E480F2 /* SessionHistoryViewController.h */,
				7762973B1FDA69CC00E480F2 /* SessionHistoryViewController.m */,
				770605831FDBBD510091FA60 /* SessionXiaoRanViewController.h */,
				770605841FDBBD510091FA60 /* SessionXiaoRanViewController.m */,
				7757A81D1FE4BE6C00B065A5 /* SystemMessageViewController.h */,
				7757A81E1FE4BE6C00B065A5 /* SystemMessageViewController.m */,
				7757A8271FE50FDE00B065A5 /* MessageTransmitViewController.h */,
				7757A8281FE50FDE00B065A5 /* MessageTransmitViewController.m */,
				777742381FD587150060BEF2 /* FrequentlyQuestionAddViewController.h */,
				777742391FD587150060BEF2 /* FrequentlyQuestionAddViewController.m */,
				7736B94B1FE144D700C55A50 /* FrequentlyQuestionContentAddViewController.h */,
				7736B94C1FE144D700C55A50 /* FrequentlyQuestionContentAddViewController.m */,
				7744A61F1FEA068700AE7ABD /* LogisticInfoViewController.h */,
				7744A6201FEA068700AE7ABD /* LogisticInfoViewController.m */,
				776F86001FF496D600CAAE33 /* WzdWebViewController.h */,
				776F86011FF496D600CAAE33 /* WzdWebViewController.m */,
				A620D4E920EF13FC0022A052 /* PhotoPrescAgreementViewController.h */,
				A620D4EA20EF13FC0022A052 /* PhotoPrescAgreementViewController.m */,
				A620D4EC20EF20370022A052 /* PhotoPrescViewController.h */,
				A620D4ED20EF20370022A052 /* PhotoPrescViewController.m */,
				77ABE81528B0F74800176DC3 /* QuickReplyQuesAddViewController.h */,
				77ABE81628B0F74800176DC3 /* QuickReplyQuesAddViewController.m */,
				77C021F328BAF4E000F551AF /* EnterQuickOrderViewController.h */,
				77C021F428BAF4E000F551AF /* EnterQuickOrderViewController.m */,
				7734EF0C2CDC95110037B345 /* QuickPrescribeSessionViewController.h */,
				7734EF0D2CDC95110037B345 /* QuickPrescribeSessionViewController.m */,
			);
			name = Controller;
			sourceTree = "<group>";
		};
		7742ECEA1F4C20A900A9B110 /* Patient */ = {
			isa = PBXGroup;
			children = (
				7742ECFD1F4C26AC00A9B110 /* Controller */,
				7742ECFC1F4C26A700A9B110 /* Model */,
				7742ECFB1F4C269F00A9B110 /* View */,
			);
			name = Patient;
			sourceTree = "<group>";
		};
		7742ECEB1F4C20D400A9B110 /* message */ = {
			isa = PBXGroup;
			children = (
				77B684921FFFAB9600A1FFF8 /* SessionStartPatient */,
				772B59B81FE7DA4A009EA260 /* MessageRevoke */,
				7712BE281FD9268B0064B0F6 /* SupplementQuestion */,
				7712BE291FD9271D0064B0F6 /* SupplementAnswer */,
				7712BE0F1FD915E00064B0F6 /* FzdQuestion */,
				7712BE0E1FD915D70064B0F6 /* FzdAnswer */,
				7712BE0D1FD915AE0064B0F6 /* WzdAnswer */,
				7712BE0C1FD915950064B0F6 /* WzdQuestion */,
				77327D7D1FC281060023939F /* CustomSystem */,
				77327D701FC2696E0023939F /* SessionEnd */,
				77327D6F1FC269620023939F /* SessiionStart */,
				7726F6081F95E86100D2E07B /* WzdBase */,
				7726F6071F95E1C000D2E07B /* Audio */,
				775309941F9449FE00823411 /* Image */,
				77B481931F66764D00466F3B /* Text */,
				77B481921F66764900466F3B /* Base */,
				77B481911F66764100466F3B /* System */,
				77B481901F66763D00466F3B /* Date */,
			);
			name = message;
			sourceTree = "<group>";
		};
		7742ECEC1F4C20DF00A9B110 /* chat */ = {
			isa = PBXGroup;
			children = (
				7761772A209958F400C35E77 /* ChatTitleView.h */,
				7761772B209958F400C35E77 /* ChatTitleView.m */,
				7761772D20995B2300C35E77 /* AgeGenderButton.h */,
				7761772E20995B2300C35E77 /* AgeGenderButton.m */,
			);
			name = chat;
			sourceTree = "<group>";
		};
		7742ECF31F4C264900A9B110 /* Invite */ = {
			isa = PBXGroup;
			children = (
				7742ECFA1F4C269700A9B110 /* Controller */,
				7742ECF91F4C269000A9B110 /* Model */,
				7742ECF81F4C268900A9B110 /* View */,
			);
			name = Invite;
			sourceTree = "<group>";
		};
		7742ECF41F4C265300A9B110 /* UserCenter */ = {
			isa = PBXGroup;
			children = (
				7742ECF71F4C267C00A9B110 /* Controller */,
				7742ECF61F4C267700A9B110 /* Model */,
				7742ECF51F4C266F00A9B110 /* View */,
				A61B101820ECA55200D52282 /* ZYPlayer */,
			);
			name = UserCenter;
			sourceTree = "<group>";
		};
		7742ECF51F4C266F00A9B110 /* View */ = {
			isa = PBXGroup;
			children = (
				77CB751422D5DD2300262B0C /* Broker */,
				A64703D2204E4D4000F33B3D /* doctorState */,
				A68CE8B71FE2776F008E5F05 /* agent */,
				A64C6CBC1FC045D800138EB1 /* personalData */,
				77DB71391F56E03100D07919 /* userCenter */,
				77131EA81F4D79DD006C9F79 /* login&register */,
			);
			name = View;
			sourceTree = "<group>";
		};
		7742ECF61F4C267700A9B110 /* Model */ = {
			isa = PBXGroup;
			children = (
				77CB751822D5E7B900262B0C /* Broker */,
				A64F46F0204F7E40004C5FAB /* doctorStateMdel */,
				7724007D1F56D9DA0015E0E3 /* PanelIconModel.h */,
				7724007E1F56D9DA0015E0E3 /* PanelIconModel.m */,
				77F7A1471F5E3DCF00EA5FC1 /* ListCellModel.h */,
				77F7A1481F5E3DCF00EA5FC1 /* ListCellModel.m */,
				A62459881F8F715900C88DD4 /* WaitAcceptDoctorModel.h */,
				A62459891F8F715900C88DD4 /* WaitAcceptDoctorModel.m */,
				A6A1CD511FEA073D00044E3C /* CommonProblemsModel.h */,
				A6A1CD521FEA073D00044E3C /* CommonProblemsModel.m */,
				A6FC85251FF4915D005B6CA8 /* TaskAllocationModel.h */,
				A6FC85261FF4915D005B6CA8 /* TaskAllocationModel.m */,
				A6FC852E1FF4EB15005B6CA8 /* ChooseAgentmodel.h */,
				A6FC852F1FF4EB15005B6CA8 /* ChooseAgentmodel.m */,
				A63F1FFF1FF62ED5004A0DFA /* DownloadModel.h */,
				A63F20001FF62ED5004A0DFA /* DownloadModel.m */,
				A6106C1F1FF39504006E5D32 /* toPush */,
				A61BCE3A1FF24E48006C2EF1 /* architecture */,
			);
			name = Model;
			sourceTree = "<group>";
		};
		7742ECF71F4C267C00A9B110 /* Controller */ = {
			isa = PBXGroup;
			children = (
				77CB751022D5CA6D00262B0C /* Broker */,
				A6FF5D8D204D368100261A8F /* DoctorState */,
				7742ED071F4C274F00A9B110 /* UserCenterViewController.h */,
				7742ED081F4C274F00A9B110 /* UserCenterViewController.m */,
				A64E491A1F8C6BC50060CCDF /* PersonalDataViewController.h */,
				A64E491B1F8C6BC50060CCDF /* PersonalDataViewController.m */,
				A60FFDED1FC2CD360097C11D /* InputDataViewController.h */,
				A60FFDEE1FC2CD360097C11D /* InputDataViewController.m */,
				A649543D1F8B72D600F16C42 /* QualificationCertificationViewController.h */,
				A649543E1F8B72D600F16C42 /* QualificationCertificationViewController.m */,
				A67511DB1FD6CA4E00B6DDD3 /* CertificationSuccessViewController.h */,
				A67511DC1FD6CA4E00B6DDD3 /* CertificationSuccessViewController.m */,
				A691B0751FD8E29300D29516 /* IsReviewingViewController.h */,
				A691B0761FD8E29300D29516 /* IsReviewingViewController.m */,
				A68FA2791FD5435800DAF689 /* SeeTheSampleViewController.h */,
				A68FA27A1FD5435800DAF689 /* SeeTheSampleViewController.m */,
				A64954401F8B75E600F16C42 /* SecuritySettingViewController.h */,
				A64954411F8B75E600F16C42 /* SecuritySettingViewController.m */,
				A62ABA8D1FD12F8D00668558 /* ReplaceTelViewController.h */,
				A62ABA8E1FD12F8D00668558 /* ReplaceTelViewController.m */,
				A64954431F8B76DF00F16C42 /* CommonProblemsViewController.h */,
				A64954441F8B76DF00F16C42 /* CommonProblemsViewController.m */,
				A60629BE1FFCFD4F004BA00A /* ProblemSolvingViewController.h */,
				A60629BF1FFCFD4F004BA00A /* ProblemSolvingViewController.m */,
				A64954461F8B77B100F16C42 /* ProblemFeedbackViewController.h */,
				A64954471F8B77B100F16C42 /* ProblemFeedbackViewController.m */,
				A649544C1F8B791B00F16C42 /* AboutViewController.h */,
				A649544D1F8B791B00F16C42 /* AboutViewController.m */,
				A62003CF20901A9400D1B82F /* AvplayerViewController.h */,
				A62003D020901A9400D1B82F /* AvplayerViewController.m */,
				A62459731F8F5AE900C88DD4 /* AboutBRZYViewController.h */,
				A62459741F8F5AE900C88DD4 /* AboutBRZYViewController.m */,
				A68CE8AE1FE272C7008E5F05 /* CMOAssistantViewController.h */,
				A68CE8AF1FE272C7008E5F05 /* CMOAssistantViewController.m */,
				A68CE8B11FE275A1008E5F05 /* SchedulingViewController.h */,
				A68CE8B21FE275A1008E5F05 /* SchedulingViewController.m */,
				A68CE8B41FE275F8008E5F05 /* ArchitectureViewController.h */,
				A68CE8B51FE275F8008E5F05 /* ArchitectureViewController.m */,
				A60629C11FFD050D004BA00A /* SearchArchitectureViewController.h */,
				A60629C21FFD050D004BA00A /* SearchArchitectureViewController.m */,
				A69A30161FFBA7FA007E9413 /* AllocationViewController.h */,
				A69A30171FFBA7FA007E9413 /* AllocationViewController.m */,
				A62459761F8F5C6100C88DD4 /* ToPushViewController.h */,
				A62459771F8F5C6100C88DD4 /* ToPushViewController.m */,
				A624597C1F8F640800C88DD4 /* WaitAcceptDoctorViewController.h */,
				A624597D1F8F640800C88DD4 /* WaitAcceptDoctorViewController.m */,
				A69A30131FFB90AD007E9413 /* ScanQrcodeViewController.h */,
				A69A30141FFB90AD007E9413 /* ScanQrcodeViewController.m */,
				A6C45FAB1F96F64900F85928 /* DoctorDetailsViewController.h */,
				A6C45FAC1F96F64900F85928 /* DoctorDetailsViewController.m */,
				A68CE8AB1FE2684A008E5F05 /* DownloadViewController.h */,
				A68CE8AC1FE2684A008E5F05 /* DownloadViewController.m */,
				A61044DF2057F4A20055B6B2 /* SearchDownloadViewController.h */,
				A61044E02057F4A20055B6B2 /* SearchDownloadViewController.m */,
				A62459821F8F664100C88DD4 /* ResultsQueryViewController.h */,
				A62459831F8F664100C88DD4 /* ResultsQueryViewController.m */,
				A64761531F94B00000C84511 /* PatientNumberViewController.h */,
				A64761541F94B00000C84511 /* PatientNumberViewController.m */,
				A60B54501F95AB5500123620 /* ResultSummaryViewController.h */,
				A60B54511F95AB5500123620 /* ResultSummaryViewController.m */,
				A6504DE920ADA4D4001C00F2 /* SearchSummaryViewController.h */,
				A6504DEA20ADA4D4001C00F2 /* SearchSummaryViewController.m */,
				A6E4212720AE7AB700321AB4 /* SummaryMonthDetailViewController.h */,
				A6E4212820AE7AB700321AB4 /* SummaryMonthDetailViewController.m */,
				A6E4212A20AE82CB00321AB4 /* SummaryMonthViewController.h */,
				A6E4212B20AE82CB00321AB4 /* SummaryMonthViewController.m */,
				A62459851F8F673D00C88DD4 /* TaskAllocationViewController.h */,
				A62459861F8F673D00C88DD4 /* TaskAllocationViewController.m */,
				A6FC85281FF4E35A005B6CA8 /* ChooseAgentViewController.h */,
				A6FC85291FF4E35A005B6CA8 /* ChooseAgentViewController.m */,
				A67CF4BE205949B500E2DEEA /* SearchChooseViewController.h */,
				A67CF4BF205949B500E2DEEA /* SearchChooseViewController.m */,
				A6A4CF911FB942D300B545F6 /* BrzyAgreementViewController.h */,
				A6A4CF921FB942D300B545F6 /* BrzyAgreementViewController.m */,
				A635E7F91FBD2C810085E1D0 /* FindPasswordViewController.h */,
				A635E7FA1FBD2C810085E1D0 /* FindPasswordViewController.m */,
				77264C941F58F647002C9CBD /* MyQRCodeViewController.h */,
				77264C951F58F647002C9CBD /* MyQRCodeViewController.m */,
				7774866127B7F39D00B49BB4 /* AccountTerminateViewController.h */,
				7774866227B7F39D00B49BB4 /* AccountTerminateViewController.m */,
				77CFB9442AD4D77600A1EA90 /* BeianViewController.h */,
				77CFB9452AD4D77600A1EA90 /* BeianViewController.m */,
			);
			name = Controller;
			sourceTree = "<group>";
		};
		7742ECF81F4C268900A9B110 /* View */ = {
			isa = PBXGroup;
			children = (
				77194D951FD0026800716835 /* BRDHTextField.h */,
				77194D961FD0026800716835 /* BRDHTextField.m */,
				77A546221FFE52A80086EFD8 /* BRNameTextField.h */,
				77A546231FFE52A80086EFD8 /* BRNameTextField.m */,
			);
			name = View;
			sourceTree = "<group>";
		};
		7742ECF91F4C269000A9B110 /* Model */ = {
			isa = PBXGroup;
			children = (
			);
			name = Model;
			sourceTree = "<group>";
		};
		7742ECFA1F4C269700A9B110 /* Controller */ = {
			isa = PBXGroup;
			children = (
				7742ED041F4C273200A9B110 /* InviteViewController.h */,
				7742ED051F4C273200A9B110 /* InviteViewController.m */,
				7735EF421F62BFEF003CE02D /* InvitePatientViewController.h */,
				7735EF431F62BFEF003CE02D /* InvitePatientViewController.m */,
				7735EF451F62C00C003CE02D /* InviteDoctorViewController.h */,
				7735EF461F62C00C003CE02D /* InviteDoctorViewController.m */,
			);
			name = Controller;
			sourceTree = "<group>";
		};
		7742ECFB1F4C269F00A9B110 /* View */ = {
			isa = PBXGroup;
			children = (
				7777422F1FD56A710060BEF2 /* PatientsListCell.h */,
				777742301FD56A710060BEF2 /* PatientsListCell.m */,
				7744A6221FEA07D000AE7ABD /* PatientInfoView.h */,
				7744A6231FEA07D000AE7ABD /* PatientInfoView.m */,
				7744A6261FEA3C8300AE7ABD /* PatientInfoListCell.h */,
				7744A6271FEA3C8300AE7ABD /* PatientInfoListCell.m */,
				77EF7D6D1FEB549800232BC0 /* PatientsListXiaoRanView.h */,
				77EF7D6E1FEB549800232BC0 /* PatientsListXiaoRanView.m */,
				779F1797213CDD2700F44BE3 /* PatientFooterView.h */,
				779F1798213CDD2700F44BE3 /* PatientFooterView.m */,
			);
			name = View;
			sourceTree = "<group>";
		};
		7742ECFC1F4C26A700A9B110 /* Model */ = {
			isa = PBXGroup;
			children = (
				774C2DDA1FEE5E87004CD7E2 /* PatientModel.h */,
				774C2DDB1FEE5E87004CD7E2 /* PatientModel.m */,
				774C2DD71FEE2E03004CD7E2 /* PatientInfoModel.h */,
				774C2DD81FEE2E03004CD7E2 /* PatientInfoModel.m */,
				7786ECE71FF0AACD008BEEE7 /* PatientSelectedInfoModel.h */,
				7786ECE81FF0AACD008BEEE7 /* PatientSelectedInfoModel.m */,
				778139211FF2521700852B27 /* PatientsDocumentModel.h */,
				778139221FF2521700852B27 /* PatientsDocumentModel.m */,
				7793D29D2A236B9400971D97 /* BRTakerInfoModel.h */,
				7793D29E2A236B9400971D97 /* BRTakerInfoModel.m */,
				7793D2A02A2371D600971D97 /* BRQuickPrescribePatientModel.h */,
				7793D2A12A2371D600971D97 /* BRQuickPrescribePatientModel.m */,
			);
			name = Model;
			sourceTree = "<group>";
		};
		7742ECFD1F4C26AC00A9B110 /* Controller */ = {
			isa = PBXGroup;
			children = (
				7742ED011F4C272200A9B110 /* PatientViewController.h */,
				7742ED021F4C272200A9B110 /* PatientViewController.m */,
				7756569D1F62849000F779C2 /* PatientSearchViewController.h */,
				7756569E1F62849000F779C2 /* PatientSearchViewController.m */,
				7744A61C1FEA059100AE7ABD /* PatientInfoViewController.h */,
				7744A61D1FEA059100AE7ABD /* PatientInfoViewController.m */,
				7774865E27B79B1100B49BB4 /* PatientComplainViewController.h */,
				7774865F27B79B1100B49BB4 /* PatientComplainViewController.m */,
				77CFB9472AD5031900A1EA90 /* InviteScanPrescribeViewController.h */,
				77CFB9482AD5031900A1EA90 /* InviteScanPrescribeViewController.m */,
			);
			name = Controller;
			sourceTree = "<group>";
		};
		774494421FE0C29200522A88 /* NoDataView */ = {
			isa = PBXGroup;
			children = (
				774494431FE0C2A900522A88 /* BRNoDataView.h */,
				774494441FE0C2A900522A88 /* BRNoDataView.m */,
			);
			path = NoDataView;
			sourceTree = "<group>";
		};
		77475AE01FF26D5A0066F5C8 /* HistorySelectPatient */ = {
			isa = PBXGroup;
			children = (
				77475AE11FF26D740066F5C8 /* HistorySelectPatientView.h */,
				77475AE21FF26D740066F5C8 /* HistorySelectPatientView.m */,
			);
			path = HistorySelectPatient;
			sourceTree = "<group>";
		};
		77494F2B28A7225B0000132A /* QuickPrescribe */ = {
			isa = PBXGroup;
			children = (
				77494F2F28A722FF0000132A /* BRWritePatientInfoView.h */,
				77494F3028A722FF0000132A /* BRWritePatientInfoView.m */,
			);
			name = QuickPrescribe;
			sourceTree = "<group>";
		};
		7752437C1FC9778D00EA2ED3 /* model */ = {
			isa = PBXGroup;
			children = (
				7752437D1FC977CB00EA2ED3 /* BRVisitsArrangementModel.h */,
				7752437E1FC977CB00EA2ED3 /* BRVisitsArrangementModel.m */,
			);
			path = model;
			sourceTree = "<group>";
		};
		775243801FC978CB00EA2ED3 /* DHBaseModel */ = {
			isa = PBXGroup;
			children = (
				775243811FC978EE00EA2ED3 /* BRDHBaseModel.h */,
				775243821FC978EE00EA2ED3 /* BRDHBaseModel.m */,
			);
			path = DHBaseModel;
			sourceTree = "<group>";
		};
		775309941F9449FE00823411 /* Image */ = {
			isa = PBXGroup;
			children = (
				7726F6011F958FE000D2E07B /* BRImageMessageCell.h */,
				7726F6021F958FE000D2E07B /* BRImageMessageCell.m */,
				7726F6041F95902100D2E07B /* BRImageMessageCellLayout.h */,
				7726F6051F95902100D2E07B /* BRImageMessageCellLayout.m */,
			);
			name = Image;
			sourceTree = "<group>";
		};
		7757A8201FE4BE7500B065A5 /* system message */ = {
			isa = PBXGroup;
			children = (
				7757A8211FE4C04400B065A5 /* SystemMessageCell.h */,
				7757A8221FE4C04400B065A5 /* SystemMessageCell.m */,
				7757A8241FE4C11F00B065A5 /* SystemDateCell.h */,
				7757A8251FE4C11F00B065A5 /* SystemDateCell.m */,
			);
			name = "system message";
			sourceTree = "<group>";
		};
		7758EFCA1FE7CDAC0084D11E /* UpDate */ = {
			isa = PBXGroup;
			children = (
				7758EFCB1FE7CE5C0084D11E /* BRUpDateView.h */,
				7758EFCC1FE7CE5C0084D11E /* BRUpDateView.m */,
			);
			path = UpDate;
			sourceTree = "<group>";
		};
		7761534A1FCD30A7006A2FAC /* PatientDocumentModel */ = {
			isa = PBXGroup;
			children = (
				7761534B1FCD30D9006A2FAC /* PatientDocumentModel.h */,
				7761534C1FCD30D9006A2FAC /* PatientDocumentModel.m */,
			);
			path = PatientDocumentModel;
			sourceTree = "<group>";
		};
		7761534E1FCD7450006A2FAC /* CommonlyPrescription */ = {
			isa = PBXGroup;
			children = (
				776153511FCD7490006A2FAC /* controller */,
				7761534F1FCD7490006A2FAC /* view */,
			);
			path = CommonlyPrescription;
			sourceTree = "<group>";
		};
		7761534F1FCD7490006A2FAC /* view */ = {
			isa = PBXGroup;
			children = (
				7733F25D1FCE51430085B43F /* BRComView.h */,
				7733F25E1FCE51430085B43F /* BRComView.m */,
				7733F2601FCE51530085B43F /* BRHistoryView.h */,
				7733F2611FCE51530085B43F /* BRHistoryView.m */,
				7733F2661FCEC9890085B43F /* BRHistoryTableViewCell.h */,
				7733F2671FCEC9890085B43F /* BRHistoryTableViewCell.m */,
				7733F2681FCEC9890085B43F /* BRHistoryTableViewCell.xib */,
			);
			path = view;
			sourceTree = "<group>";
		};
		776153511FCD7490006A2FAC /* controller */ = {
			isa = PBXGroup;
			children = (
				776153521FCD74D0006A2FAC /* BRComPrescriptionViewController.h */,
				776153531FCD74D0006A2FAC /* BRComPrescriptionViewController.m */,
			);
			path = controller;
			sourceTree = "<group>";
		};
		776A6763200EE4CD00412402 /* GuideView */ = {
			isa = PBXGroup;
			children = (
				776A6767200EE4F100412402 /* BRGuideView.h */,
				776A6768200EE4F100412402 /* BRGuideView.m */,
			);
			path = GuideView;
			sourceTree = "<group>";
		};
		776BDF071FE522D500CBD754 /* MedicationWarning */ = {
			isa = PBXGroup;
			children = (
				776BDF081FE522F600CBD754 /* BRMedicationWarning.h */,
				776BDF091FE522F600CBD754 /* BRMedicationWarning.m */,
				7762B04120CE1A8600273CD5 /* BRMedicationWarningAlertView.h */,
				7762B04220CE1A8700273CD5 /* BRMedicationWarningAlertView.m */,
			);
			path = MedicationWarning;
			sourceTree = "<group>";
		};
		7770FD6D1F540D6500651814 /* Helper */ = {
			isa = PBXGroup;
			children = (
				7770FD6E1F540D7D00651814 /* IMSDKHelper.h */,
				7770FD6F1F540D7D00651814 /* IMSDKHelper.m */,
				772B10C51FA85F1A00D894EA /* IMUIHelper.h */,
				772B10C61FA85F1A00D894EA /* IMUIHelper.m */,
			);
			name = Helper;
			sourceTree = "<group>";
		};
		77723DFF1FD6331600E1BD5A /* AddCommonlyPrescription */ = {
			isa = PBXGroup;
			children = (
				77723E031FD635BE00E1BD5A /* controller */,
			);
			path = AddCommonlyPrescription;
			sourceTree = "<group>";
		};
		77723E031FD635BE00E1BD5A /* controller */ = {
			isa = PBXGroup;
			children = (
				77723E041FD635BE00E1BD5A /* AddCommonlyPrescriptionViewController.h */,
				77723E051FD635BE00E1BD5A /* AddCommonlyPrescriptionViewController.m */,
			);
			path = controller;
			sourceTree = "<group>";
		};
		777727CF276DA63200DA7FD4 /* ebook */ = {
			isa = PBXGroup;
			children = (
				777727D7276DDEE200DA7FD4 /* Model */,
				777727D6276DDEDB00DA7FD4 /* View */,
				777727D8276DDEE700DA7FD4 /* Controller */,
			);
			name = ebook;
			sourceTree = "<group>";
		};
		777727D6276DDEDB00DA7FD4 /* View */ = {
			isa = PBXGroup;
			children = (
				777727D9276DDF7200DA7FD4 /* EbookListItemCell.h */,
				777727DA276DDF7200DA7FD4 /* EbookListItemCell.m */,
				777727DC276DE62C00DA7FD4 /* EbookMallListCell.h */,
				777727DD276DE62C00DA7FD4 /* EbookMallListCell.m */,
			);
			name = View;
			sourceTree = "<group>";
		};
		777727D7276DDEE200DA7FD4 /* Model */ = {
			isa = PBXGroup;
			children = (
			);
			name = Model;
			sourceTree = "<group>";
		};
		777727D8276DDEE700DA7FD4 /* Controller */ = {
			isa = PBXGroup;
			children = (
				777727D0276DA66000DA7FD4 /* EbookListViewController.h */,
				777727D1276DA66000DA7FD4 /* EbookListViewController.m */,
				777727D3276DA73400DA7FD4 /* EbookMallListViewController.h */,
				777727D4276DA73400DA7FD4 /* EbookMallListViewController.m */,
			);
			name = Controller;
			sourceTree = "<group>";
		};
		7781E2951F4D1B3500DDD047 /* IM */ = {
			isa = PBXGroup;
			children = (
				77E870111FE3C466005E0F74 /* Models */,
				771608A61F662D1300D3A03C /* UI */,
				7770FD6D1F540D6500651814 /* Helper */,
				7781E2971F4D1BE300DDD047 /* include */,
				7781E2961F4D1B5D00DDD047 /* Database */,
			);
			name = IM;
			sourceTree = "<group>";
		};
		7781E2961F4D1B5D00DDD047 /* Database */ = {
			isa = PBXGroup;
			children = (
				7781E2A01F4D1D4700DDD047 /* IMDataBaseManager.h */,
				7781E2A11F4D1D4700DDD047 /* IMDataBaseManager.mm */,
				776633971F5E981B00CF7029 /* IMTableContact.h */,
				776633981F5E981B00CF7029 /* IMTableContact.mm */,
				77A8C4BE1F5FE2960056597E /* IMTableContact+WCTTableCoding.h */,
				776633911F5E97F500CF7029 /* IMTableMessage.h */,
				776633921F5E97F500CF7029 /* IMTableMessage.mm */,
				77E94C021F5FA1380010812A /* IMTableMessage+WCTTableCoding.h */,
				776633941F5E980200CF7029 /* IMTableSession.h */,
				776633951F5E980200CF7029 /* IMTableSession.mm */,
				77B13F8E1F5FD948005B9755 /* IMTableSession+WCTTableCoding.h */,
				772E57F01FF1E319008252E0 /* BRTablePharmacopeia.h */,
				772E57F11FF1E319008252E0 /* BRTablePharmacopeia.mm */,
				77E976EA1FF1E44F00404F69 /* BRTablePharmacopeia+WCTTableCoding.h */,
				0F9F54A120941EA600F2741C /* BRTemporaryPrescription.h */,
				0F9F54A220941EA600F2741C /* BRTemporaryPrescription.mm */,
				773D1D092094218C000467A6 /* BRTemporaryPrescription+WCTTableCoding.h */,
			);
			name = Database;
			sourceTree = "<group>";
		};
		7781E2971F4D1BE300DDD047 /* include */ = {
			isa = PBXGroup;
			children = (
				7770FD571F53FCA000651814 /* IMClient.h */,
				7770FD581F53FCA000651814 /* IMClient.m */,
				7729019A1F5D4F4E008C51F6 /* IMSessionManager.h */,
				7729019B1F5D4F4E008C51F6 /* IMSessionManager.m */,
				7770FD6A1F5408AD00651814 /* IMContactManager.h */,
				7770FD6B1F5408AD00651814 /* IMContactManager.m */,
				7770FD5E1F53FEA100651814 /* IMMessage.h */,
				7770FD5F1F53FEA100651814 /* IMMessage.m */,
				77CA9C3A1F5CFEBF00B6CDE5 /* IMSession.h */,
				77CA9C3B1F5CFEBF00B6CDE5 /* IMSession.m */,
				7770FD671F54089A00651814 /* IMContact.h */,
				7770FD681F54089A00651814 /* IMContact.m */,
			);
			name = include;
			sourceTree = "<group>";
		};
		778E0B61279BCAB600F9BA6B /* 提现 */ = {
			isa = PBXGroup;
			children = (
				774162D7279EE2D20028FC2C /* Model */,
				778E0B62279BCAE600F9BA6B /* View */,
				7755B71A20A5A49F00162617 /* MyPurseViewController.h */,
				7755B71B20A5A49F00162617 /* MyPurseViewController.m */,
				774AE86B1FD7FDCE009AFEF9 /* BRBankCardViewController.h */,
				774AE86C1FD7FDCE009AFEF9 /* BRBankCardViewController.m */,
				778E0B63279BCBD700F9BA6B /* BRWechatPocketMoneyViewController.h */,
				778E0B64279BCBD700F9BA6B /* BRWechatPocketMoneyViewController.m */,
			);
			name = "提现";
			sourceTree = "<group>";
		};
		778E0B62279BCAE600F9BA6B /* View */ = {
			isa = PBXGroup;
			children = (
				775E751020A999D500EAA5DB /* MyPurseHeaderView.h */,
				775E751120A999D500EAA5DB /* MyPurseHeaderView.m */,
				778E0B66279BD0BD00F9BA6B /* WechatWithdrawChooseCell.h */,
				778E0B67279BD0BD00F9BA6B /* WechatWithdrawChooseCell.m */,
				778E0B69279BD15A00F9BA6B /* WechatWithDrawSumCell.h */,
				778E0B6A279BD15A00F9BA6B /* WechatWithDrawSumCell.m */,
				778E0B6F279BE0D900F9BA6B /* WechatWithdrawInputNumCell.h */,
				778E0B70279BE0D900F9BA6B /* WechatWithdrawInputNumCell.m */,
				778E0B6C279BD9BE00F9BA6B /* WechatWithdrawBottomView.h */,
				778E0B6D279BD9BE00F9BA6B /* WechatWithdrawBottomView.m */,
			);
			name = View;
			sourceTree = "<group>";
		};
		778E0B72279D33DB00F9BA6B /* 弹窗 */ = {
			isa = PBXGroup;
			children = (
				778E0B73279D341100F9BA6B /* BRPrivacyPopView.h */,
				778E0B74279D341100F9BA6B /* BRPrivacyPopView.m */,
			);
			path = "弹窗";
			sourceTree = "<group>";
		};
		779642761FC40B9A00AA0976 /* CustomView */ = {
			isa = PBXGroup;
			children = (
				776A6763200EE4CD00412402 /* GuideView */,
				770D3E17200753AF00D48C5A /* ShareView */,
				770FE9591FF33CFE00008C78 /* ShowMyPurseSMSCode */,
				77475AE01FF26D5A0066F5C8 /* HistorySelectPatient */,
				77D778271FEDEB1500CB1A6F /* Announcement */,
				7758EFCA1FE7CDAC0084D11E /* UpDate */,
				776BDF071FE522D500CBD754 /* MedicationWarning */,
				774494421FE0C29200522A88 /* NoDataView */,
				779642771FC40B9A00AA0976 /* ScreeningView */,
				7796427C1FC40B9A00AA0976 /* SelectCity */,
			);
			path = CustomView;
			sourceTree = "<group>";
		};
		779642771FC40B9A00AA0976 /* ScreeningView */ = {
			isa = PBXGroup;
			children = (
				779642781FC40B9A00AA0976 /* BRSelectPatientView.h */,
				779642791FC40B9A00AA0976 /* BRSelectPatientView.m */,
				7796427A1FC40B9A00AA0976 /* UILabel+myLabel.h */,
				7796427B1FC40B9A00AA0976 /* UILabel+myLabel.m */,
			);
			path = ScreeningView;
			sourceTree = "<group>";
		};
		7796427C1FC40B9A00AA0976 /* SelectCity */ = {
			isa = PBXGroup;
			children = (
				7796427D1FC40B9A00AA0976 /* BRSelectCityZone.h */,
				7796427E1FC40B9A00AA0976 /* BRSelectCityZone.m */,
			);
			path = SelectCity;
			sourceTree = "<group>";
		};
		7796427F1FC40B9A00AA0976 /* DHBaseViewController */ = {
			isa = PBXGroup;
			children = (
				779642801FC40B9A00AA0976 /* BRDHBaseViewController.h */,
				779642811FC40B9A00AA0976 /* BRDHBaseViewController.m */,
				779642821FC40B9A00AA0976 /* Header.h */,
			);
			path = DHBaseViewController;
			sourceTree = "<group>";
		};
		7796428D1FC4214500AA0976 /* VisitsArrangement */ = {
			isa = PBXGroup;
			children = (
				7752437C1FC9778D00EA2ED3 /* model */,
				7796428E1FC4214500AA0976 /* controller */,
				779642941FC4214500AA0976 /* view */,
			);
			path = VisitsArrangement;
			sourceTree = "<group>";
		};
		7796428E1FC4214500AA0976 /* controller */ = {
			isa = PBXGroup;
			children = (
				7796428F1FC4214500AA0976 /* AddAndEditVisitsInfo */,
				779642921FC4214500AA0976 /* BRVisitsArrangementViewController.h */,
				779642931FC4214500AA0976 /* BRVisitsArrangementViewController.m */,
			);
			path = controller;
			sourceTree = "<group>";
		};
		7796428F1FC4214500AA0976 /* AddAndEditVisitsInfo */ = {
			isa = PBXGroup;
			children = (
				779642901FC4214500AA0976 /* BRAddVisitsViewController.h */,
				779642911FC4214500AA0976 /* BRAddVisitsViewController.m */,
			);
			path = AddAndEditVisitsInfo;
			sourceTree = "<group>";
		};
		779642941FC4214500AA0976 /* view */ = {
			isa = PBXGroup;
			children = (
				77BC46B22000DC790066CE29 /* AddAndEditVisitsInfo */,
				779642951FC4214500AA0976 /* BRCalendarCollectionViewCell.h */,
				779642961FC4214500AA0976 /* BRCalendarCollectionViewCell.m */,
				779642971FC4214500AA0976 /* BRCalendarCollectionViewCell.xib */,
				779642981FC4214500AA0976 /* BRCalendarView.h */,
				779642991FC4214500AA0976 /* BRCalendarView.m */,
			);
			path = view;
			sourceTree = "<group>";
		};
		7796429A1FC4214500AA0976 /* 我的钱包 */ = {
			isa = PBXGroup;
			children = (
				7796429B1FC4214500AA0976 /* controller */,
			);
			path = "我的钱包";
			sourceTree = "<group>";
		};
		7796429B1FC4214500AA0976 /* controller */ = {
			isa = PBXGroup;
			children = (
				7796429C1FC4214500AA0976 /* BRMyPurseViewController.h */,
				7796429D1FC4214500AA0976 /* BRMyPurseViewController.m */,
				77EF33DA1FD8D84B0008B712 /* BRGuiZeViewController.h */,
				77EF33DB1FD8D84B0008B712 /* BRGuiZeViewController.m */,
				770551681FDBBBBD000306B1 /* BRBinDingViewController.h */,
				770551691FDBBBBD000306B1 /* BRBinDingViewController.m */,
				7705516B1FDBC99D000306B1 /* BRBankCardListViewController.h */,
				7705516C1FDBC99D000306B1 /* BRBankCardListViewController.m */,
			);
			path = controller;
			sourceTree = "<group>";
		};
		77ABE80428B0A88300176DC3 /* quickReply */ = {
			isa = PBXGroup;
			children = (
				77ABE80528B0A8BD00176DC3 /* QuickReplyShowView.h */,
				77ABE80628B0A8BD00176DC3 /* QuickReplyShowView.m */,
				77ABE80F28B0E3C900176DC3 /* QuickReplyTopView.h */,
				77ABE81028B0E3C900176DC3 /* QuickReplyTopView.m */,
				77ABE81228B0EB9900176DC3 /* QuickReplyContentListCell.h */,
				77ABE81328B0EB9900176DC3 /* QuickReplyContentListCell.m */,
			);
			name = quickReply;
			sourceTree = "<group>";
		};
		77ABE80828B0B55400176DC3 /* QuickReply */ = {
			isa = PBXGroup;
			children = (
				77ABE80928B0B57200176DC3 /* QuickReplyContentModel.h */,
				77ABE80A28B0B57200176DC3 /* QuickReplyContentModel.m */,
				77ABE80C28B0B5EE00176DC3 /* QuickReplyTypeInfoModel.h */,
				77ABE80D28B0B5EE00176DC3 /* QuickReplyTypeInfoModel.m */,
			);
			name = QuickReply;
			sourceTree = "<group>";
		};
		77B481811F665F8A00466F3B /* View */ = {
			isa = PBXGroup;
			children = (
				77B4817E1F665EDA00466F3B /* IMChatCollectionView.h */,
				77B4817F1F665EDA00466F3B /* IMChatCollectionView.m */,
				77B481781F663B8400466F3B /* IMChatInputPanel.h */,
				77B481791F663B8400466F3B /* IMChatInputPanel.m */,
				77F4E76F1F6BBC9D00897C4A /* IMChatInputMoreView.h */,
				77F4E7701F6BBC9D00897C4A /* IMChatInputMoreView.m */,
				77B4817B1F665DDB00466F3B /* IMChatItemCell.h */,
				77B4817C1F665DDB00466F3B /* IMChatItemCell.m */,
				77B481751F663A8D00466F3B /* IMChatContainerView.h */,
				77B481761F663A8D00466F3B /* IMChatContainerView.m */,
				77B481721F6638FF00466F3B /* IMChatCollectionViewLayout.h */,
				77B481731F6638FF00466F3B /* IMChatCollectionViewLayout.m */,
				771608C41F66375100D3A03C /* IMChatItemCellLayout.h */,
			);
			name = View;
			sourceTree = "<group>";
		};
		77B481851F6664DB00466F3B /* ViewController */ = {
			isa = PBXGroup;
			children = (
				77B481821F6660E600466F3B /* IMChatViewController.h */,
				77B481831F6660E600466F3B /* IMChatViewController.m */,
			);
			name = ViewController;
			sourceTree = "<group>";
		};
		77B481861F6664E800466F3B /* Modal */ = {
			isa = PBXGroup;
			children = (
				771608C31F66371A00D3A03C /* IMChatItem.h */,
			);
			name = Modal;
			sourceTree = "<group>";
		};
		77B481881F66740A00466F3B /* GrowingTextView */ = {
			isa = PBXGroup;
			children = (
				77B481891F66740A00466F3B /* HPGrowingTextView.h */,
				77B4818A1F66740A00466F3B /* HPGrowingTextView.m */,
				77B4818B1F66740A00466F3B /* HPTextViewInternal.h */,
				77B4818C1F66740A00466F3B /* HPTextViewInternal.m */,
			);
			path = GrowingTextView;
			sourceTree = "<group>";
		};
		77B4818F1F66763000466F3B /* Input Panel */ = {
			isa = PBXGroup;
			children = (
				77B481941F6676E100466F3B /* BRChatInputTextPanel.h */,
				77B481951F6676E100466F3B /* BRChatInputTextPanel.m */,
				7706FF351F6A1C2C00EBB35C /* BRChatInputMoreContainerView.h */,
				7706FF361F6A1C2C00EBB35C /* BRChatInputMoreContainerView.m */,
				774518181FDE5C6800DAB506 /* BRXiaoRanChatInputMoreContainerView.h */,
				774518191FDE5C6800DAB506 /* BRXiaoRanChatInputMoreContainerView.m */,
			);
			name = "Input Panel";
			sourceTree = "<group>";
		};
		77B481901F66763D00466F3B /* Date */ = {
			isa = PBXGroup;
			children = (
				77B481B81F668FC100466F3B /* BRDateMessageCell.h */,
				77B481B91F668FC100466F3B /* BRDateMessageCell.m */,
				77B481B21F668E6300466F3B /* BRDateMessageCellLayout.h */,
				77B481B31F668E6300466F3B /* BRDateMessageCellLayout.m */,
			);
			name = Date;
			sourceTree = "<group>";
		};
		77B481911F66764100466F3B /* System */ = {
			isa = PBXGroup;
			children = (
				77B481AF1F668DC900466F3B /* BRSystemMessageCell.h */,
				77B481B01F668DC900466F3B /* BRSystemMessageCell.m */,
				77B481A61F668B5D00466F3B /* BRSystemMessageCellLayout.h */,
				77B481A71F668B5D00466F3B /* BRSystemMessageCellLayout.m */,
			);
			name = System;
			sourceTree = "<group>";
		};
		77B481921F66764900466F3B /* Base */ = {
			isa = PBXGroup;
			children = (
				77B4819D1F6683F200466F3B /* BRBaseMessageCell.h */,
				77B4819E1F6683F200466F3B /* BRBaseMessageCell.m */,
				77B4819A1F6682C800466F3B /* BRBaseMessageCellLayout.h */,
				77B4819B1F6682C800466F3B /* BRBaseMessageCellLayout.m */,
			);
			name = Base;
			sourceTree = "<group>";
		};
		77B481931F66764D00466F3B /* Text */ = {
			isa = PBXGroup;
			children = (
				77B481A31F6689E700466F3B /* BRTextMessageCell.h */,
				77B481A41F6689E700466F3B /* BRTextMessageCell.m */,
				77B481A01F66849E00466F3B /* BRTextMessageCellLayout.h */,
				77B481A11F66849E00466F3B /* BRTextMessageCellLayout.m */,
			);
			name = Text;
			sourceTree = "<group>";
		};
		77B684921FFFAB9600A1FFF8 /* SessionStartPatient */ = {
			isa = PBXGroup;
			children = (
				77B684961FFFAC2F00A1FFF8 /* BRSessionStartPatientMessageCell.h */,
				77B684971FFFAC2F00A1FFF8 /* BRSessionStartPatientMessageCell.m */,
				77B684991FFFAC4900A1FFF8 /* BRSessionStartPatientMessageCellLayout.h */,
				77B6849A1FFFAC4900A1FFF8 /* BRSessionStartPatientMessageCellLayout.m */,
			);
			name = SessionStartPatient;
			sourceTree = "<group>";
		};
		77BC46B22000DC790066CE29 /* AddAndEditVisitsInfo */ = {
			isa = PBXGroup;
			children = (
				77BC46B32000DCCE0066CE29 /* BRVisitsHospitalName.h */,
				77BC46B42000DCCE0066CE29 /* BRVisitsHospitalName.m */,
				77BC46B62000DD280066CE29 /* BRVisitsHospitalAddress.h */,
				77BC46B72000DD280066CE29 /* BRVisitsHospitalAddress.m */,
			);
			path = AddAndEditVisitsInfo;
			sourceTree = "<group>";
		};
		77BDAF4726CE98E1000470BE /* 地区模型 */ = {
			isa = PBXGroup;
			children = (
				77BDAF4826CE990F000470BE /* JTAreaInfoModel.h */,
				77BDAF4926CE990F000470BE /* JTAreaInfoModel.m */,
			);
			name = "地区模型";
			sourceTree = "<group>";
		};
		77BEE6621F5568E100597489 /* SocketReceiveModel */ = {
			isa = PBXGroup;
			children = (
				7720F1931FE8EFE2009DC35C /* SessionList */,
				7720F1921FE8EF6C009DC35C /* Login & Register */,
				771736CD1FA96A8A00ADE932 /* Messages */,
			);
			name = SocketReceiveModel;
			sourceTree = "<group>";
		};
		77C29328293C29CC001A5B45 /* View */ = {
			isa = PBXGroup;
			children = (
				77C29329293C29EA001A5B45 /* DrugInfoListCell.h */,
				77C2932A293C29EA001A5B45 /* DrugInfoListCell.m */,
				770044462C8EE94700236FF1 /* FloatingButton.h */,
				770044472C8EE94700236FF1 /* FloatingButton.m */,
				777B879B2CFC1A7C00004875 /* CustomBoilyWayCell.h */,
				777B879C2CFC1A7C00004875 /* CustomBoilyWayCell.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		77C5D3C01F7A490800E836BE /* Manager */ = {
			isa = PBXGroup;
			children = (
				7796429A1FC4214500AA0976 /* 我的钱包 */,
				778E0B61279BCAB600F9BA6B /* 提现 */,
				77C5D3C11F7A494700E836BE /* Controller */,
				77C5D3C21F7A494E00E836BE /* Model */,
				77C5D3C31F7A495600E836BE /* View */,
			);
			name = Manager;
			sourceTree = "<group>";
		};
		77C5D3C11F7A494700E836BE /* Controller */ = {
			isa = PBXGroup;
			children = (
				77723DFF1FD6331600E1BD5A /* AddCommonlyPrescription */,
				7761534E1FCD7450006A2FAC /* CommonlyPrescription */,
				7796428D1FC4214500AA0976 /* VisitsArrangement */,
				77C5D3C41F7A499500E836BE /* ManagerViewController.h */,
				77C5D3C51F7A499500E836BE /* ManagerViewController.m */,
				A67B75851FC51FC4005C637F /* OrderViewController.h */,
				A67B75861FC51FC4005C637F /* OrderViewController.m */,
				A61C693F1FCE56C100E7AB32 /* CommonlyUsedPartyViewController.h */,
				A61C69401FCE56C100E7AB32 /* CommonlyUsedPartyViewController.m */,
				A63695971FE7A79D002DFB0A /* SearchCommonViewController.h */,
				A63695981FE7A79D002DFB0A /* SearchCommonViewController.m */,
				A61C69451FCE59C500E7AB32 /* ServiceSettingViewController.h */,
				A61C69461FCE59C500E7AB32 /* ServiceSettingViewController.m */,
				A61C69481FCE5A8400E7AB32 /* MyAnnouncementViewController.h */,
				A61C69491FCE5A8400E7AB32 /* MyAnnouncementViewController.m */,
				A61C694B1FCE5B4300E7AB32 /* BlacklistViewController.h */,
				A61C694C1FCE5B4300E7AB32 /* BlacklistViewController.m */,
				A61C694E1FCE5BF400E7AB32 /* PharmacopoeiaViewController.h */,
				A61C694F1FCE5BF400E7AB32 /* PharmacopoeiaViewController.m */,
				A60629B91FFCDD16004BA00A /* MedicineParticularsViewController.h */,
				A60629B81FFCDD16004BA00A /* MedicineParticularsViewController.m */,
				777E588020526B1C000336AC /* ClassicPrescriptionViewController.h */,
				777E588120526B1C000336AC /* ClassicPrescriptionViewController.m */,
				77286C832057755E00117038 /* ClassicPrescriptionSearchViewController.h */,
				77286C842057755E00117038 /* ClassicPrescriptionSearchViewController.m */,
				77286C892057D77B00117038 /* ClassicPrescriptionListViewController.h */,
				77286C8A2057D77B00117038 /* ClassicPrescriptionListViewController.m */,
				0FB534AC20F49B2B00E084B7 /* PhotoPresViewController.h */,
				0FB534AD20F49B2B00E084B7 /* PhotoPresViewController.m */,
				0FB534B920F4D22100E084B7 /* PhotoPresDetailViewController.h */,
				0FB534BA20F4D22100E084B7 /* PhotoPresDetailViewController.m */,
				77C1EA17212C036100256EA8 /* DrugStoreViewController.h */,
				77C1EA18212C036100256EA8 /* DrugStoreViewController.m */,
				77264DF5212D5444002EE6C9 /* DrugStoreListViewController.h */,
				77264DF6212D5444002EE6C9 /* DrugStoreListViewController.m */,
				A6C74F24212D07D800BC2AD7 /* OrderRecordViewController.h */,
				A6C74F25212D07D800BC2AD7 /* OrderRecordViewController.m */,
				A6A574D8213528FD007F90AE /* OrderContentViewController.h */,
				A6A574D9213528FD007F90AE /* OrderContentViewController.m */,
				A642A8612137E8EF00C8FA8A /* GoodsContentViewController.h */,
				A642A8622137E8EF00C8FA8A /* GoodsContentViewController.m */,
			);
			name = Controller;
			sourceTree = "<group>";
		};
		77C5D3C21F7A494E00E836BE /* Model */ = {
			isa = PBXGroup;
			children = (
				A61C69511FCE975600E7AB32 /* PharmacopeiaModel.h */,
				A61C69521FCE975600E7AB32 /* PharmacopeiaModel.m */,
				A68CE8A81FE22289008E5F05 /* OrdersModel.h */,
				A68CE8A91FE22289008E5F05 /* OrdersModel.m */,
				A6274F231FEE405D000BE82A /* CompleteModel.h */,
				A6274F241FEE405D000BE82A /* CompleteModel.m */,
				A60DECF31FE3DB7E00FE2C42 /* CommonlyUsedPartyModel.h */,
				A60DECF41FE3DB7E00FE2C42 /* CommonlyUsedPartyModel.m */,
				A636959D1FE7E87C002DFB0A /* BlackListModel.h */,
				A636959E1FE7E87C002DFB0A /* BlackListModel.m */,
				A60629B51FFCD39A004BA00A /* PharmacopeiaCache.h */,
				A60629B61FFCD39A004BA00A /* PharmacopeiaCache.m */,
				771D93A82056292500A59DD2 /* PrescriptionModel.h */,
				771D93A92056292500A59DD2 /* PrescriptionModel.m */,
				77286C862057CAB700117038 /* ClassicSearchModel.h */,
				77286C872057CAB700117038 /* ClassicSearchModel.m */,
				775E751620AADE4B00EAA5DB /* BillDetailsModel.h */,
				775E751720AADE4B00EAA5DB /* BillDetailsModel.m */,
				0FB534B320F4B14900E084B7 /* PhotoPresModel.h */,
				0FB534B420F4B14900E084B7 /* PhotoPresModel.m */,
				A6F85C58212E6D2E00780DA6 /* OrderRecordModel.h */,
				A6F85C59212E6D2E00780DA6 /* OrderRecordModel.m */,
				771E52092134E9DE006B6ACB /* DrugStoreModel.h */,
				771E520A2134E9DE006B6ACB /* DrugStoreModel.m */,
				771E520C2134ED30006B6ACB /* DrugStoreListModel.h */,
				771E520D2134ED30006B6ACB /* DrugStoreListModel.m */,
			);
			name = Model;
			sourceTree = "<group>";
		};
		77C5D3C31F7A495600E836BE /* View */ = {
			isa = PBXGroup;
			children = (
				0FB534AF20F4A3A600E084B7 /* photoPres */,
				A61C69541FCEAA3C00E7AB32 /* commonlyUsedParty */,
				77DB713A1F56E07F00D07919 /* UserCenterPanelCollectionViewCell.h */,
				77DB713B1F56E07F00D07919 /* UserCenterPanelCollectionViewCell.m */,
				77DB713D1F56F34E00D07919 /* UserCenterHeaderView.h */,
				77DB713E1F56F34E00D07919 /* UserCenterHeaderView.m */,
				77C7D24C1F5815C300C6F31A /* UserCenterFooterView.h */,
				77C7D24D1F5815C300C6F31A /* UserCenterFooterView.m */,
				77F7A1441F5E3D6600EA5FC1 /* UserCenterListCell.h */,
				77F7A1451F5E3D6600EA5FC1 /* UserCenterListCell.m */,
				A636959A1FE7DF5C002DFB0A /* BlackListCell.h */,
				A636959B1FE7DF5C002DFB0A /* BlackListCell.m */,
				77F7A14A1F5E3FCD00EA5FC1 /* AboutHeader.h */,
				77F7A14B1F5E3FCD00EA5FC1 /* AboutHeader.m */,
				77F7A14D1F5E51AB00EA5FC1 /* AboutFooter.h */,
				77F7A14E1F5E51AB00EA5FC1 /* AboutFooter.m */,
				A60629BB1FFCDDA9004BA00A /* MedicinePCell.h */,
				A60629BC1FFCDDA9004BA00A /* MedicinePCell.m */,
				771D93AB205667E600A59DD2 /* ClassicPrescriptionCell.h */,
				771D93AC205667E600A59DD2 /* ClassicPrescriptionCell.m */,
				775E751320AA76B600EAA5DB /* BillDetailsHeaderView.h */,
				775E751420AA76B600EAA5DB /* BillDetailsHeaderView.m */,
				775E751920AAEB0600EAA5DB /* BillDetailsBaseCell.h */,
				775E751A20AAEB0600EAA5DB /* BillDetailsBaseCell.m */,
				77BDD74420AC0CB400762078 /* BillDetailsPrescriptionOrderCell.h */,
				77BDD74520AC0CB400762078 /* BillDetailsPrescriptionOrderCell.m */,
				77BDD74720AC0CC800762078 /* BillDetailsWithdrawCell.h */,
				77BDD74820AC0CC800762078 /* BillDetailsWithdrawCell.m */,
				77BDD75620AC2ECF00762078 /* BillDetailsWithdrawServiceChargeCell.h */,
				77BDD75720AC2ECF00762078 /* BillDetailsWithdrawServiceChargeCell.m */,
				77BDD74A20AC10AE00762078 /* BillDetailsWithdrawFailedCell.h */,
				77BDD74B20AC10AE00762078 /* BillDetailsWithdrawFailedCell.m */,
				77BDD75920AC2F3A00762078 /* BillDetailsWithdrawFailedServiceChargeCell.h */,
				77BDD75A20AC2F3A00762078 /* BillDetailsWithdrawFailedServiceChargeCell.m */,
				77BDD74D20AC10E400762078 /* BillDetailsConsultFeeCell.h */,
				77BDD74E20AC10E400762078 /* BillDetailsConsultFeeCell.m */,
				77BDD75020AC113F00762078 /* BillDetailsPatientRewardCell.h */,
				77BDD75120AC113F00762078 /* BillDetailsPatientRewardCell.m */,
				77BDD75320AC116A00762078 /* BillDetailsPlateformRewardCell.h */,
				77BDD75420AC116A00762078 /* BillDetailsPlateformRewardCell.m */,
				77BDD75C20AC506600762078 /* BillDetailsInviteCell.h */,
				77BDD75D20AC506600762078 /* BillDetailsInviteCell.m */,
				7795B576213E214E006A2A9E /* BillDetailsDrugStoreRewardCell.h */,
				7795B577213E214E006A2A9E /* BillDetailsDrugStoreRewardCell.m */,
				7718689620AD326500B62997 /* BRBillDetailsAccountOfMoneyCell.h */,
				7718689720AD326500B62997 /* BRBillDetailsAccountOfMoneyCell.m */,
				776FF328212C2CB300B9F71A /* DrugStoreHeaderView.h */,
				776FF329212C2CB300B9F71A /* DrugStoreHeaderView.m */,
				776FF32B212C348E00B9F71A /* DrugHeaderButton.h */,
				776FF32C212C348E00B9F71A /* DrugHeaderButton.m */,
				77264DF2212D3BFC002EE6C9 /* DrugStoreListCell.h */,
				77264DF3212D3BFC002EE6C9 /* DrugStoreListCell.m */,
				774DC662212EB17F008F4931 /* ShareContentView.h */,
				774DC663212EB17F008F4931 /* ShareContentView.m */,
				A6295B271FC9597200BD1A53 /* order */,
			);
			name = View;
			sourceTree = "<group>";
		};
		77CB751022D5CA6D00262B0C /* Broker */ = {
			isa = PBXGroup;
			children = (
				77CB751122D5CAF000262B0C /* BrokerOrderListViewController.h */,
				77CB751222D5CAF000262B0C /* BrokerOrderListViewController.m */,
				77D14A8B22D6EA150013CC7D /* BrokerOrderSearchViewController.h */,
				77D14A8C22D6EA150013CC7D /* BrokerOrderSearchViewController.m */,
			);
			name = Broker;
			sourceTree = "<group>";
		};
		77CB751422D5DD2300262B0C /* Broker */ = {
			isa = PBXGroup;
			children = (
				77CB751522D5DD4800262B0C /* BrokerOrderListCell.h */,
				77CB751622D5DD4800262B0C /* BrokerOrderListCell.m */,
				77E8B6C822F298E900D5C08C /* BrokerOrderUserCell.h */,
				77E8B6C922F298E900D5C08C /* BrokerOrderUserCell.m */,
			);
			name = Broker;
			sourceTree = "<group>";
		};
		77CB751822D5E7B900262B0C /* Broker */ = {
			isa = PBXGroup;
			children = (
				77CB751922D5E7D500262B0C /* BrokerInfoModel.h */,
				77CB751A22D5E7D500262B0C /* BrokerInfoModel.m */,
				77E8B6C522F2977D00D5C08C /* BrokerUserModel.h */,
				77E8B6C622F2977D00D5C08C /* BrokerUserModel.m */,
			);
			name = Broker;
			sourceTree = "<group>";
		};
		77D778271FEDEB1500CB1A6F /* Announcement */ = {
			isa = PBXGroup;
			children = (
				77D778281FEDEB3100CB1A6F /* BRAnnouncementView.h */,
				77D778291FEDEB3100CB1A6F /* BRAnnouncementView.m */,
			);
			path = Announcement;
			sourceTree = "<group>";
		};
		77DB71391F56E03100D07919 /* userCenter */ = {
			isa = PBXGroup;
			children = (
				A69F62D71F7CF5F9004B7AE8 /* UserCenterHeaderCell.h */,
				A69F62D81F7CF5F9004B7AE8 /* UserCenterHeaderCell.m */,
				A624598B1F8F71CE00C88DD4 /* WaitAcceptDoctorCell.h */,
				A624598C1F8F71CE00C88DD4 /* WaitAcceptDoctorCell.m */,
				A66EAD301F9063AB0065ADC3 /* TaskAllocationCell.h */,
				A66EAD311F9063AB0065ADC3 /* TaskAllocationCell.m */,
				A66EAD331F90979E0065ADC3 /* MyDoctorHeaderCell.h */,
				A66EAD341F90979E0065ADC3 /* MyDoctorHeaderCell.m */,
				A66EAD361F909A400065ADC3 /* MyDoctorListCell.h */,
				A66EAD371F909A400065ADC3 /* MyDoctorListCell.m */,
				A66EAD391F90AABD0065ADC3 /* ResultsQueryCell.h */,
				A66EAD3A1F90AABD0065ADC3 /* ResultsQueryCell.m */,
				A64761621F94B91800C84511 /* PatientNumberCell.h */,
				A64761631F94B91800C84511 /* PatientNumberCell.m */,
				A60B54531F95B07400123620 /* ResultSummaryCell.h */,
				A60B54541F95B07400123620 /* ResultSummaryCell.m */,
				A6C45FAE1F96FFAC00F85928 /* DoctorDetailCell.h */,
				A6C45FAF1F96FFAC00F85928 /* DoctorDetailCell.m */,
				A6A6A82E1FE93FBD00249235 /* CommonProblemsCell.h */,
				A6A6A82F1FE93FBD00249235 /* CommonProblemsCell.m */,
				A63F1FFC1FF5ED6B004A0DFA /* DownloadCell.h */,
				A63F1FFD1FF5ED6B004A0DFA /* DownloadCell.m */,
				A6FF5D8E204D38FE00261A8F /* DoctorStateCollecCell.h */,
				A6FF5D8F204D38FE00261A8F /* DoctorStateCollecCell.m */,
			);
			name = userCenter;
			sourceTree = "<group>";
		};
		77E870111FE3C466005E0F74 /* Models */ = {
			isa = PBXGroup;
			children = (
				77E870121FE3C48A005E0F74 /* HttpReceivedModel */,
				77BEE6621F5568E100597489 /* SocketReceiveModel */,
				77BEE65F1F5565BE00597489 /* BRSocketReceiveBaseModel.h */,
				77BEE6601F5565BE00597489 /* BRSocketReceiveBaseModel.m */,
			);
			name = Models;
			sourceTree = "<group>";
		};
		77E870121FE3C48A005E0F74 /* HttpReceivedModel */ = {
			isa = PBXGroup;
			children = (
				77E870131FE3C4AC005E0F74 /* BRContactModel.h */,
				77E870141FE3C4AC005E0F74 /* BRContactModel.m */,
			);
			name = HttpReceivedModel;
			sourceTree = "<group>";
		};
		77EA2615295DA01A000D1359 /* titleView */ = {
			isa = PBXGroup;
			children = (
				77EA2616295DA047000D1359 /* BRMessageTitleView.h */,
				77EA2617295DA047000D1359 /* BRMessageTitleView.m */,
			);
			name = titleView;
			sourceTree = "<group>";
		};
		77FCA5D01F60EC2D00369CB9 /* popover */ = {
			isa = PBXGroup;
			children = (
				77FCA5D11F60EC5400369CB9 /* PopListCell.h */,
				77FCA5D21F60EC5400369CB9 /* PopListCell.m */,
			);
			name = popover;
			sourceTree = "<group>";
		};
		77FCA5D41F6136D400369CB9 /* PopoverView */ = {
			isa = PBXGroup;
			children = (
				77FCA5D51F6136D400369CB9 /* PopoverAction.h */,
				77FCA5D61F6136D400369CB9 /* PopoverAction.m */,
				77FCA5D71F6136D400369CB9 /* PopoverView.h */,
				77FCA5D81F6136D400369CB9 /* PopoverView.m */,
				77FCA5D91F6136D400369CB9 /* PopoverViewCell.h */,
				77FCA5DA1F6136D400369CB9 /* PopoverViewCell.m */,
			);
			path = PopoverView;
			sourceTree = "<group>";
		};
		A6106C1F1FF39504006E5D32 /* toPush */ = {
			isa = PBXGroup;
			children = (
				A6106C201FF39543006E5D32 /* MyDoctorModel.h */,
				A6106C211FF39543006E5D32 /* MyDoctorModel.m */,
				A6106C231FF3AAC2006E5D32 /* QueryModel.h */,
				A6106C241FF3AAC2006E5D32 /* QueryModel.m */,
				A6106C261FF3CA4B006E5D32 /* ResultSummaryModel.h */,
				A6106C271FF3CA4B006E5D32 /* ResultSummaryModel.m */,
			);
			name = toPush;
			sourceTree = "<group>";
		};
		A61B101820ECA55200D52282 /* ZYPlayer */ = {
			isa = PBXGroup;
			children = (
				A661A7ED20EE1D9C009CFD40 /* AVPlayer */,
				A661A7B320EE1D9B009CFD40 /* ControlView */,
				A661A7D320EE1D9C009CFD40 /* Core */,
				A661A7CD20EE1D9C009CFD40 /* ijkplayer */,
				A661A7D020EE1D9C009CFD40 /* KSYMediaPlayer */,
			);
			name = ZYPlayer;
			sourceTree = "<group>";
		};
		A61BCE3A1FF24E48006C2EF1 /* architecture */ = {
			isa = PBXGroup;
			children = (
				A61BCE3B1FF24E84006C2EF1 /* ArchitectureModel.h */,
				A61BCE3C1FF24E84006C2EF1 /* ArchitectureModel.m */,
				A61BCE3E1FF25129006C2EF1 /* TeamListModel.h */,
				A61BCE3F1FF25129006C2EF1 /* TeamListModel.m */,
				A61BCE411FF2574B006C2EF1 /* UserListModel.h */,
				A61BCE421FF2574B006C2EF1 /* UserListModel.m */,
				A6106C1C1FF34CB7006E5D32 /* SchedulingModel.h */,
				A6106C1D1FF34CB7006E5D32 /* SchedulingModel.m */,
			);
			name = architecture;
			sourceTree = "<group>";
		};
		A61C69541FCEAA3C00E7AB32 /* commonlyUsedParty */ = {
			isa = PBXGroup;
			children = (
				A61C69551FCEAA8000E7AB32 /* CommonlyUsedPartyCell.h */,
				A61C69561FCEAA8000E7AB32 /* CommonlyUsedPartyCell.m */,
			);
			name = commonlyUsedParty;
			sourceTree = "<group>";
		};
		A6295B271FC9597200BD1A53 /* order */ = {
			isa = PBXGroup;
			children = (
				A6295B281FC959CF00BD1A53 /* OrderCell.h */,
				A6295B291FC959CF00BD1A53 /* OrderCell.m */,
				A660C9021FCBF25800D36C7C /* CompleteTotalOrGapCell.h */,
				A660C9031FCBF25800D36C7C /* CompleteTotalOrGapCell.m */,
				A660C9051FCBFE3C00D36C7C /* CompleteDetailCell.h */,
				A660C9061FCBFE3C00D36C7C /* CompleteDetailCell.m */,
				A660C9081FCC06D900D36C7C /* CompleteTotalCell.h */,
				A660C9091FCC06D900D36C7C /* CompleteTotalCell.m */,
				A6C74F27212D495F00BC2AD7 /* OrderRecordView.h */,
				A6C74F28212D495F00BC2AD7 /* OrderRecordView.m */,
				A6C74F2A212D5C2600BC2AD7 /* OrderRecordCell.h */,
				A6C74F2B212D5C2600BC2AD7 /* OrderRecordCell.m */,
				A6F85C5B212EB6FA00780DA6 /* CompletedView.h */,
				A6F85C5C212EB6FA00780DA6 /* CompletedView.m */,
				A6F85C5E212EBCEE00780DA6 /* CompletedCell.h */,
				A6F85C5F212EBCEE00780DA6 /* CompletedCell.m */,
			);
			name = order;
			sourceTree = "<group>";
		};
		A64703D2204E4D4000F33B3D /* doctorState */ = {
			isa = PBXGroup;
			children = (
				A64703D3204E8E0A00F33B3D /* InvitaUnregisteredCell.h */,
				A64703D4204E8E0A00F33B3D /* InvitaUnregisteredCell.m */,
				A64703D6204E92F500F33B3D /* RegisterUnauthorizedCell.h */,
				A64703D7204E92F500F33B3D /* RegisterUnauthorizedCell.m */,
				A64703D9204EA20F00F33B3D /* CertificationProcessCell.h */,
				A64703DA204EA20F00F33B3D /* CertificationProcessCell.m */,
				A64F46F72050060F004C5FAB /* CertificationFailCell.h */,
				A64F46F82050060F004C5FAB /* CertificationFailCell.m */,
			);
			name = doctorState;
			sourceTree = "<group>";
		};
		A64C6CBC1FC045D800138EB1 /* personalData */ = {
			isa = PBXGroup;
			children = (
				A64C6CBD1FC0464500138EB1 /* PersonalDataHeaderCell.h */,
				A64C6CBE1FC0464500138EB1 /* PersonalDataHeaderCell.m */,
				A64C6CC01FC0476800138EB1 /* PersonalDataListCell.h */,
				A64C6CC11FC0476800138EB1 /* PersonalDataListCell.m */,
			);
			name = personalData;
			sourceTree = "<group>";
		};
		A64F46F0204F7E40004C5FAB /* doctorStateMdel */ = {
			isa = PBXGroup;
			children = (
				A64F46F4204F806C004C5FAB /* DoctorStateModel.h */,
				A64F46F5204F806C004C5FAB /* DoctorStateModel.m */,
			);
			name = doctorStateMdel;
			sourceTree = "<group>";
		};
		A661A7B320EE1D9B009CFD40 /* ControlView */ = {
			isa = PBXGroup;
			children = (
				A661A7B420EE1D9B009CFD40 /* UIImageView+ZFCache.h */,
				A661A7B520EE1D9B009CFD40 /* UIImageView+ZFCache.m */,
				A661A7B620EE1D9B009CFD40 /* UIView+ZFFrame.h */,
				A661A7B720EE1D9B009CFD40 /* UIView+ZFFrame.m */,
				A661A7B820EE1D9B009CFD40 /* ZFLandScapeControlView.h */,
				A661A7B920EE1D9B009CFD40 /* ZFLandScapeControlView.m */,
				A661A7BA20EE1D9B009CFD40 /* ZFLoadingView.h */,
				A661A7BB20EE1D9B009CFD40 /* ZFLoadingView.m */,
				A661A7BC20EE1D9B009CFD40 /* ZFNetworkSpeedMonitor.h */,
				A661A7BD20EE1D9B009CFD40 /* ZFNetworkSpeedMonitor.m */,
				A661A7BE20EE1D9B009CFD40 /* ZFPlayer.bundle */,
				A661A7BF20EE1D9B009CFD40 /* ZFPlayerControlView.h */,
				A661A7C020EE1D9B009CFD40 /* ZFPlayerControlView.m */,
				A661A7C120EE1D9B009CFD40 /* ZFPortraitControlView.h */,
				A661A7C220EE1D9B009CFD40 /* ZFPortraitControlView.m */,
				A661A7C320EE1D9B009CFD40 /* ZFSliderView.h */,
				A661A7C420EE1D9B009CFD40 /* ZFSliderView.m */,
				A661A7C520EE1D9B009CFD40 /* ZFSmallFloatControlView.h */,
				A661A7C620EE1D9B009CFD40 /* ZFSmallFloatControlView.m */,
				A661A7C720EE1D9B009CFD40 /* ZFSpeedLoadingView.h */,
				A661A7C820EE1D9B009CFD40 /* ZFSpeedLoadingView.m */,
				A661A7C920EE1D9B009CFD40 /* ZFUtilities.h */,
				A661A7CA20EE1D9B009CFD40 /* ZFUtilities.m */,
				A661A7CB20EE1D9B009CFD40 /* ZFVolumeBrightnessView.h */,
				A661A7CC20EE1D9B009CFD40 /* ZFVolumeBrightnessView.m */,
			);
			path = ControlView;
			sourceTree = "<group>";
		};
		A661A7CD20EE1D9C009CFD40 /* ijkplayer */ = {
			isa = PBXGroup;
			children = (
				A661A7CE20EE1D9C009CFD40 /* ZFIJKPlayerManager.h */,
				A661A7CF20EE1D9C009CFD40 /* ZFIJKPlayerManager.m */,
			);
			path = ijkplayer;
			sourceTree = "<group>";
		};
		A661A7D020EE1D9C009CFD40 /* KSYMediaPlayer */ = {
			isa = PBXGroup;
			children = (
				A661A7D120EE1D9C009CFD40 /* KSMediaPlayerManager.h */,
				A661A7D220EE1D9C009CFD40 /* KSMediaPlayerManager.m */,
			);
			path = KSYMediaPlayer;
			sourceTree = "<group>";
		};
		A661A7D320EE1D9C009CFD40 /* Core */ = {
			isa = PBXGroup;
			children = (
				A661A7D420EE1D9C009CFD40 /* UIScrollView+ZFPlayer.h */,
				A661A7D520EE1D9C009CFD40 /* UIScrollView+ZFPlayer.m */,
				A661A7D620EE1D9C009CFD40 /* UIViewController+ZFPlayerRotation.h */,
				A661A7D720EE1D9C009CFD40 /* UIViewController+ZFPlayerRotation.m */,
				A661A7D820EE1D9C009CFD40 /* ZFFloatView.h */,
				A661A7D920EE1D9C009CFD40 /* ZFFloatView.m */,
				A661A7DA20EE1D9C009CFD40 /* ZFKVOController.h */,
				A661A7DB20EE1D9C009CFD40 /* ZFKVOController.m */,
				A661A7DC20EE1D9C009CFD40 /* ZFOrientationObserver.h */,
				A661A7DD20EE1D9C009CFD40 /* ZFOrientationObserver.m */,
				A661A7DE20EE1D9C009CFD40 /* ZFPlayer.h */,
				A661A7DF20EE1D9C009CFD40 /* ZFPlayerController.h */,
				A661A7E020EE1D9C009CFD40 /* ZFPlayerController.m */,
				A661A7E120EE1D9C009CFD40 /* ZFPlayerGestureControl.h */,
				A661A7E220EE1D9C009CFD40 /* ZFPlayerGestureControl.m */,
				A661A7E320EE1D9C009CFD40 /* ZFPlayerLogManager.h */,
				A661A7E420EE1D9C009CFD40 /* ZFPlayerLogManager.m */,
				A661A7E520EE1D9C009CFD40 /* ZFPlayerMediaControl.h */,
				A661A7E620EE1D9C009CFD40 /* ZFPlayerMediaPlayback.h */,
				A661A7E720EE1D9C009CFD40 /* ZFPlayerNotification.h */,
				A661A7E820EE1D9C009CFD40 /* ZFPlayerNotification.m */,
				A661A7E920EE1D9C009CFD40 /* ZFPlayerView.h */,
				A661A7EA20EE1D9C009CFD40 /* ZFPlayerView.m */,
				A661A7EB20EE1D9C009CFD40 /* ZFReachabilityManager.h */,
				A661A7EC20EE1D9C009CFD40 /* ZFReachabilityManager.m */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		A661A7ED20EE1D9C009CFD40 /* AVPlayer */ = {
			isa = PBXGroup;
			children = (
				A661A7EE20EE1D9C009CFD40 /* ZFAVPlayerManager.h */,
				A661A7EF20EE1D9C009CFD40 /* ZFAVPlayerManager.m */,
			);
			path = AVPlayer;
			sourceTree = "<group>";
		};
		A68CE8B71FE2776F008E5F05 /* agent */ = {
			isa = PBXGroup;
			children = (
				A68CE8B81FE277A8008E5F05 /* SchedulingCell.h */,
				A68CE8B91FE277A8008E5F05 /* SchedulingCell.m */,
				A60DECED1FE3684100FE2C42 /* ArchitectureCell.h */,
				A60DECEE1FE3684100FE2C42 /* ArchitectureCell.m */,
				A69A301C1FFBB9C0007E9413 /* ArchitectureCollectionViewCell.h */,
				A69A301D1FFBB9C0007E9413 /* ArchitectureCollectionViewCell.m */,
				A60629B21FFC77A9004BA00A /* HeadCollectionReusableView.h */,
				A60629B31FFC77A9004BA00A /* HeadCollectionReusableView.m */,
				A60DECF01FE3718700FE2C42 /* ArchitectureTabCell.h */,
				A60DECF11FE3718700FE2C42 /* ArchitectureTabCell.m */,
				A6FC852B1FF4E416005B6CA8 /* ChooseAgentCell.h */,
				A6FC852C1FF4E416005B6CA8 /* ChooseAgentCell.m */,
				A69A30191FFBAEDB007E9413 /* AllocationCell.h */,
				A69A301A1FFBAEDB007E9413 /* AllocationCell.m */,
				A60629C41FFD0E74004BA00A /* SearchArchitectureCell.h */,
				A60629C51FFD0E74004BA00A /* SearchArchitectureCell.m */,
			);
			name = agent;
			sourceTree = "<group>";
		};
		A6FF5D8D204D368100261A8F /* DoctorState */ = {
			isa = PBXGroup;
			children = (
				A624597F1F8F64A300C88DD4 /* MyDoctorViewController.h */,
				A62459801F8F64A300C88DD4 /* MyDoctorViewController.m */,
				A64703B7204E302800F33B3D /* InvitaUnregisteredViewController.h */,
				A64703B8204E302800F33B3D /* InvitaUnregisteredViewController.m */,
				A64703BA204E310900F33B3D /* RegisterUnauthorizedViewController.h */,
				A64703BB204E310A00F33B3D /* RegisterUnauthorizedViewController.m */,
				A64703BD204E323C00F33B3D /* CertificationProcessViewController.h */,
				A64703BE204E323C00F33B3D /* CertificationProcessViewController.m */,
				A64703C0204E32CB00F33B3D /* CertificationFailViewController.h */,
				A64703C1204E32CB00F33B3D /* CertificationFailViewController.m */,
				A64703C3204E33AA00F33B3D /* CertificationNoPatientViewController.h */,
				A64703C4204E33AA00F33B3D /* CertificationNoPatientViewController.m */,
				A64703C6204E340F00F33B3D /* NoPrescriptionViewController.h */,
				A64703C7204E340F00F33B3D /* NoPrescriptionViewController.m */,
				A64703C9204E357F00F33B3D /* PrescriptionNoPayViewController.h */,
				A64703CA204E357F00F33B3D /* PrescriptionNoPayViewController.m */,
				A64703CC204E35E600F33B3D /* CertificationViewController.h */,
				A64703CD204E35E600F33B3D /* CertificationViewController.m */,
				A64703CF204E3F7700F33B3D /* AbnormalDoctorViewController.h */,
				A64703D0204E3F7700F33B3D /* AbnormalDoctorViewController.m */,
				A67CF4B82058B9B900E2DEEA /* TwoWeeksNotPrescViewController.h */,
				A67CF4B92058B9B900E2DEEA /* TwoWeeksNotPrescViewController.m */,
				A67CF4BB2058BA0700E2DEEA /* PrescLessOneHundredViewController.h */,
				A67CF4BC2058BA0700E2DEEA /* PrescLessOneHundredViewController.m */,
			);
			name = DoctorState;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7742ECA91F4BD7ED00A9B110 /* BRZY */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7742ECC11F4BD7ED00A9B110 /* Build configuration list for PBXNativeTarget "BRZY" */;
			buildPhases = (
				F8E7353A087E3AC402AAFD5F /* [CP] Check Pods Manifest.lock */,
				7742ECA61F4BD7ED00A9B110 /* Sources */,
				7742ECA71F4BD7ED00A9B110 /* Frameworks */,
				7742ECA81F4BD7ED00A9B110 /* Resources */,
				99A3FAC7DFB0B8AA092E8C19 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = BRZY;
			productName = BRZY;
			productReference = 7742ECAA1F4BD7ED00A9B110 /* BRZY.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7742ECA21F4BD7ED00A9B110 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0830;
				ORGANIZATIONNAME = "Yi YiKang Technology(Beijing) Co.,Ltd";
				TargetAttributes = {
					7742ECA91F4BD7ED00A9B110 = {
						CreatedOnToolsVersion = 8.3.3;
						DevelopmentTeam = NTQ8FSBK83;
						LastSwiftMigration = 1600;
						ProvisioningStyle = Automatic;
						SystemCapabilities = {
							com.apple.NetworkExtensions = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = 7742ECA51F4BD7ED00A9B110 /* Build configuration list for PBXProject "BRZY" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				Base,
				en,
				"zh-Hans",
			);
			mainGroup = 7742ECA11F4BD7ED00A9B110;
			productRefGroup = 7742ECAB1F4BD7ED00A9B110 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7742ECA91F4BD7ED00A9B110 /* BRZY */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7742ECA81F4BD7ED00A9B110 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				A661A7F520EE1D9C009CFD40 /* ZFPlayer.bundle in Resources */,
				771F66F11FC8090F002AAA5C /* BRPatientDocumentTableViewCell.xib in Resources */,
				7764AB0E1F60081800B26332 /* Localizable.strings in Resources */,
				7731F8862E42CA5400A7CC99 /* mapping.txt in Resources */,
				77089A802CF0991C00C9E6C3 /* OcrSDK.bundle in Resources */,
				7742ECBA1F4BD7ED00A9B110 /* Assets.xcassets in Resources */,
				77F6D00D2D12878E00247F4E /* .env in Resources */,
				7733F26A1FCEC9890085B43F /* BRHistoryTableViewCell.xib in Resources */,
				7724004F1F5659600015E0E3 /* ModulesRegister.plist in Resources */,
				779642A11FC4214500AA0976 /* BRCalendarCollectionViewCell.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		99A3FAC7DFB0B8AA092E8C19 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-BRZY/Pods-BRZY-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/AFNetworking/AFNetworking.framework",
				"${BUILT_PRODUCTS_DIR}/ASIHTTPRequest/ASIHTTPRequest.framework",
				"${BUILT_PRODUCTS_DIR}/CocoaAsyncSocket/CocoaAsyncSocket.framework",
				"${BUILT_PRODUCTS_DIR}/DACircularProgress/DACircularProgress.framework",
				"${BUILT_PRODUCTS_DIR}/DZNEmptyDataSet/DZNEmptyDataSet.framework",
				"${BUILT_PRODUCTS_DIR}/DateTools/DateTools.framework",
				"${BUILT_PRODUCTS_DIR}/IQKeyboardManager/IQKeyboardManager.framework",
				"${BUILT_PRODUCTS_DIR}/LSTPopView/LSTPopView.framework",
				"${BUILT_PRODUCTS_DIR}/LSTTimer/LSTTimer.framework",
				"${BUILT_PRODUCTS_DIR}/MBProgressHUD/MBProgressHUD.framework",
				"${BUILT_PRODUCTS_DIR}/MJExtension/MJExtension.framework",
				"${BUILT_PRODUCTS_DIR}/MJRefresh/MJRefresh.framework",
				"${BUILT_PRODUCTS_DIR}/MWPhotoBrowser/MWPhotoBrowser.framework",
				"${BUILT_PRODUCTS_DIR}/Masonry/Masonry.framework",
				"${BUILT_PRODUCTS_DIR}/Reachability/Reachability.framework",
				"${BUILT_PRODUCTS_DIR}/ReactiveObjC/ReactiveObjC.framework",
				"${BUILT_PRODUCTS_DIR}/SDWebImage/SDWebImage.framework",
				"${BUILT_PRODUCTS_DIR}/Toast/Toast.framework",
				"${BUILT_PRODUCTS_DIR}/WCDB/WCDB.framework",
				"${BUILT_PRODUCTS_DIR}/WCDBOptimizedSQLCipher/sqlcipher.framework",
				"${BUILT_PRODUCTS_DIR}/YYKit/YYKit.framework",
				"${BUILT_PRODUCTS_DIR}/ZFDownload/ZFDownload.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AFNetworking.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ASIHTTPRequest.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/CocoaAsyncSocket.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/DACircularProgress.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/DZNEmptyDataSet.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/DateTools.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/IQKeyboardManager.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/LSTPopView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/LSTTimer.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MBProgressHUD.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MJExtension.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MJRefresh.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/MWPhotoBrowser.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Masonry.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Reachability.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ReactiveObjC.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SDWebImage.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Toast.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/WCDB.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/sqlcipher.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/YYKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ZFDownload.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-BRZY/Pods-BRZY-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		F8E7353A087E3AC402AAFD5F /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-BRZY-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7742ECA61F4BD7ED00A9B110 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				778E0B65279BCBD700F9BA6B /* BRWechatPocketMoneyViewController.m in Sources */,
				0A69BE181FD246D5009F8630 /* BRPresInfoView.m in Sources */,
				774C2DDC1FEE5E87004CD7E2 /* PatientModel.m in Sources */,
				A62459841F8F664100C88DD4 /* ResultsQueryViewController.m in Sources */,
				0FB534B820F4B61600E084B7 /* PhotoTableView.m in Sources */,
				0A6F25411FB3FF8D001D4FC9 /* BRPrescriptionInputPanel.m in Sources */,
				0A69BE1F1FD2ACD8009F8630 /* BRPresSelectTypeMasterCell.m in Sources */,
				0ABEE64F1FC2B551006FE61B /* BRPresNoteView.m in Sources */,
				7742ED091F4C274F00A9B110 /* UserCenterViewController.m in Sources */,
				776366001F4FC2D800AF92A1 /* UITextField+Max.m in Sources */,
				7770FD6C1F5408AD00651814 /* IMContactManager.m in Sources */,
				7712BE351FD928080064B0F6 /* BRSupplementAnswerMessageCell.m in Sources */,
				77B4819F1F6683F200466F3B /* BRBaseMessageCell.m in Sources */,
				77264C961F58F647002C9CBD /* MyQRCodeViewController.m in Sources */,
				7757A81F1FE4BE6C00B065A5 /* SystemMessageViewController.m in Sources */,
				A67CF4BD2058BA0700E2DEEA /* PrescLessOneHundredViewController.m in Sources */,
				A62003D120901A9400D1B82F /* AvplayerViewController.m in Sources */,
				A64761551F94B00000C84511 /* PatientNumberViewController.m in Sources */,
				77D39E511FD2A5F1004A01E0 /* UITextView+MaxLength.m in Sources */,
				77475AE31FF26D740066F5C8 /* HistorySelectPatientView.m in Sources */,
				A61044E12057F4A20055B6B2 /* SearchDownloadViewController.m in Sources */,
				779F1799213CDD2700F44BE3 /* PatientFooterView.m in Sources */,
				7774866327B7F39D00B49BB4 /* AccountTerminateViewController.m in Sources */,
				775E751520AA76B600EAA5DB /* BillDetailsHeaderView.m in Sources */,
				0A7FD4681FCE8DCC00318462 /* BRDatePicker.m in Sources */,
				77B684981FFFAC2F00A1FFF8 /* BRSessionStartPatientMessageCell.m in Sources */,
				0A8CFB3C1FC7B704006A449E /* BRPresContraindicationView.m in Sources */,
				A68CE8B31FE275A1008E5F05 /* SchedulingViewController.m in Sources */,
				A67CF4BA2058B9B900E2DEEA /* TwoWeeksNotPrescViewController.m in Sources */,
				772901961F5D385A008C51F6 /* UIView+BR.m in Sources */,
				77B4817A1F663B8400466F3B /* IMChatInputPanel.m in Sources */,
				A64703C8204E340F00F33B3D /* NoPrescriptionViewController.m in Sources */,
				776BDF0A1FE522F600CBD754 /* BRMedicationWarning.m in Sources */,
				7762973C1FDA69CC00E480F2 /* SessionHistoryViewController.m in Sources */,
				A64703D1204E3F7700F33B3D /* AbnormalDoctorViewController.m in Sources */,
				7752437F1FC977CB00EA2ED3 /* BRVisitsArrangementModel.m in Sources */,
				0A42DD161FD7DEFA0060D4F3 /* BRPresSelectTypeDetailCell.m in Sources */,
				77131EA71F4D76D2006C9F79 /* UINavigationBar+BackgroundColor.m in Sources */,
				779642A21FC4214500AA0976 /* BRCalendarView.m in Sources */,
				7720F1991FE8F658009DC35C /* BRSessionRosterUserModel.m in Sources */,
				0A97FEDD1FE263F50043D14D /* BRPresReplaceDrugTitleCell.m in Sources */,
				77C2932B293C29EA001A5B45 /* DrugInfoListCell.m in Sources */,
				7796429E1FC4214500AA0976 /* BRAddVisitsViewController.m in Sources */,
				77D39E501FD2A5F1004A01E0 /* UITextView+APSUIControlTargetAction.m in Sources */,
				A661A7F720EE1D9C009CFD40 /* ZFPortraitControlView.m in Sources */,
				7742ED061F4C273200A9B110 /* InviteViewController.m in Sources */,
				775E751B20AAEB0600EAA5DB /* BillDetailsBaseCell.m in Sources */,
				A661A7F620EE1D9C009CFD40 /* ZFPlayerControlView.m in Sources */,
				7705516D1FDBC99D000306B1 /* BRBankCardListViewController.m in Sources */,
				77BDD74C20AC10AE00762078 /* BillDetailsWithdrawFailedCell.m in Sources */,
				0A2999221FCBB38B003C5CAA /* BRSearchModel.m in Sources */,
				A67CF4C0205949B500E2DEEA /* SearchChooseViewController.m in Sources */,
				775967351FFB280700ADC587 /* DoctorAuthentiationModel.m in Sources */,
				770D3E1A200753CF00D48C5A /* BRShareView.m in Sources */,
				77DB713F1F56F34E00D07919 /* UserCenterHeaderView.m in Sources */,
				771E520B2134E9DE006B6ACB /* DrugStoreModel.m in Sources */,
				7751DD5E1F4EAF8800C10C3C /* LoginViewController.m in Sources */,
				7721B3F51FC43299000A6DFA /* BRActionSheetView.m in Sources */,
				0AA18C841FF33A9D00CC3C61 /* ZYActivityImgView.m in Sources */,
				A636959C1FE7DF5C002DFB0A /* BlackListCell.m in Sources */,
				A66EAD381F909A400065ADC3 /* MyDoctorListCell.m in Sources */,
				A6C45FB31F9735B100F85928 /* RegisterViewController.m in Sources */,
				7720F19C1FE8F9B2009DC35C /* BRSessionListItemModel.m in Sources */,
				A68CE8AA1FE22289008E5F05 /* OrdersModel.m in Sources */,
				77B481841F6660E600466F3B /* IMChatViewController.m in Sources */,
				7781E2A21F4D1D4700DDD047 /* IMDataBaseManager.mm in Sources */,
				A6FF5D90204D38FE00261A8F /* DoctorStateCollecCell.m in Sources */,
				770FE95C1FF33D1800008C78 /* ShowMyPurseSMSCode.m in Sources */,
				0A9CED1D1F7B7BFF0072CF4A /* BRPrescriptionDiagnosesView.m in Sources */,
				776153541FCD74D0006A2FAC /* BRComPrescriptionViewController.m in Sources */,
				77327D831FC283380023939F /* BRCustomSystemMessageCellLayout.m in Sources */,
				A642A8632137E8EF00C8FA8A /* GoodsContentViewController.m in Sources */,
				A661A80620EE1D9C009CFD40 /* ZFPlayerLogManager.m in Sources */,
				77B481B11F668DC900466F3B /* BRSystemMessageCell.m in Sources */,
				A69A301E1FFBB9C0007E9413 /* ArchitectureCollectionViewCell.m in Sources */,
				773E8AF82CD9E62B0096F0A5 /* BRAdjustByMultipleContentView.m in Sources */,
				7790B2082E06594800A08108 /* WithdrawPasswordViewController.m in Sources */,
				A636959F1FE7E87C002DFB0A /* BlackListModel.m in Sources */,
				776191581F5A436100A19B77 /* UIImageView+Badge.m in Sources */,
				0A5F4D5A1FC0313800491FE7 /* BRPresOtherView.m in Sources */,
				77264C991F590452002C9CBD /* BaseViewController.m in Sources */,
				0F43AEF120FF352500D568B5 /* PhotoPresWaitCell.m in Sources */,
				A661A80320EE1D9C009CFD40 /* ZFOrientationObserver.m in Sources */,
				777B879D2CFC1A7C00004875 /* CustomBoilyWayCell.m in Sources */,
				0A71DB211F976DDF00BF831F /* BRPresNoticeView.m in Sources */,
				77B481BA1F668FC100466F3B /* BRDateMessageCell.m in Sources */,
				7774866027B79B1100B49BB4 /* PatientComplainViewController.m in Sources */,
				A624597E1F8F640800C88DD4 /* WaitAcceptDoctorViewController.m in Sources */,
				7742ED101F4C346800A9B110 /* UIColor+Util.m in Sources */,
				A661A80A20EE1D9C009CFD40 /* ZFAVPlayerManager.m in Sources */,
				77131EA41F4D758D006C9F79 /* NSDate+Message.m in Sources */,
				77BDD74920AC0CC800762078 /* BillDetailsWithdrawCell.m in Sources */,
				77C021F528BAF4E000F551AF /* EnterQuickOrderViewController.m in Sources */,
				77B4818E1F66740A00466F3B /* HPTextViewInternal.m in Sources */,
				A6A6A8301FE93FBD00249235 /* CommonProblemsCell.m in Sources */,
				0ACF4F291FC974A500A3B87F /* MMNumberKeyboard.m in Sources */,
				77293F9F1FC9659F00329DC2 /* RRFPSBar.m in Sources */,
				0AF203261F8B6E8F00432DD6 /* BRPresDescTitleView.m in Sources */,
				0FB534AB20F488B200E084B7 /* BRCanTapImgView.m in Sources */,
				7751DD611F4EE06400C10C3C /* LoginInputView.m in Sources */,
				A60629C61FFD0E74004BA00A /* SearchArchitectureCell.m in Sources */,
				7712BE121FD919E00064B0F6 /* BRWzdAnswerMessageCell.m in Sources */,
				77EF7D6F1FEB549800232BC0 /* PatientsListXiaoRanView.m in Sources */,
				7793B9642C15F1DD006AD7FF /* BRMessagePrescribeViewController.m in Sources */,
				77F7A1461F5E3D6600EA5FC1 /* UserCenterListCell.m in Sources */,
				7744A6241FEA07D000AE7ABD /* PatientInfoView.m in Sources */,
				A60629B71FFCD39A004BA00A /* PharmacopeiaCache.m in Sources */,
				77494F3128A722FF0000132A /* BRWritePatientInfoView.m in Sources */,
				777A239A2CF80E2300C0853F /* BRHerbInfo.m in Sources */,
				77327D801FC2830F0023939F /* BRCustomSystemMessageCell.m in Sources */,
				A61C694A1FCE5A8400E7AB32 /* MyAnnouncementViewController.m in Sources */,
				77EA261B295EDE64000D1359 /* BRCashTypeInfoModel.m in Sources */,
				A661A80720EE1D9C009CFD40 /* ZFPlayerNotification.m in Sources */,
				7742ED001F4C26E400A9B110 /* MessageListViewController.m in Sources */,
				779642851FC40B9A00AA0976 /* BRSelectCityZone.m in Sources */,
				776F86021FF496D600CAAE33 /* WzdWebViewController.m in Sources */,
				778E0B75279D341100F9BA6B /* BRPrivacyPopView.m in Sources */,
				77B3A54D2D24465000097E8A /* BRDrugSpecSelectView.m in Sources */,
				776365FD1F4FC19300AF92A1 /* UIButton+HotPointButtonCategory.m in Sources */,
				0A5F4D511FBC3B5F00491FE7 /* PrescriptionViewController.m in Sources */,
				77327D761FC269F50023939F /* BRSessionStartMessageCell.m in Sources */,
				775E751820AADE4B00EAA5DB /* BillDetailsModel.m in Sources */,
				7786ECE91FF0AACD008BEEE7 /* PatientSelectedInfoModel.m in Sources */,
				7735EF381F62A9D4003CE02D /* PatientDocumentViewController.m in Sources */,
				779642A31FC4214500AA0976 /* BRMyPurseViewController.m in Sources */,
				7756569F1F62849000F779C2 /* PatientSearchViewController.m in Sources */,
				7744A6211FEA068700AE7ABD /* LogisticInfoViewController.m in Sources */,
				0FB534B220F4A40100E084B7 /* PhotoPresCell.m in Sources */,
				A660C90A1FCC06D900D36C7C /* CompleteTotalCell.m in Sources */,
				77E8B6C722F2977D00D5C08C /* BrokerUserModel.m in Sources */,
				0F419020204FEC73005B81D3 /* BRGuidePageView.m in Sources */,
				A61BCE431FF2574B006C2EF1 /* UserListModel.m in Sources */,
				A66EAD3B1F90AABD0065ADC3 /* ResultsQueryCell.m in Sources */,
				A60629B41FFC77A9004BA00A /* HeadCollectionReusableView.m in Sources */,
				779642841FC40B9A00AA0976 /* UILabel+myLabel.m in Sources */,
				7712BE241FD9205C0064B0F6 /* BRFzdQuestionMessageCellLayout.m in Sources */,
				771736C61FA9689500ADE932 /* BRMessageModel.m in Sources */,
				777727D5276DA73400DA7FD4 /* EbookMallListViewController.m in Sources */,
				77FCA5DC1F6136D400369CB9 /* PopoverView.m in Sources */,
				A60629C01FFCFD4F004BA00A /* ProblemSolvingViewController.m in Sources */,
				A61C69571FCEAA8000E7AB32 /* CommonlyUsedPartyCell.m in Sources */,
				77B4817D1F665DDB00466F3B /* IMChatItemCell.m in Sources */,
				773D91422CF5D8B100547A91 /* BRTencentOCRRequest.m in Sources */,
				772901991F5D3C96008C51F6 /* BRBadgeView.m in Sources */,
				A661A7F120EE1D9C009CFD40 /* UIView+ZFFrame.m in Sources */,
				A68FA27B1FD5435800DAF689 /* SeeTheSampleViewController.m in Sources */,
				77B481771F663A8D00466F3B /* IMChatContainerView.m in Sources */,
				77327D7C1FC275A00023939F /* BRSessionEndMessageCellLayout.m in Sources */,
				A6C74F26212D07D800BC2AD7 /* OrderRecordViewController.m in Sources */,
				A649543F1F8B72D600F16C42 /* QualificationCertificationViewController.m in Sources */,
				A6295B2A1FC959CF00BD1A53 /* OrderCell.m in Sources */,
				A66EAD321F9063AB0065ADC3 /* TaskAllocationCell.m in Sources */,
				77399A0D20060B8F00CA2277 /* BRDataEmptyView.m in Sources */,
				77D7782A1FEDEB3100CB1A6F /* BRAnnouncementView.m in Sources */,
				77BDD75520AC116A00762078 /* BillDetailsPlateformRewardCell.m in Sources */,
				774AE86D1FD7FDCE009AFEF9 /* BRBankCardViewController.m in Sources */,
				776A6769200EE4F100412402 /* BRGuideView.m in Sources */,
				7726F6061F95902100D2E07B /* BRImageMessageCellLayout.m in Sources */,
				7742ECE51F4C183B00A9B110 /* BaseTabBarController.m in Sources */,
				77F7A1491F5E3DCF00EA5FC1 /* ListCellModel.m in Sources */,
				0AA18C871FF33A9D00CC3C61 /* ZYScrollView.m in Sources */,
				77194D971FD0026800716835 /* BRDHTextField.m in Sources */,
				778139231FF2521700852B27 /* PatientsDocumentModel.m in Sources */,
				772400531F5665850015E0E3 /* BRAppStartModule.m in Sources */,
				77BDD74620AC0CB400762078 /* BillDetailsPrescriptionOrderCell.m in Sources */,
				77131EAB1F4D7D6B006C9F79 /* Reachability.m in Sources */,
				77A3BB5D2D09D65F0074A786 /* BRUpdateUserInfoModel.m in Sources */,
				77E59D9A2C8C19B000CB2662 /* BRCustomPopupView.m in Sources */,
				A6106C281FF3CA4B006E5D32 /* ResultSummaryModel.m in Sources */,
				77ABE81728B0F74800176DC3 /* QuickReplyQuesAddViewController.m in Sources */,
				778E0B6B279BD15A00F9BA6B /* WechatWithDrawSumCell.m in Sources */,
				A6E4212C20AE82CB00321AB4 /* SummaryMonthViewController.m in Sources */,
				7733F2621FCE51530085B43F /* BRHistoryView.m in Sources */,
				A64F46F92050060F004C5FAB /* CertificationFailCell.m in Sources */,
				A6C45FB01F96FFAC00F85928 /* DoctorDetailCell.m in Sources */,
				776633961F5E980200CF7029 /* IMTableSession.mm in Sources */,
				A64703DB204EA20F00F33B3D /* CertificationProcessCell.m in Sources */,
				77FCA5DD1F6136D400369CB9 /* PopoverViewCell.m in Sources */,
				77A7DF991FA09F520012C18C /* EWVoiceHUD.m in Sources */,
				77FCA5E01F61512000369CB9 /* TitleBarView.m in Sources */,
				0ACF4F251FC943A400A3B87F /* BRPresHaveNoticeCell.m in Sources */,
				77ABE80728B0A8BD00176DC3 /* QuickReplyShowView.m in Sources */,
				7712BE211FD920330064B0F6 /* BRFzdQuestionMessageCell.m in Sources */,
				0AA18C821FF33A9D00CC3C61 /* ZYActivitiesView.m in Sources */,
				772E57F21FF1E319008252E0 /* BRTablePharmacopeia.mm in Sources */,
				77BEE6611F5565BE00597489 /* BRSocketReceiveBaseModel.m in Sources */,
				778E0B78279D6C7600F9BA6B /* FirstOpenViewController.m in Sources */,
				770B62F01FCD565500F6380C /* ConfigInfo.m in Sources */,
				77C5D3C91F7A4D6800E836BE /* HTTPRequest.m in Sources */,
				7712BE181FD91FC60064B0F6 /* BRWzdQuestionMessageCell.m in Sources */,
				A68CE8B01FE272C7008E5F05 /* CMOAssistantViewController.m in Sources */,
				A62459871F8F673D00C88DD4 /* TaskAllocationViewController.m in Sources */,
				773D913F2CF5D63D00547A91 /* BRTencentCloudAPISignature.m in Sources */,
				0A73F4701FE51A9000125F2D /* BRRiskTipModel.m in Sources */,
				77ABE81428B0EB9900176DC3 /* QuickReplyContentListCell.m in Sources */,
				776366031F4FD00100AF92A1 /* UserInfo.m in Sources */,
				A6F85C5A212E6D2E00780DA6 /* OrderRecordModel.m in Sources */,
				A69A301B1FFBAEDB007E9413 /* AllocationCell.m in Sources */,
				77CFB9462AD4D77600A1EA90 /* BeianViewController.m in Sources */,
				A64954481F8B77B100F16C42 /* ProblemFeedbackViewController.m in Sources */,
				777727DE276DE62C00DA7FD4 /* EbookMallListCell.m in Sources */,
				770CBCEB1FDFDC2300C27AB3 /* FrequentQuestionShowView.m in Sources */,
				77E8B6CA22F298E900D5C08C /* BrokerOrderUserCell.m in Sources */,
				7742ECF21F4C234F00A9B110 /* ViewTools.m in Sources */,
				776FF32A212C2CB300B9F71A /* DrugStoreHeaderView.m in Sources */,
				77C5D3C61F7A499500E836BE /* ManagerViewController.m in Sources */,
				77B481961F6676E100466F3B /* BRChatInputTextPanel.m in Sources */,
				A6FC852A1FF4E35A005B6CA8 /* ChooseAgentViewController.m in Sources */,
				A61BCE401FF25129006C2EF1 /* TeamListModel.m in Sources */,
				A64954451F8B76DF00F16C42 /* CommonProblemsViewController.m in Sources */,
				7742ED0C1F4C2A1E00A9B110 /* BaseNavigationController.m in Sources */,
				A624598A1F8F715900C88DD4 /* WaitAcceptDoctorModel.m in Sources */,
				77286C882057CAB700117038 /* ClassicSearchModel.m in Sources */,
				771C15CF1F4D709200099626 /* UIView+Util.m in Sources */,
				770CBCE81FDFD04100C27AB3 /* QuestionListCell.m in Sources */,
				A661A7FC20EE1D9C009CFD40 /* ZFVolumeBrightnessView.m in Sources */,
				77BC46B52000DCCE0066CE29 /* BRVisitsHospitalName.m in Sources */,
				A6A574DA213528FD007F90AE /* OrderContentViewController.m in Sources */,
				777DC1F7273D687D00A594D7 /* JTPrivacyWebViewController.m in Sources */,
				A620D4EB20EF13FC0022A052 /* PhotoPrescAgreementViewController.m in Sources */,
				772A7D091FA19066001AE0BB /* BRAudioMessageCellLayout.m in Sources */,
				7724007F1F56D9DA0015E0E3 /* PanelIconModel.m in Sources */,
				A63F20011FF62ED5004A0DFA /* DownloadModel.m in Sources */,
				A661A7FA20EE1D9C009CFD40 /* ZFSpeedLoadingView.m in Sources */,
				A61C694D1FCE5B4300E7AB32 /* BlacklistViewController.m in Sources */,
				0AF2032C1F8CBF8800432DD6 /* BRPrescriptionChargeView.m in Sources */,
				77E870151FE3C4AC005E0F74 /* BRContactModel.m in Sources */,
				7721B3EA1FC422E7000A6DFA /* BRWzdBaseMessageCell.m in Sources */,
				7724004D1F5658B10015E0E3 /* BRThirdPartModule.m in Sources */,
				776FF32D212C348E00B9F71A /* DrugHeaderButton.m in Sources */,
				0FB534BB20F4D22100E084B7 /* PhotoPresDetailViewController.m in Sources */,
				A69A30151FFB90AD007E9413 /* ScanQrcodeViewController.m in Sources */,
				770044482C8EE94700236FF1 /* FloatingButton.m in Sources */,
				7733F25F1FCE51430085B43F /* BRComView.m in Sources */,
				A64E491C1F8C6BC50060CCDF /* PersonalDataViewController.m in Sources */,
				7712BE1E1FD920130064B0F6 /* BRFzdAnswerMessageCell.m in Sources */,
				772400441F56549C0015E0E3 /* BRModuleManager.m in Sources */,
				772B10C71FA85F1A00D894EA /* IMUIHelper.m in Sources */,
				7705516A1FDBBBBD000306B1 /* BRBinDingViewController.m in Sources */,
				A6C45FAD1F96F64900F85928 /* DoctorDetailsViewController.m in Sources */,
				7744A6281FEA3C8300AE7ABD /* PatientInfoListCell.m in Sources */,
				A61C69501FCE5BF400E7AB32 /* PharmacopoeiaViewController.m in Sources */,
				7733F2691FCEC9890085B43F /* BRHistoryTableViewCell.m in Sources */,
				772F70D7205139FE004BFE4D /* BRStockInfoModel.m in Sources */,
				77BDD75820AC2ECF00762078 /* BillDetailsWithdrawServiceChargeCell.m in Sources */,
				A67B75871FC51FC4005C637F /* OrderViewController.m in Sources */,
				77FCA5DB1F6136D400369CB9 /* PopoverAction.m in Sources */,
				77BDD74F20AC10E400762078 /* BillDetailsConsultFeeCell.m in Sources */,
				7749E1A11F7DE23B00E93881 /* ButtonInfoModel.m in Sources */,
				7793D2A22A2371D600971D97 /* BRQuickPrescribePatientModel.m in Sources */,
				7757A8291FE50FDE00B065A5 /* MessageTransmitViewController.m in Sources */,
				0A9926111FCD6800003B537E /* BRPresAddPatientView.m in Sources */,
				A6FC85301FF4EB15005B6CA8 /* ChooseAgentmodel.m in Sources */,
				A6F85C60212EBCEE00780DA6 /* CompletedCell.m in Sources */,
				77CB751722D5DD4800262B0C /* BrokerOrderListCell.m in Sources */,
				0A9CED2D1F7CE6C40072CF4A /* BRPrescriptionDrugCell.m in Sources */,
				775656A51F628BF800F779C2 /* SessionListCell.m in Sources */,
				A69F62D91F7CF5F9004B7AE8 /* UserCenterHeaderCell.m in Sources */,
				770605851FDBBD510091FA60 /* SessionXiaoRanViewController.m in Sources */,
				A649544E1F8B791B00F16C42 /* AboutViewController.m in Sources */,
				0FB534AE20F49B2B00E084B7 /* PhotoPresViewController.m in Sources */,
				77273AEE2CF70E1C0018A9CD /* EnvironmentConfig.m in Sources */,
				A661A80420EE1D9C009CFD40 /* ZFPlayerController.m in Sources */,
				0A5F4D541FBC486800491FE7 /* BRPrescriptionPatientView.m in Sources */,
				7742ECB21F4BD7ED00A9B110 /* AppDelegate.m in Sources */,
				7735EF471F62C00C003CE02D /* InviteDoctorViewController.m in Sources */,
				A6274F251FEE405D000BE82A /* CompleteModel.m in Sources */,
				A661A7F420EE1D9C009CFD40 /* ZFNetworkSpeedMonitor.m in Sources */,
				7755B71C20A5A49F00162617 /* MyPurseViewController.m in Sources */,
				A661A80020EE1D9C009CFD40 /* UIViewController+ZFPlayerRotation.m in Sources */,
				77FCA5D31F60EC5400369CB9 /* PopListCell.m in Sources */,
				77CB751B22D5E7D500262B0C /* BrokerInfoModel.m in Sources */,
				A64703C2204E32CB00F33B3D /* CertificationFailViewController.m in Sources */,
				A60FFDEF1FC2CD360097C11D /* InputDataViewController.m in Sources */,
				0F57A579204D27FA002914A9 /* BRPrescriptionToolBar.m in Sources */,
				A60629BD1FFCDDA9004BA00A /* MedicinePCell.m in Sources */,
				7745181A1FDE5C6800DAB506 /* BRXiaoRanChatInputMoreContainerView.m in Sources */,
				777742311FD56A710060BEF2 /* PatientsListCell.m in Sources */,
				0A71DB151F948F1800BF831F /* BRPresInputView.m in Sources */,
				A69A30181FFBA7FA007E9413 /* AllocationViewController.m in Sources */,
				A661A7F320EE1D9C009CFD40 /* ZFLoadingView.m in Sources */,
				A61F8E811FC6BB8600AB928E /* CameraManager.m in Sources */,
				77A546241FFE52A80086EFD8 /* BRNameTextField.m in Sources */,
				A661A7F820EE1D9C009CFD40 /* ZFSliderView.m in Sources */,
				778E0B6E279BD9BE00F9BA6B /* WechatWithdrawBottomView.m in Sources */,
				77C1EA19212C036100256EA8 /* DrugStoreViewController.m in Sources */,
				7734EF0E2CDC95110037B345 /* QuickPrescribeSessionViewController.m in Sources */,
				77B481A21F66849E00466F3B /* BRTextMessageCellLayout.m in Sources */,
				7781E2901F4D19CE00DDD047 /* Config.m in Sources */,
				A62459811F8F64A300C88DD4 /* MyDoctorViewController.m in Sources */,
				77B481A81F668B5D00466F3B /* BRSystemMessageCellLayout.m in Sources */,
				A60B54551F95B07400123620 /* ResultSummaryCell.m in Sources */,
				77BDD75E20AC506600762078 /* BillDetailsInviteCell.m in Sources */,
				7718689820AD326500B62997 /* BRBillDetailsAccountOfMoneyCell.m in Sources */,
				777B879A2CFC15B900004875 /* CustomBoilyWayViewController.m in Sources */,
				775E751220A999D500EAA5DB /* MyPurseHeaderView.m in Sources */,
				777727D2276DA66000DA7FD4 /* EbookListViewController.m in Sources */,
				77BDAF4A26CE990F000470BE /* JTAreaInfoModel.m in Sources */,
				A68CE8B61FE275F8008E5F05 /* ArchitectureViewController.m in Sources */,
				A64703D5204E8E0A00F33B3D /* InvitaUnregisteredCell.m in Sources */,
				A63F1FFE1FF5ED6B004A0DFA /* DownloadCell.m in Sources */,
				A60DECF21FE3718700FE2C42 /* ArchitectureTabCell.m in Sources */,
				A64954421F8B75E600F16C42 /* SecuritySettingViewController.m in Sources */,
				A660C9041FCBF25800D36C7C /* CompleteTotalOrGapCell.m in Sources */,
				77BC46B82000DD280066CE29 /* BRVisitsHospitalAddress.m in Sources */,
				0A9CED1A1F7B3A380072CF4A /* BRPrescriptionTitleView.m in Sources */,
				77B481741F6638FF00466F3B /* IMChatCollectionViewLayout.m in Sources */,
				77D14A8D22D6EA150013CC7D /* BrokerOrderSearchViewController.m in Sources */,
				77C7D24E1F5815C300C6F31A /* UserCenterFooterView.m in Sources */,
				A64761641F94B91800C84511 /* PatientNumberCell.m in Sources */,
				0A89E71F1FF0A24100834D48 /* FactoryInfoViewController.m in Sources */,
				0A097E431FCFDF4F00A1BFEF /* BRPatientModel.m in Sources */,
				77B481991F667A3700466F3B /* BRMessage.m in Sources */,
				0A3124592004974700D56D36 /* BRAPNsModule.m in Sources */,
				0A9CED201F7CD28C0072CF4A /* BRPrescriptionDisplayView.m in Sources */,
				77B3A5532D2447C000097E8A /* TCPMappingParser.m in Sources */,
				A661A80920EE1D9C009CFD40 /* ZFReachabilityManager.m in Sources */,
				A661A7F020EE1D9C009CFD40 /* UIImageView+ZFCache.m in Sources */,
				0A9CED2A1F7CE5EE0072CF4A /* BRMedicineModel.m in Sources */,
				77EF50602DA2294000F085B1 /* AuthCheckHelper.m in Sources */,
				A64703C5204E33AA00F33B3D /* CertificationNoPatientViewController.m in Sources */,
				772A7D061FA1903C001AE0BB /* BRAudioMessageCell.m in Sources */,
				77BEE6691F556EC900597489 /* BRError.m in Sources */,
				0FB534B520F4B14900E084B7 /* PhotoPresModel.m in Sources */,
				A63695991FE7A79D002DFB0A /* SearchCommonViewController.m in Sources */,
				7736B9441FE100F700C55A50 /* QuestionAddOrEditListCell.m in Sources */,
				7775A1DA1FFF4C77009D2131 /* BRDebugInfoViewController.m in Sources */,
				7724EE6A1FBE8DE100309E92 /* UINavigationBar+Addition.m in Sources */,
				7735EF441F62BFEF003CE02D /* InvitePatientViewController.m in Sources */,
				A64C6CC21FC0476800138EB1 /* PersonalDataListCell.m in Sources */,
				0A69E5BC1FD940BE003342A6 /* BRFactoryModel.m in Sources */,
				77351B1720037DF500E266F7 /* DataInitLoadingViewController.m in Sources */,
				7736B94D1FE144D700C55A50 /* FrequentlyQuestionContentAddViewController.m in Sources */,
				A64703B9204E302800F33B3D /* InvitaUnregisteredViewController.m in Sources */,
				A60629C31FFD050D004BA00A /* SearchArchitectureViewController.m in Sources */,
				77ABE80E28B0B5EE00176DC3 /* QuickReplyTypeInfoModel.m in Sources */,
				773D91502CF5DA6500547A91 /* OCRResponse.m in Sources */,
				A64703BC204E310A00F33B3D /* RegisterUnauthorizedViewController.m in Sources */,
				A61C69531FCE975600E7AB32 /* PharmacopeiaModel.m in Sources */,
				0AA894DE1FDEABAF0034C3A2 /* BRPresReplaceDrugCell.m in Sources */,
				A64703BF204E323C00F33B3D /* CertificationProcessViewController.m in Sources */,
				771911AB1FE21A6000070347 /* BRBaseTextLinePositionModifier.m in Sources */,
				77CA9C3C1F5CFEBF00B6CDE5 /* IMSession.m in Sources */,
				777E588220526B1C000336AC /* ClassicPrescriptionViewController.m in Sources */,
				A6D9E7D61FFE4BF600694EC4 /* UITabBar+bagde.m in Sources */,
				A6106C1E1FF34CB7006E5D32 /* SchedulingModel.m in Sources */,
				77BEE6651F55690A00597489 /* BRLoginModel.m in Sources */,
				77B6849B1FFFAC4900A1FFF8 /* BRSessionStartPatientMessageCellLayout.m in Sources */,
				A61C69471FCE59C500E7AB32 /* ServiceSettingViewController.m in Sources */,
				0A71DB1E1F959E6300BF831F /* BRSubMedicineModel.m in Sources */,
				77B3A5502D24479A00097E8A /* TCMMatchResult.m in Sources */,
				A6FC85271FF4915D005B6CA8 /* TaskAllocationModel.m in Sources */,
				A60B54521F95AB5500123620 /* ResultSummaryViewController.m in Sources */,
				7742ED031F4C272200A9B110 /* PatientViewController.m in Sources */,
				A635E7FB1FBD2C810085E1D0 /* FindPasswordViewController.m in Sources */,
				7712BE2C1FD927B10064B0F6 /* BRSupplementQuestionMessageCell.m in Sources */,
				A67511DD1FD6CA4E00B6DDD3 /* CertificationSuccessViewController.m in Sources */,
				A60DECF51FE3DB7E00FE2C42 /* CommonlyUsedPartyModel.m in Sources */,
				7757A8261FE4C11F00B065A5 /* SystemDateCell.m in Sources */,
				7729019C1F5D4F4E008C51F6 /* IMSessionManager.m in Sources */,
				A60629BA1FFCDD16004BA00A /* MedicineParticularsViewController.m in Sources */,
				7735EF321F62A713003CE02D /* ChatViewController.m in Sources */,
				A60DECEF1FE3684100FE2C42 /* ArchitectureCell.m in Sources */,
				774162DA279EE2E50028FC2C /* BRWechatBindResultModel.m in Sources */,
				A620D4EE20EF20370022A052 /* PhotoPrescViewController.m in Sources */,
				779642831FC40B9A00AA0976 /* BRSelectPatientView.m in Sources */,
				A6C74F29212D495F00BC2AD7 /* OrderRecordView.m in Sources */,
				A64703D8204E92F500F33B3D /* RegisterUnauthorizedCell.m in Sources */,
				7761534D1FCD30D9006A2FAC /* PatientDocumentModel.m in Sources */,
				778E0B68279BD0BD00F9BA6B /* WechatWithdrawChooseCell.m in Sources */,
				775243831FC978EE00EA2ED3 /* BRDHBaseModel.m in Sources */,
				770CBCE51FDFD02400C27AB3 /* QuestionTypeCell.m in Sources */,
				774C2DD91FEE2E03004CD7E2 /* PatientInfoModel.m in Sources */,
				77B4819C1F6682C800466F3B /* BRBaseMessageCellLayout.m in Sources */,
				7706FF371F6A1C2C00EBB35C /* BRChatInputMoreContainerView.m in Sources */,
				0A9202091F8F6DC10053B7D2 /* BRUnderlineRedTextField.m in Sources */,
				772B59BB1FE7DA84009EA260 /* BRRevokeMessageCell.m in Sources */,
				7712BE321FD927ED0064B0F6 /* BRSupplementQuestionMessageCellLayout.m in Sources */,
				776191641F5A491100A19B77 /* UIButton+Badge.m in Sources */,
				A661A7F220EE1D9C009CFD40 /* ZFLandScapeControlView.m in Sources */,
				7793D29F2A236B9400971D97 /* BRTakerInfoModel.m in Sources */,
				A62459781F8F5C6100C88DD4 /* ToPushViewController.m in Sources */,
				A61C69411FCE56C100E7AB32 /* CommonlyUsedPartyViewController.m in Sources */,
				77F7A14F1F5E51AB00EA5FC1 /* AboutFooter.m in Sources */,
				A661A7FD20EE1D9C009CFD40 /* ZFIJKPlayerManager.m in Sources */,
				0A69E5BF1FD946E7003342A6 /* BRSubFactoryModel.m in Sources */,
				771D93AD205667E600A59DD2 /* ClassicPrescriptionCell.m in Sources */,
				A66EAD351F90979E0065ADC3 /* MyDoctorHeaderCell.m in Sources */,
				A660C9071FCBFE3C00D36C7C /* CompleteDetailCell.m in Sources */,
				0AF203231F8B699700432DD6 /* BRPrescriptionDescView.m in Sources */,
				7726F6031F958FE000D2E07B /* BRImageMessageCell.m in Sources */,
				7770FD601F53FEA100651814 /* IMMessage.m in Sources */,
				77F4E7711F6BBC9D00897C4A /* IMChatInputMoreView.m in Sources */,
				77264DF7212D5444002EE6C9 /* DrugStoreListViewController.m in Sources */,
				A64F46F6204F806C004C5FAB /* DoctorStateModel.m in Sources */,
				776365FA1F4FC18A00AF92A1 /* UITextView+Placeholder.m in Sources */,
				7735EF351F62A720003CE02D /* SessionViewController.m in Sources */,
				77A7DF9C1FA0A10F0012C18C /* AACRecord.m in Sources */,
				77B481B41F668E6300466F3B /* BRDateMessageCellLayout.m in Sources */,
				0AF2032F1F8CD48C00432DD6 /* BRPresOfflineView.m in Sources */,
				0AA894DA1FDEA2B60034C3A2 /* BRPresReplaceDrugView.m in Sources */,
				77494F2A28A71AEE0000132A /* BRQuickPrescribeViewController.m in Sources */,
				7733342F1FA2FE6F00F996A0 /* BRPlayer.m in Sources */,
				7742ECEF1F4C22EC00A9B110 /* Utils.m in Sources */,
				77264DF4212D3BFC002EE6C9 /* DrugStoreListCell.m in Sources */,
				77B481A51F6689E700466F3B /* BRTextMessageCell.m in Sources */,
				0A9CED261F7CE36F0072CF4A /* BRPrescriptionModel.m in Sources */,
				7770FD691F54089A00651814 /* IMContact.m in Sources */,
				7758EFCD1FE7CE5C0084D11E /* BRUpDateView.m in Sources */,
				7712BE271FD920720064B0F6 /* BRFzdAnswerMessageCellLayout.m in Sources */,
				776633991F5E981B00CF7029 /* IMTableContact.mm in Sources */,
				773D914D2CF5DA3300547A91 /* OCRTextDetection.m in Sources */,
				A64703CB204E357F00F33B3D /* PrescriptionNoPayViewController.m in Sources */,
				77EF33DC1FD8D84B0008B712 /* BRGuiZeViewController.m in Sources */,
				0A9A1BFC1FE13CB600F5DA0E /* BRPresInfoModel.m in Sources */,
				A68CE8BA1FE277A8008E5F05 /* SchedulingCell.m in Sources */,
				771138DB1FC7B4920084D9CB /* BRAlertView.m in Sources */,
				A62587402004CB3A00259622 /* RegisterTF.m in Sources */,
				0A9CED231F7CE1F20072CF4A /* BRPrescriptionDrugListView.m in Sources */,
				771736C91FA968A200ADE932 /* BRMessageContentModel.m in Sources */,
				77327D791FC2757C0023939F /* BRSessionEndMessageCell.m in Sources */,
				771F66F01FC8090F002AAA5C /* BRPatientDocumentTableViewCell.m in Sources */,
				A661A7FE20EE1D9C009CFD40 /* KSMediaPlayerManager.m in Sources */,
				77CB751322D5CAF000262B0C /* BrokerOrderListViewController.m in Sources */,
				A64C6CBF1FC0464500138EB1 /* PersonalDataHeaderCell.m in Sources */,
				7762B04320CE1A8700273CD5 /* BRMedicationWarningAlertView.m in Sources */,
				A68CE8AD1FE2684A008E5F05 /* DownloadViewController.m in Sources */,
				7720F1961FE8F626009DC35C /* BRSessionListModel.m in Sources */,
				A6106C221FF39543006E5D32 /* MyDoctorModel.m in Sources */,
				77ABE81128B0E3C900176DC3 /* QuickReplyTopView.m in Sources */,
				7770FD591F53FCA000651814 /* IMClient.m in Sources */,
				7719417E1F7B402D00E11088 /* UIFont+Util.m in Sources */,
				0A71DB111F94668C00BF831F /* AddDrugViewController.m in Sources */,
				A661A7F920EE1D9C009CFD40 /* ZFSmallFloatControlView.m in Sources */,
				770605881FDBEAC60091FA60 /* FileManager.m in Sources */,
				7757A8231FE4C04400B065A5 /* SystemMessageCell.m in Sources */,
				A62459751F8F5AE900C88DD4 /* AboutBRZYViewController.m in Sources */,
				0AA18C831FF33A9D00CC3C61 /* ZYActivitiesViewController.m in Sources */,
				773D91472CF5D99200547A91 /* OCRPoint.m in Sources */,
				7712BE1B1FD91FEA0064B0F6 /* BRWzdQuestionMessageCellLayout.m in Sources */,
				A6C74F2C212D5C2600BC2AD7 /* OrderRecordCell.m in Sources */,
				77EA2618295DA047000D1359 /* BRMessageTitleView.m in Sources */,
				777418982CEF208B00392B18 /* BRIntelligentEntryViewController.m in Sources */,
				0F9F54A320941EA600F2741C /* BRTemporaryPrescription.mm in Sources */,
				771C15CC1F4D2B4100099626 /* UserManager.m in Sources */,
				A6FC852D1FF4E416005B6CA8 /* ChooseAgentCell.m in Sources */,
				77286C852057755E00117038 /* ClassicPrescriptionSearchViewController.m in Sources */,
				7736B94A1FE1260700C55A50 /* QuestionInfoModel.m in Sources */,
				77BDD75B20AC2F3A00762078 /* BillDetailsWithdrawFailedServiceChargeCell.m in Sources */,
				0AF203291F8CBB0300432DD6 /* BRUnderlineTextField.m in Sources */,
				7719B97320206233005504C6 /* UIImage+BR.m in Sources */,
				77F7A14C1F5E3FCD00EA5FC1 /* AboutHeader.m in Sources */,
				7796429F1FC4214500AA0976 /* BRVisitsArrangementViewController.m in Sources */,
				77327D731FC269B90023939F /* BRSessionStartMessageCellLayout.m in Sources */,
				0AADC48D1FFF9A96008F29D6 /* BRContraindicationModel.m in Sources */,
				A661A80520EE1D9C009CFD40 /* ZFPlayerGestureControl.m in Sources */,
				0ABEE6521FC67073006FE61B /* BRPresDrugCell.m in Sources */,
				776633931F5E97F500CF7029 /* IMTableMessage.mm in Sources */,
				A6106C251FF3AAC2006E5D32 /* QueryModel.m in Sources */,
				7736B9471FE1257C00C55A50 /* QuestionItemModel.m in Sources */,
				778E0B71279BE0D900F9BA6B /* WechatWithdrawInputNumCell.m in Sources */,
				7781E2931F4D1AB900DDD047 /* SocketManager.m in Sources */,
				7744A61E1FEA059100AE7ABD /* PatientInfoViewController.m in Sources */,
				0FB534C120F5A2FD00E084B7 /* BRSegmentView.m in Sources */,
				7795B578213E214E006A2A9E /* BillDetailsDrugStoreRewardCell.m in Sources */,
				7761772C209958F400C35E77 /* ChatTitleView.m in Sources */,
				A661A80820EE1D9C009CFD40 /* ZFPlayerView.m in Sources */,
				771E520E2134ED30006B6ACB /* DrugStoreListModel.m in Sources */,
				A661A7FB20EE1D9C009CFD40 /* ZFUtilities.m in Sources */,
				7777423A1FD587150060BEF2 /* FrequentlyQuestionAddViewController.m in Sources */,
				77286C8B2057D77B00117038 /* ClassicPrescriptionListViewController.m in Sources */,
				7721B3ED1FC42310000A6DFA /* BRWzdBaseMessageCellLayout.m in Sources */,
				A691B0771FD8E29300D29516 /* IsReviewingViewController.m in Sources */,
				0A5F4D571FBEF18600491FE7 /* BRPresUsageView.m in Sources */,
				77131E9E1F4D7503006C9F79 /* NSDateFormatter+Singleton.m in Sources */,
				7712BE2F1FD927CD0064B0F6 /* BRSupplementAnswerMessageCellLayout.m in Sources */,
				7712BE151FD91A2A0064B0F6 /* BRWzdAnswerMessageCellLayout.m in Sources */,
				A661A80120EE1D9C009CFD40 /* ZFFloatView.m in Sources */,
				771736CC1FA96A7E00ADE932 /* BRMessagesModel.m in Sources */,
				77723E071FD635BE00E1BD5A /* AddCommonlyPrescriptionViewController.m in Sources */,
				A6504DEB20ADA4D4001C00F2 /* SearchSummaryViewController.m in Sources */,
				A6F85C5D212EB6FA00780DA6 /* CompletedView.m in Sources */,
				771D93AA2056292500A59DD2 /* PrescriptionModel.m in Sources */,
				773D914A2CF5DA1400547A91 /* OCRWordPolygon.m in Sources */,
				777727DB276DDF7200DA7FD4 /* EbookListItemCell.m in Sources */,
				A661A80220EE1D9C009CFD40 /* ZFKVOController.m in Sources */,
				77CFB9492AD5031900A1EA90 /* InviteScanPrescribeViewController.m in Sources */,
				77DB713C1F56E07F00D07919 /* UserCenterPanelCollectionViewCell.m in Sources */,
				775643142DF7381900392EFE /* BRAuthConfig.m in Sources */,
				77110C8E2E069F8000F002FC /* BRWithdrawPasswordView.m in Sources */,
				77B4818D1F66740A00466F3B /* HPGrowingTextView.m in Sources */,
				0AA18C851FF33A9D00CC3C61 /* ZYActivityModel.m in Sources */,
				0ABEE6551FC672A7006FE61B /* BRZYTextField.m in Sources */,
				77BDD75220AC113F00762078 /* BillDetailsPatientRewardCell.m in Sources */,
				7700F3511FCBE4920059BA14 /* MedicatedInfoViewController.m in Sources */,
				7770FD701F540D7D00651814 /* IMSDKHelper.m in Sources */,
				7785B2912E224E9F006F68FC /* BRPackageSpecModel.m in Sources */,
				A62ABA8F1FD12F8D00668558 /* ReplaceTelViewController.m in Sources */,
				A6A1CD531FEA073D00044E3C /* CommonProblemsModel.m in Sources */,
				774162DD279EE7300028FC2C /* BRWithdrawInfoModel.m in Sources */,
				77A546211FFE46020086EFD8 /* InterrogationAndVisitViewController.m in Sources */,
				77B481AE1F668CC300466F3B /* NSAttributedString+BR.m in Sources */,
				7742ECAF1F4BD7ED00A9B110 /* main.m in Sources */,
				779642861FC40B9A00AA0976 /* BRDHBaseViewController.m in Sources */,
				0AA18C861FF33A9D00CC3C61 /* ZYPageControl.m in Sources */,
				77B481801F665EDA00466F3B /* IMChatCollectionView.m in Sources */,
				A661A7FF20EE1D9C009CFD40 /* UIScrollView+ZFPlayer.m in Sources */,
				7761772F20995B2300C35E77 /* AgeGenderButton.m in Sources */,
				A61BCE3D1FF24E84006C2EF1 /* ArchitectureModel.m in Sources */,
				A64703CE204E35E600F33B3D /* CertificationViewController.m in Sources */,
				774494451FE0C2A900522A88 /* BRNoDataView.m in Sources */,
				7756569C1F6282EC00F779C2 /* SessionSearchViewController.m in Sources */,
				77C29327293B0995001A5B45 /* DrugDetailListViewController.m in Sources */,
				A6A4CF931FB942D300B545F6 /* BrzyAgreementViewController.m in Sources */,
				0A69BE1B1FD29E48009F8630 /* BRPresSelectTypeView.m in Sources */,
				772B59BE1FE7DACC009EA260 /* BRRevokeMessageCellLayout.m in Sources */,
				774DC664212EB17F008F4931 /* ShareContentView.m in Sources */,
				77ABE80B28B0B57200176DC3 /* QuickReplyContentModel.m in Sources */,
				779642A01FC4214500AA0976 /* BRCalendarCollectionViewCell.m in Sources */,
				A624598D1F8F71CE00C88DD4 /* WaitAcceptDoctorCell.m in Sources */,
				A6E4212920AE7AB700321AB4 /* SummaryMonthDetailViewController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		7764AB101F60081800B26332 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				7764AB0F1F60081800B26332 /* zh-Hans */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		7742ECBF1F4BD7ED00A9B110 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				ENABLE_TESTABILITY = YES;
				EXCLUDED_ARCHS = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.3;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7742ECC01F4BD7ED00A9B110 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				EXCLUDED_ARCHS = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.3;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7742ECC21F4BD7ED00A9B110 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BCF38E31BD45249EAE704F9D /* Pods-BRZY.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_MODULE_DEBUGGING = YES;
				CLANG_WARN_CXX0X_EXTENSIONS = NO;
				CODE_SIGN_ENTITLEMENTS = BRZY/BRZY.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = *******;
				DEVELOPMENT_TEAM = NTQ8FSBK83;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/BRZY/Classes/Vendors/TencentORC",
					"$(PROJECT_DIR)/BRZY/Classes/Vendors",
				);
				GCC_PREFIX_HEADER = "$(SRCROOT)/BRZY/PrefixHeader.pch";
				INFOPLIST_FILE = BRZY/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/BRZY/Classes/Vendors/AMR/lib",
					"$(inherited)",
				);
				MARKETING_VERSION = 4.6.4;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.yykkj.biranzhongyi;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		7742ECC31F4BD7ED00A9B110 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F3DFD5232C69E696D14262AA /* Pods-BRZY.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_MODULE_DEBUGGING = YES;
				CLANG_WARN_CXX0X_EXTENSIONS = NO;
				CODE_SIGN_ENTITLEMENTS = BRZY/BRZYRelease.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = *******;
				DEVELOPMENT_TEAM = NTQ8FSBK83;
				EXCLUDED_ARCHS = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/BRZY/Classes/Vendors/TencentORC",
					"$(PROJECT_DIR)/BRZY/Classes/Vendors",
				);
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREFIX_HEADER = "$(SRCROOT)/BRZY/PrefixHeader.pch";
				INFOPLIST_FILE = BRZY/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/BRZY/Classes/Vendors/AMR/lib",
					"$(inherited)",
				);
				MARKETING_VERSION = 4.6.4;
				PRODUCT_BUNDLE_IDENTIFIER = com.yykkj.biranzhongyi;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7742ECA51F4BD7ED00A9B110 /* Build configuration list for PBXProject "BRZY" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7742ECBF1F4BD7ED00A9B110 /* Debug */,
				7742ECC01F4BD7ED00A9B110 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7742ECC11F4BD7ED00A9B110 /* Build configuration list for PBXNativeTarget "BRZY" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7742ECC21F4BD7ED00A9B110 /* Debug */,
				7742ECC31F4BD7ED00A9B110 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7742ECA21F4BD7ED00A9B110 /* Project object */;
}
