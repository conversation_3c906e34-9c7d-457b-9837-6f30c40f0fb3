//
//  BRAppStartModule.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/30.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRAppStartModule.h"
#import "IMClient.h"
#import "BRAlertView.h"
#import "BRAnnouncementView.h"
#import "IMClient.h"
#import "BRUpDateView.h"
#import "BRGuideView.h"

#import "FirstOpenViewController.h"

@interface BRAppStartModule () <SocketManagerDelegate,IMSessionManagerDelegate,ConfigDelegate>

@end

@implementation BRAppStartModule

#pragma mark - AppDelegate 

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions{
    
    [[Config shareInstance] addDelegate:self];
    [[SocketManager shareInstance] addDelegate:self];

//    if ([Config getisNeedAgreePrivacy]) {
//
//    }
//    else {
        //是否首次登录
        if ([Config getisFirstOpenAppByVersion]) {
            if ([[Config shareInstance] showDialogStartWithType:kDialogFuncIntroduce]) {
                [self showGuideView];
            }
            [Config changeFirstOpenAppToNo];
        }
        
        if ([[Config shareInstance] showDialogStartWithType:kDialogAutoUpdate]) {
            [self upDateCheck];
        }
//    }
//    if ([Config getisNeedAgreePrivacy]) {
//    [[Utils sharedInstance] showPrivacyPopViewTapPrivacy:^{
//        NSLog(@"隐私政策====");
//    } tapUserAgreement:^{
//        NSLog(@"用户协议====");
//    }];
//    }
    
    return YES;
}

- (void)applicationWillResignActive:(UIApplication *)application{
    
}

- (void)applicationDidEnterBackground:(UIApplication *)application{
    
}

- (void)applicationWillEnterForeground:(UIApplication *)application{
    
}

- (void)applicationDidBecomeActive:(UIApplication *)application{
    
}

- (void)applicationWillTerminate:(UIApplication *)application{
    
}

#pragma mark - confiDelgate
- (void)configDelegateDidshowDialogWithType:(NSString *)dialog {
    //自动更新
    if ([dialog isEqualToString:kDialogAutoUpdate]) {
        [self upDateCheck];
    }
    //功能引导
    else if ([dialog isEqualToString:kDialogFuncIntroduce]) {
        [self showGuideView];
    }
}
#pragma mark - 登录冲突
- (void)socketManagerDidSDKLoginConflict {
    
    [[UserManager shareInstance] logout];
    
    BRAlertView *alertView = [[BRAlertView alloc] init];
    alertView.isHideWhenTapBackground = YES;
    [Utils startLogin];
    
    [alertView showAlertView:@"帐号已在另一台设备上登录，您已被迫下线。" completion:^{
        [alertView close];
        //
    }];
}
#pragma mark - socket 登录回调
- (void)socketManagerDidLoginSuccess:(BRLoginModel *)model {
    //自动更新
    if ([Config getisAutoLogin]) {
        if ([[Config shareInstance] showDialogStartWithType:kDialogAutoUpdate]) {
            [self upDateCheck];
        }
    }
}
#pragma mark - 功能引导
- (void)showGuideView {
    BRGuideView *guideView = [[BRGuideView alloc] initWithBackNum:^(NSUInteger imageNum) {
        
    } removeUI:^{
        [[Config shareInstance] showDialogFinishWithType:kDialogFuncIntroduce];
    }];
}
#pragma mark - 检查版本更新
- (void)upDateCheck {
    
    __weak typeof(self)mySelf = self;
    NSDictionary *dic = @{@"method_code":@"000050",@"systemType":@"2"};
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;
            
            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                
                NSString *contentStr = @"";
                if (![[dataDic objectForKey:@"updateContent"] isKindOfClass:[NSNull class]]) {
                    contentStr = [dataDic objectForKey:@"updateContent"];
                }
                BOOL isFUD = NO;
                if (![[dataDic objectForKey:@"isForce"] isKindOfClass:[NSNull class]]) {
                    NSString *str = [dataDic objectForKey:@"isForce"];
                    if ([str isEqualToString:@"1"]) {
                        isFUD = YES;
                    }
                }
                NSString *versionStr = @"";
                if (![[dataDic objectForKey:@"versionNo"] isKindOfClass:[NSNull class]]) {
                    versionStr = [dataDic objectForKey:@"versionNo"];
                }
                
                NSString *appStorePath = @"";
                if (![[dataDic objectForKey:@"url"] isKindOfClass:[NSNull class]]) {
                    appStorePath = [dataDic objectForKey:@"url"];
                }
                NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
                NSString *appCurVersion = [infoDictionary objectForKey:@"CFBundleShortVersionString"];
                
                NSArray *curVersionArr = [appCurVersion componentsSeparatedByString:@"."];
                NSArray *newVersionArr = [versionStr componentsSeparatedByString:@"."];
                

                NSInteger curVerson = [[curVersionArr componentsJoinedByString:@""] integerValue];
                NSInteger newVerson = [[newVersionArr componentsJoinedByString:@""] integerValue];
                
                //未更新
                if (curVerson >= newVerson) {
                    [[Config shareInstance] showDialogFinishWithType:kDialogAutoUpdate];
                }
                //更新
                else {
//                    BRUpDateView *upDate = [[BRUpDateView alloc] initWithContent:contentStr isForcedUpDate:isFUD withClick:^{
//                        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:appStorePath]];
//                    }];
                    
                    BRUpDateView *upDate = [[BRUpDateView alloc] initWithContent:contentStr isForcedUpDate:isFUD withClick:^{
                        NSURL *url = [NSURL URLWithString:appStorePath];
                        
                        if ([[UIApplication sharedApplication] canOpenURL:url]) {
                            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {
                                if (success) {
                                    NSLog(@"成功打开 App Store 链接");
                                } else {
                                    NSLog(@"无法打开 App Store 链接");
                                }
                            }];
                        } else {
                            NSLog(@"无法处理此 URL");
                        }
                    }];
                    
                    upDate.removeUpDateViewBlock = ^{
                        [[Config shareInstance] showDialogFinishWithType:kDialogAutoUpdate];
                    };
                }
                
            }
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"-------版本更新检查失败---------");
        [[Config shareInstance] showDialogFinishWithType:kDialogAutoUpdate];
    }];
}

@end
