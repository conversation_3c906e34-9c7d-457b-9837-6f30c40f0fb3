//
//  BRThirdPartModule.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/30.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRThirdPartModule.h"
#import "Reachability.h"
//#import <OcrSDKKit/OcrSDKKit.h>
//#import <OcrSDKKit/OcrSDKConfig.h>

//#import <ShareSDK/ShareSDK.h>
//#import <ShareSDKConnector/ShareSDKConnector.h>
//#import "WXApi.h"
//#import <TencentOpenAPI/TencentOAuth.h>
//#import <TencentOpenAPI/QQApiInterface.h>


#ifndef __OPTIMIZE__
#import "RRFPSBar.h"
#endif

@interface BRThirdPartModule ()

@property (strong, nonatomic) Reachability *hostReachability;
@property (strong, nonatomic) Reachability *internetReachability;

@end

@implementation BRThirdPartModule

#pragma mark - Application 
- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions{
    

    
//    [ShareSDK registPlatforms:^(SSDKRegister *platformsRegister) {
//        //QQ
//        [platformsRegister setupQQWithAppId:@"1106552871" appkey:@"TIeSnfNIbOgg1oD0" enableUniversalLink:NO universalLink:nil];
//        //微信
//        [platformsRegister setupWeChatWithAppId:@"wx266e4342efb1a84b" appSecret:nil universalLink:kUniversialLink];
//    }];
    
//    [ShareSDK registerActivePlatforms:@[@(SSDKPlatformTypeWechat),@(SSDKPlatformTypeQQ)] onImport:^(SSDKPlatformType platformType) {
//        switch (platformType)
//        {
//            case SSDKPlatformTypeWechat:
//                [ShareSDKConnector connectWeChat:[WXApi class]];
//                break;
//            case SSDKPlatformTypeQQ:
//                [ShareSDKConnector connectQQ:[QQApiInterface class] tencentOAuthClass:[TencentOAuth class]];
//                break;
//
//            default:
//                break;
//        }
//    } onConfiguration:^(SSDKPlatformType platformType, NSMutableDictionary *appInfo) {
//        switch (platformType) {
//            case SSDKPlatformTypeWechat:
//            {
////                [appInfo SSDKSetupWeChatByAppId:@"wxf01a0ebc7a49f3f2"
////                                      appSecret:@"c594fb5e13ef690c1b7aea758c7dc6b8"];
//                [appInfo SSDKSetupWeChatByAppId:@"wx266e4342efb1a84b" appSecret:nil];
//            }
//                break;
//
//            case SSDKPlatformTypeQQ:
//                [appInfo SSDKSetupQQByAppId:@"1106552871"
//                                     appKey:@"TIeSnfNIbOgg1oD0"
//                                   authType:SSDKAuthTypeBoth];
//                break;
//
//            default:
//                break;
//        }
//    }];
    
#ifndef __OPTIMIZE__
    [[RRFPSBar sharedInstance] setHidden:YES];
#endif
    [self configSetKeyboard];
    [self networkTestReachableStatus];
    
//    [self configOCRRecognition];
 
    return YES;
}

- (void)applicationWillResignActive:(UIApplication *)application{
    
}

- (void)applicationDidEnterBackground:(UIApplication *)application{
    
}

- (void)applicationWillEnterForeground:(UIApplication *)application{
    
}

- (void)applicationDidBecomeActive:(UIApplication *)application{
    
}

- (void)applicationWillTerminate:(UIApplication *)application{
    
}

#pragma mark - OCR识别
- (void)configOCRRecognition {
    
//    OcrSDKConfig *ocrSDKConfig = [[OcrSDKConfig alloc] init];
//    ocrSDKConfig.ocrModeType = OCR_DETECT_AUTO_MANUAL;
//    [[OcrSDKKit sharedInstance] loadSDKConfigWithSecretId:SECRET_ID withSecretKey:SECRET_KEY withConfig:ocrSDKConfig];
}

#pragma mark- 键盘回收相关方法
- (void)configSetKeyboard {
    IQKeyboardManager *manager = [IQKeyboardManager sharedManager];
    [[manager disabledDistanceHandlingClasses]addObject:NSClassFromString(@"AddDrugViewController")];
    [[manager disabledDistanceHandlingClasses]addObject:NSClassFromString(@"BRPresHaveNoticeCell")];
    [[manager disabledDistanceHandlingClasses]addObject:NSClassFromString(@"AddCommonlyPrescriptionViewController")];
    manager.enable = YES;
    manager.shouldResignOnTouchOutside = YES;
    manager.shouldToolbarUsesTextFieldTintColor = YES;
    manager.enableAutoToolbar = NO;
}

#pragma mark- 网络监测方法
- (void)networkTestReachableStatus {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(reachabilityChange:) name:kReachabilityChangedNotification object:nil];
    
    NSString *remoteHostName = @"www.baidu.com";
    
    self.hostReachability = [Reachability reachabilityWithHostName:remoteHostName];
    [self.hostReachability startNotifier];
    [self updateInterfaceWithReachability:self.hostReachability];
    
    self.internetReachability = [Reachability reachabilityForInternetConnection];
    [self.internetReachability startNotifier];
    [self updateInterfaceWithReachability:self.internetReachability];
}

- (void)reachabilityChange:(NSNotification *)note{
    Reachability *curReach = [note object];
//    NSParameterAssert([curReach isKindOfClass:[Reachability class]]);
//    [self updateInterfaceWithReachability:curReach];
}

- (void)updateInterfaceWithReachability:(Reachability *)reachability {
    if (reachability == self.hostReachability) {
//        NetworkStatus netStatus = [reachability currentReachabilityStatus];
        BOOL connectionRequired = [reachability connectionRequired];
        
        if (connectionRequired) {
            
        } else {
            
        }
        
        if (reachability == self.internetReachability) {
            
        }
    }
}



@end
