//
//  BRModuleManager.m
//  BRZY
//
//  Created by  <PERSON>ujiangtao on 2017/8/30.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRModuleManager.h"

@interface BRModuleManager ()

@property (strong, nonatomic) NSMutableArray <id <BRModule>> *modules;

@end

@implementation BRModuleManager

+ (instancetype)sharedInstance {
    static BRModuleManager *brModuleManager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        brModuleManager = [[[self class] alloc] init];
    });
    return brModuleManager;
}

- (NSMutableArray<id<BRModule>> *)modules {
    if (!_modules) {
        _modules = [NSMutableArray array];
    }
    return _modules;
}

- (void)addmodule:(id<BRModule>)module {
    if (![self.modules containsObject:module]) {
        [self.modules addObject:module];
    }
}

- (void)loadModulesWithPlistFile:(NSString *)plistFile {
    NSArray <NSString *> *moduleNames = [NSArray arrayWithContentsOfFile:plistFile];
    
    for (NSString *moduleName in moduleNames) {
        id<BRModule> module = [[NSClassFromString(moduleName) alloc]  init];
        [self addmodule:module];
    }
}

- (NSArray<id<BRModule>> *)allModules {
    return self.modules;
}

#pragma mark - UIApplicationDelegate's methods

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
    for (id<BRModule> module in self.modules) {
        if ([module respondsToSelector:_cmd]) {
            [module application:application didFinishLaunchingWithOptions:launchOptions];
        }
    }
    return YES;
}

- (void)applicationWillResignActive:(UIApplication *)application
{
    for (id<BRModule> module in self.modules) {
        if ([module respondsToSelector:_cmd]) {
            [module applicationWillResignActive:application];
        }
    }
}

- (void)applicationDidEnterBackground:(UIApplication *)application
{
    for (id<BRModule> module in self.modules) {
        if ([module respondsToSelector:_cmd]) {
            [module applicationDidEnterBackground:application];
        }
    }
}

- (void)applicationWillEnterForeground:(UIApplication *)application
{
    for (id<BRModule> module in self.modules) {
        if ([module respondsToSelector:_cmd]) {
            [module applicationWillEnterForeground:application];
        }
    }
}

- (void)applicationDidBecomeActive:(UIApplication *)application
{
    for (id<BRModule> module in self.modules) {
        if ([module respondsToSelector:_cmd]) {
            [module applicationDidBecomeActive:application];
        }
    }
}

- (void)applicationWillTerminate:(UIApplication *)application
{
    for (id<BRModule> module in self.modules) {
        if ([module respondsToSelector:_cmd]) {
            [module applicationWillTerminate:application];
        }
    }
}

- (void)application:(UIApplication *)application
didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
{
    for (id<BRModule> module in self.modules) {
        if ([module respondsToSelector:_cmd]) {
            [module application:application didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
        }
    }
}

- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error {
    for (id<BRModule> module in self.modules) {
        if ([module respondsToSelector:_cmd]) {
            [module application:application didFailToRegisterForRemoteNotificationsWithError:error];
        }
    }
}


@end
