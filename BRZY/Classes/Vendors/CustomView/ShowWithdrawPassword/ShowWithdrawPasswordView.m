//
//  ShowWithdrawPasswordView.m
//  BRZY
//
//  Created by <PERSON> on 2025/06/21.
//  Copyright © 2025年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "ShowWithdrawPasswordView.h"

@interface ShowWithdrawPasswordView () {
    UIWindow *_window;
}

@property (nonatomic, copy)WithdrawPasswordBlock dataResultBlock;
@property (nonatomic, copy)WithdrawPasswordCancelBlock cancelBlock;
@property (nonatomic, assign)float viewH;

@end

@implementation ShowWithdrawPasswordView

- (instancetype)initWithBlock:(WithdrawPasswordBlock)backPassword :(WithdrawPasswordCancelBlock)cancelClick {
    self = [super initWithFrame:[UIScreen mainScreen].bounds];
    
    if (self) {
        _window = [UIApplication sharedApplication].keyWindow;
        [_window addSubview:self];
        self.backgroundColor = [UIColor blackColor];
        self.alpha = 0.3;
        self.dataResultBlock = backPassword;
        self.cancelBlock = cancelClick;
        [self createUI];
    }
    
    return self;
}

- (void)createUI {
    
    self.viewH = FONTSIZE(50) + FONTSIZE(15) + FONTSIZE(40) + FONTSIZE(25) + FONTSIZE(40) + FONTSIZE(35) + FONTSIZE(40) + FONTSIZE(25);
    __weak typeof(self)mySelf = self;

    self.selectViewList = [[ShowWithdrawPasswordContent alloc] initWithFrame:CGRectMake(FONTSIZE(30), self.frame.size.height, self.frame.size.width-FONTSIZE(30)*2, self.viewH) withBlock:^(NSString *password) {
        
        mySelf.dataResultBlock(password);
        
    } :^{
        
        if (mySelf.cancelBlock) {
            mySelf.cancelBlock();
        }
        [mySelf removeView];
        
    }];
    self.selectViewList.layer.masksToBounds = YES;
    self.selectViewList.layer.cornerRadius = 8;
    [_window addSubview:_selectViewList];
    
    [UIView animateWithDuration:0.3 animations:^{
        mySelf.selectViewList.frame = CGRectMake(FONTSIZE(30), self.frame.size.height/2-FONTSIZE(50)-_viewH/2, self.frame.size.width-FONTSIZE(30)*2, self.viewH);
    } completion:nil];
    
    
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapBgEvent)];
    [self addGestureRecognizer:tapGesture];
}

- (void)removeView {
    __weak typeof(self)mySelf = self;
    [UIView animateWithDuration:0.3 animations:^{
        mySelf.selectViewList.frame = CGRectMake(FONTSIZE(30), mySelf.frame.size.height, mySelf.frame.size.width-FONTSIZE(30)*2, mySelf.viewH);
        
    } completion:^(BOOL finished) {
        [mySelf.selectViewList removeFromSuperview];
        mySelf.selectViewList = nil;
        [mySelf removeFromSuperview];
    }];
}

- (void)setIsHideWhenTapBackground:(BOOL)isHideWhenTapBackground {
    _isHideWhenTapBackground = isHideWhenTapBackground;
}

- (void)tapBgEvent {
    if (_isHideWhenTapBackground) {
        [self removeView];
    }
}

@end

@interface ShowWithdrawPasswordContent () {
    UIWindow *_window;
}

@property (nonatomic, copy)WithdrawPasswordBlock resultBlock;
@property (nonatomic, copy)WithdrawPasswordCancelBlock cancelClickBlock;

@end

@implementation ShowWithdrawPasswordContent

- (instancetype)initWithFrame:(CGRect)frame withBlock:(WithdrawPasswordBlock)passwordBack :(WithdrawPasswordCancelBlock)cancelBtnClick {
    self = [super initWithFrame:frame];
    
    if (self) {
        _window = [UIApplication sharedApplication].keyWindow;
        self.backgroundColor = [UIColor whiteColor];
        self.resultBlock = passwordBack;
        self.cancelClickBlock = cancelBtnClick;
        [self createUI];
    }
    
    return self;
}

- (void)createUI {
    
    // 标题
    self.titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, self.frame.size.width, FONTSIZE(50))];
    self.titleLabel.text = @"输入提现密码";
    self.titleLabel.font = FONT_Regular(18);
    self.titleLabel.textColor = [UIColor colorWithRed:83.0/255 green:133.0/255 blue:223.0/255 alpha:1.f];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    [self addSubview:self.titleLabel];
    
    // 密码输入框背景
    UIView *passwordBgView = [[UIView alloc] initWithFrame:CGRectMake(FONTSIZE(20), self.titleLabel.frame.origin.y + self.titleLabel.frame.size.height + FONTSIZE(15), self.frame.size.width - FONTSIZE(40), FONTSIZE(40))];
    passwordBgView.backgroundColor = [UIColor colorWithRed:COLORNUM(246.0) green:COLORNUM(246.0) blue:COLORNUM(246.0) alpha:1.f];
    passwordBgView.layer.cornerRadius = 5;
    passwordBgView.layer.masksToBounds = YES;
    [self addSubview:passwordBgView];
    
    // 密码输入框
    self.passwordTextField = [[UITextField alloc] initWithFrame:CGRectMake(FONTSIZE(15), 0, passwordBgView.frame.size.width - FONTSIZE(30), passwordBgView.frame.size.height)];
    self.passwordTextField.placeholder = @"请输入6位数字密码";
    self.passwordTextField.font = FONT_Light(16);
    self.passwordTextField.textColor = [UIColor colorWithRed:COLORNUM(29.0) green:COLORNUM(32.0) blue:COLORNUM(36.0) alpha:1.f];
    self.passwordTextField.keyboardType = UIKeyboardTypeNumberPad;
    self.passwordTextField.secureTextEntry = YES;
    self.passwordTextField.clearButtonMode = UITextFieldViewModeWhileEditing;
    [passwordBgView addSubview:self.passwordTextField];
    
    // 取消按钮
    self.cancel = [UIButton buttonWithType:UIButtonTypeRoundedRect];
    self.cancel.frame = CGRectMake(FONTSIZE(20), passwordBgView.frame.origin.y + passwordBgView.frame.size.height + FONTSIZE(25), (self.frame.size.width - FONTSIZE(40) - FONTSIZE(10))/2, FONTSIZE(40));
    [self.cancel setTitle:@"取消" forState:UIControlStateNormal];
    [self.cancel setTitleColor:[UIColor colorWithRed:COLORNUM(29.0) green:COLORNUM(32.0) blue:COLORNUM(36.0) alpha:1.f] forState:UIControlStateNormal];
    self.cancel.titleLabel.font = FONT_Regular(16);
    self.cancel.backgroundColor = [UIColor colorWithRed:COLORNUM(246.0) green:COLORNUM(246.0) blue:COLORNUM(246.0) alpha:1.f];
    self.cancel.layer.cornerRadius = 5;
    self.cancel.layer.masksToBounds = YES;
    [self.cancel addTarget:self action:@selector(cancelClick) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:self.cancel];
    
    // 确认按钮
    self.yes = [UIButton buttonWithType:UIButtonTypeRoundedRect];
    self.yes.frame = CGRectMake(self.cancel.frame.origin.x + self.cancel.frame.size.width + FONTSIZE(10), self.cancel.frame.origin.y, self.cancel.frame.size.width, FONTSIZE(40));
    [self.yes setTitle:@"确认" forState:UIControlStateNormal];
    [self.yes setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.yes.titleLabel.font = FONT_Regular(16);
    self.yes.backgroundColor = [UIColor colorWithRed:83.0/255 green:133.0/255 blue:223.0/255 alpha:1.f];
    self.yes.layer.cornerRadius = 5;
    self.yes.layer.masksToBounds = YES;
    [self.yes addTarget:self action:@selector(confirmClick) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:self.yes];
}

- (void)cancelClick {
    if (self.cancelClickBlock) {
        self.cancelClickBlock();
    }
}

- (void)confirmClick {
    NSString *password = self.passwordTextField.text;
    
    if (!password.length) {
        [_window makeToast:@"请输入提现密码" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    if (password.length != 6) {
        [_window makeToast:@"提现密码必须为6位数字" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    // 检查是否全为数字
    NSCharacterSet *numericSet = [NSCharacterSet decimalDigitCharacterSet];
    NSCharacterSet *passwordSet = [NSCharacterSet characterSetWithCharactersInString:password];
    if (![numericSet isSupersetOfSet:passwordSet]) {
        [_window makeToast:@"提现密码只能包含数字" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    if (self.resultBlock) {
        self.resultBlock(password);
    }
}

@end