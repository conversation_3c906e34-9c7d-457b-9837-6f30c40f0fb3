//
//  ShowWithdrawPasswordView.h
//  BRZY
//
//  Created by <PERSON> on 2025/06/21.
//  Copyright © 2025年 <PERSON> YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void(^WithdrawPasswordBlock)(NSString *password);
typedef void(^WithdrawPasswordCancelBlock)();

@interface ShowWithdrawPasswordContent : UIView

@property (nonatomic, strong)UILabel *titleLabel;
@property (nonatomic, strong)UIButton *yes;
@property (nonatomic, strong)UIButton *cancel;
@property (nonatomic, strong)UITextField *passwordTextField;

- (instancetype)initWithFrame:(CGRect)frame withBlock:(WithdrawPasswordBlock)passwordBack :(WithdrawPasswordCancelBlock)cancelBtnClick;

@end

@interface ShowWithdrawPasswordView : UIView

@property (nonatomic, strong)ShowWithdrawPasswordContent *selectViewList;

- (void)removeView;
- (instancetype)initWithBlock:(WithdrawPasswordBlock)backPassword :(WithdrawPasswordCancelBlock)cancelClick;

@property (nonatomic, assign) BOOL isHideWhenTapBackground;

@end