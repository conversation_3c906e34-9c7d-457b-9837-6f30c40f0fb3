//
//  UserManager.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/23.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UserInfo.h"
#import "ConfigInfo.h"

@interface UserManager : NSObject

@property (nonatomic) BOOL isLogin;

+ (instancetype)shareInstance;

- (void)loginUsername:(NSString *)username
             password:(NSString *)password
                  sms:(NSString *)sms
              success:(void(^)(ConfigInfo *info))success
               failed:(void (^)(BRError *error))failed;



- (void)loginAutomatic;

- (NSString *)getUserId;

- (NSString *)getUserName;

- (NSString *)getName;

- (NSString *)getPlainPassword;

- (NSString *)getTelephone;

- (NSString *)getUserInfoByKey:(NSString *)key;

- (NSString *)getApp_ShowMedicalServiceFeeRule;

//获取微信openId
- (NSString *)getWXOpenId;

- (void)updateApp_ShowMedicalServiceFeeRule:(NSString *)serviceFeeRule;


- (void)logout;

//存储二维码图片
- (void)writeQRCodeImageToFile:(UIImage *)image;
//获取二维码图片
- (UIImage *)getQRCodeImageWithFile;
//删除二维码图片
- (void)removeQRCodeImage;
//二维码图片是否存在
- (BOOL)isHaveQRCodeImage;

//更新用户信息
- (void)updateUserInfo:(UserInfo *)userInfo;
//更新指定的某条用户信息
- (void)setUserInfoWithValue:(id)value key:(NSString *)key;
@end
