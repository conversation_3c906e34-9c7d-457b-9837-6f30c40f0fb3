//
//  Utils.m
//  BRZY
//
//  Created by  <PERSON>ujiangta<PERSON> on 2017/8/22.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "Utils.h"
#import <CommonCrypto/CommonDigest.h>
#import "LoginViewController.h"
#import "BaseTabBarController.h"
#import "MBProgressHUD.h"
#import "PopoverView.h"
#import "DateTools.h"
#import "BRActionSheetView.h"
#import "BRAlertView.h"
#import "SocketManager.h"
#import "JPUSHService.h"
#import "BRMedicationWarningAlertView.h"
#import "AuthCheckHelper.h"
#import "ShareContentView.h"
#import "BRPrivacyPopView.h"
#import "LSTPopView.h"
#import "WXApi.h"
#import "IMDataBaseManager.h"

static Utils *utils = nil;

@interface Utils ()

@property (nonatomic, weak)  LSTPopView *popView;

@end

@implementation Utils

// 获取当前顶层视图控制器的辅助方法
- (UIViewController *)topViewController {
    UIViewController *rootViewController = [UIApplication sharedApplication].keyWindow.rootViewController;
    return [self topViewControllerWithRootViewController:rootViewController];
}

// 递归查找顶层视图控制器
- (UIViewController *)topViewControllerWithRootViewController:(UIViewController *)rootViewController {
    if ([rootViewController isKindOfClass:[UITabBarController class]]) {
        UITabBarController *tabBarController = (UITabBarController *)rootViewController;
        return [self topViewControllerWithRootViewController:tabBarController.selectedViewController];
    } else if ([rootViewController isKindOfClass:[UINavigationController class]]) {
        UINavigationController *navigationController = (UINavigationController *)rootViewController;
        return [self topViewControllerWithRootViewController:navigationController.visibleViewController];
    } else if (rootViewController.presentedViewController) {
        UIViewController *presentedViewController = rootViewController.presentedViewController;
        return [self topViewControllerWithRootViewController:presentedViewController];
    } else {
        return rootViewController;
    }
}


+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        utils = [[Utils alloc] init];
    });
    return utils;
}

+ (NSString *)md5_32:(NSString *)str {
    const char *original_str = [str UTF8String];
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    CC_MD5(original_str, (CC_LONG)strlen(original_str), result);
    NSMutableString *ret = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH*2];//
    for(int i = 0; i<CC_MD5_DIGEST_LENGTH; i++) {
        [ret appendFormat:@"%02X",result[i]];
    }
    return ret;
}

+ (void)startLogin {

    AppDelegate *app = (AppDelegate *)[UIApplication sharedApplication].delegate;
    if ([app.window.rootViewController isKindOfClass:[BaseTabBarController class]]) {
        BaseTabBarController *baseTabController = (BaseTabBarController *)app.window.rootViewController;
        [[SocketManager shareInstance] removeDelegate:baseTabController];
    }
    for (UIView *view in app.window.subviews) {
        [view removeFromSuperview];
    }
    //设置自动登录为关
    [Config storeisAutoLogin:NO];

    //config Delegate
    [[Config shareInstance] removeAllDelegate];

    //断开连接 清除之前连接
    [[SocketManager shareInstance] disConnect];

    //设置角标为0 清除通知列表
    [UIApplication sharedApplication].applicationIconBadgeNumber = 0;
    [JPUSHService setBadge:0];

    UINavigationController *navi = [[UINavigationController alloc] initWithRootViewController:[[LoginViewController alloc] init]];
    navi.navigationBar.hidden = YES;
    app.window.rootViewController = navi;
}

+ (void)endLogin {
    AppDelegate *app = (AppDelegate *)[UIApplication sharedApplication].delegate;
    app.window.rootViewController = [[BaseTabBarController alloc] init];
}

+ (UIImage*)createImageWithColor:(UIColor*) color
{
    CGRect rect=CGRectMake(0,0, 1, 1);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    UIImage *theImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return theImage;
}

+ (UIImage *)createQRCodeFromString:(NSString *)string
{
    NSData *stringData = [string dataUsingEncoding:NSUTF8StringEncoding];

    CIFilter *QRFilter = [CIFilter filterWithName:@"CIQRCodeGenerator"];
    // Set the message content and error-correction level
    [QRFilter setValue:stringData forKey:@"inputMessage"];
    [QRFilter setValue:@"M" forKey:@"inputCorrectionLevel"];

    CGFloat scale = 5;
    CGImageRef cgImage = [[CIContext contextWithOptions:nil] createCGImage:QRFilter.outputImage fromRect:QRFilter.outputImage.extent];

    //Scale the image usign CoreGraphics
    CGFloat width = QRFilter.outputImage.extent.size.width * scale;
    UIGraphicsBeginImageContext(CGSizeMake(width, width));
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetInterpolationQuality(context, kCGInterpolationNone);
    CGContextDrawImage(context, CGContextGetClipBoundingBox(context), cgImage);
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();

    //Cleaning up
    UIGraphicsEndImageContext();
    CGImageRelease(cgImage);

    return image;
}

+ (void)br_showMedicationWarningAlertViewWithMessage:(NSAttributedString *)attributedString completion:(void (^)(BOOL))handle {

    BRMedicationWarningAlertView *alertView = [[BRMedicationWarningAlertView alloc] initWithFrame:[UIScreen mainScreen].bounds];

    AppDelegate *app = (AppDelegate *)[UIApplication sharedApplication].delegate;
    [app.window addSubview:alertView];

    alertView.contentString = attributedString;

    alertView.isNextBlock = ^(BOOL isNext) {
        handle(isNext);
    };
}

+ (void)br_showBaseFeeTipsWithBlock:(void(^)(void))handle{

    NSString *text = @"基础药费提示";

    BRAlertView *alertView = [[BRAlertView alloc] init];
    alertView.isHideWhenTapBackground = YES;
    alertView.hintTitleLabel.text = text;
    [alertView.okButton setTitle:@"知道了" forState:UIControlStateNormal];
    [alertView showAlertView:@"患者支付订单后，请到\"管理-->我的钱包\"查看【医技服务费/诊后服务费】及【诊费】。" completion:^{
        [alertView close];
        if(handle){
            handle();
        }
    }];


}

+ (void)br_showSumCountFeeTipsWithBlock:(void(^)(void))handle{
    NSString *text = @"提示信息";

    BRAlertView *alertView = [[BRAlertView alloc] init];
    alertView.isHideWhenTapBackground = YES;
    alertView.hintTitleLabel.text = text;
    [alertView.okButton setTitle:@"知道了" forState:UIControlStateNormal];
    [alertView showAlertView:@"1、患者支付订单时只显示总计费用，不显示诊费等明细。\n2、付款后24小时内顺丰快递发货(节假日、膏/丸/散/胶囊剂型除外)。\n3、总计费用(不含制作费、诊费)满100元包邮，不满100元收取15元快递费。\n4、由于处方调剂后无法再销售，非质量问题不办理退货退款，敬请谅解！" completion:^{
        [alertView close];
        if(handle){
            handle();
        }
    }];
    
    // 设置内容文字左对齐
    alertView.titleLabel.textAlignment = NSTextAlignmentLeft;
}

+ (void)br_showTipsInfoWithTitle:(NSString *)title message:(NSString *)message btnTitle:(NSString *)btnTitle handleBlock:(void(^)(void))handle{
    NSString *text = title;

    BRAlertView *alertView = [[BRAlertView alloc] init];
    alertView.isHideWhenTapBackground = YES;
    alertView.hintTitleLabel.text = text;
    [alertView.okButton setTitle:btnTitle forState:UIControlStateNormal];
    [alertView showAlertView:message completion:^{
        [alertView close];
        if(handle){
            handle();
        }
    }];
}


+ (void)br_showAdditoinalFeeTipsWithBlock:(void(^)(void))handle{

    NSString *text = @"附加诊费提示";

    BRAlertView *alertView = [[BRAlertView alloc] init];
    alertView.isHideWhenTapBackground = YES;
    alertView.hintTitleLabel.text = text;
    [alertView.okButton setTitle:@"知道了" forState:UIControlStateNormal];
    [alertView showAlertView:@"患者端不会单独显示诊费，只显示总计金额。" completion:^{
        [alertView close];
        if(handle){
            handle();
        }
    }];

}

+ (void)br_showEstimateNumberOfDayWithBlock:(void (^)(void))handle {

    NSString *text = @"预计服用提示";

    BRAlertView *alertView = [[BRAlertView alloc] init];
    alertView.isHideWhenTapBackground = YES;
    alertView.hintTitleLabel.text = text;
    [alertView.okButton setTitle:@"知道了" forState:UIControlStateNormal];
    [alertView showAlertView:@"服用天数只是预估，最终以药房实际制作出药量为准。" completion:^{
        [alertView close];
        if(handle){
            handle();
        }
    }];
}

+ (void)br_showQuickPrescribeHintWithIsMessage:(BOOL)isMessage Block:(void (^)(void))handle {

    NSString *text = @"微信开方提示";
    if (isMessage) {
        text = @"短信开方提示";
    }

    BRAlertView *alertView = [[BRAlertView alloc] init];
    alertView.isHideWhenTapBackground = YES;
    alertView.hintTitleLabel.text = text;
    [alertView.okButton setTitle:@"知道了" forState:UIControlStateNormal];
    [alertView showAlertView:@"该功能仅适用于一次性诊断或年老患者。患者未扫码关注医生，不享有在线交流、管理医案、调用历史药方、自动提醒支付、管理患者等丰富功能。长期复诊患者建议【微信扫码开方】" completion:^{
        [alertView close];
        if(handle){
            UIViewController *topVC = [[Utils sharedInstance] topViewController];

            // 从UserDefaults读取用户邀请状态
            BOOL isInviteUser = [[NSUserDefaults standardUserDefaults] boolForKey:[NSString stringWithFormat:@"is_invite_user_%@", [UserManager shareInstance].getUserId]];

            if (isInviteUser) {
                // 邀请用户直接打开页面
                handle();
            } else {
                // 非邀请用户需要检查认证状态
                NSLog(@"Utils: 微信开方提示, 使用最新认证状态判断");
                [AuthCheckHelper canUseAuthFeatureWithLatestState:topVC completion:^(BOOL canUse) {
                    if (canUse) {
                        handle();
                    }
                }];
            }
        }
    }];
}

+ (void)br_showQuickPrescribeHintInSessionWithHandle:(void (^)(void))handle {

    NSString *text = @"快速开方提示";

    BRAlertView *alertView = [[BRAlertView alloc] init];
    alertView.isHideWhenTapBackground = YES;
    alertView.hintTitleLabel.text = text;
    [alertView.okButton setTitle:@"知道了" forState:UIControlStateNormal];
    [alertView showAlertView:@"由于患者未通过医生电子名片关注医生，患者未通过平台与医生进行绑定，平台消息无法发送，导致患者无订单未支付提醒功能/无聊天功能/无骚扰拦截功能/无患者咨询费用功能/无出诊安排通知功能/无群发公告/无电子问诊单等功能，建议长期复诊患者使用邀请扫描医生电子名片开方" completion:^{
        [alertView close];
        if(handle){
            handle();
        }
    }];

}


+ (void)br_showInviteScanPrescribeWithBlock:(void(^)(void))handle {

    BRAlertView *alertView = [[BRAlertView alloc] init];
    alertView.isHideWhenTapBackground = YES;
    alertView.hintTitleLabel.text = @"扫码开方提示";
    [alertView.okButton setTitle:@"知道了" forState:UIControlStateNormal];

    NSString *showStr = @"该功能适合长期患者。患者微信扫码关注医生，可实现在线交流、管理医案、调用历史药方、自动提醒支付、管理患者等丰富功能。";

    [alertView showAlertView:showStr completion:^{
        [alertView close];

        if(handle){
            UIViewController *topVC = [[Utils sharedInstance] topViewController];

            // 从UserDefaults读取用户邀请状态
            BOOL isInviteUser = [[NSUserDefaults standardUserDefaults] boolForKey:[NSString stringWithFormat:@"is_invite_user_%@", [UserManager shareInstance].getUserId]];

            if (isInviteUser) {
                // 邀请用户直接打开页面
                handle();
            } else {
                // 非邀请用户需要检查认证状态
                NSLog(@"Utils: 扫码开方提示, 使用最新认证状态判断");
                [AuthCheckHelper canUseAuthFeatureWithLatestState:topVC completion:^(BOOL canUse) {
                    if (canUse) {
                        handle();
                    }
                }];
            }
        }
    }];
}

+ (void)br_showClearCacheHintWithBlock:(void (^)(BOOL))handle{
    BRAlertView *alertView = [[BRAlertView alloc] init];
    alertView.isHideWhenTapBackground = YES;
    alertView.hintTitleLabel.text = @"清除缓存";
    [alertView.okButton setTitle:@"清除缓存" forState:UIControlStateNormal];
    NSString *text = @"确认清除缓存后以下数据将被清除【以下内容清理后，不会删除您的聊天内容，可通过历史记录随时查看】:\n1. 联系人交流页面本地记录;\n2.系统消息记录;\n3.本地缓存音频，图片数据;\n是否确认清除?";
    [alertView showAlertViewWithCancelButton:text completion:^(BOOL isOk) {
        [alertView close];
        if (handle) {
            handle(isOk);
        }
    }];

    alertView.titleLabel.textAlignment = NSTextAlignmentLeft;
}

+ (void)showAlertViewWithTitle:(NSString *)title message:(NSString *)message inViewController:(UIViewController *)viewController{
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"提示" message:message preferredStyle:UIAlertControllerStyleAlert];

    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:nil];
    [alertController addAction:okAction];

    [viewController presentViewController:alertController animated:YES completion:nil];
}

+ (void)showAlertViewWithTitle:(NSString *)title message:(NSString *)message inViewController:(UIViewController *)viewController Complection:(void (^)(BOOL))handle {
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:@"提示" message:message preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        handle(NO);
    }];

    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        handle(YES);
    }];

    [alertController addAction:cancelAction];
    [alertController addAction:okAction];

    [viewController presentViewController:alertController animated:YES completion:nil];
}

+ (void)br_showAlertViewTitle:(NSString *)title message:(NSString *)message confirmButtonTitle:(NSString *)btnTitle completion:(void (^)(void))handle {

    BRAlertView *alertView = [[BRAlertView alloc] init];


    [alertView showAlertView:message completion:^{
        if (handle) {
            handle();
        }
    }];

    alertView.titleLabel.text = title;
    alertView.okButton.titleLabel.text = btnTitle;

}

+ (void)br_showAlertViewMessage:(NSString *)message completion:(void (^)(void))handle {
    BRAlertView *alertView = [[BRAlertView alloc] init];

    [alertView showAlertView:message completion:^{
        if (handle) {
            handle();
        }
    }];
}

+ (void)br_showAlertViewHasCancelButtonMessage:(NSString *)message completion:(void (^)(BOOL))handle {
    BRAlertView *alertView = [[BRAlertView alloc] init];

    [alertView showAlertViewWithCancelButton:message completion:^(BOOL isOk) {
        if (handle) {
            handle(isOk);
        }
    }];
}

+ (void)br_newShowAlertViewMessage:(NSString *)message completion:(void (^)(void))handle {

    BRAlertView *alertView = [[BRAlertView alloc] init];
    alertView.isHideWhenTapBackground = YES;

    [alertView showAlertView:message completion:^{
        [alertView close];
        if (handle) {
            handle();
        }
    }];
}

+ (void)br_showAlertViewHasCancelButtonMessage:(NSString *)message buttonTitle:(NSString *)buttonTitle completion:(void (^)(BOOL))handle {
    BRAlertView *alertView = [[BRAlertView alloc] init];
    [alertView.okButton setTitle:buttonTitle forState:UIControlStateNormal];

    [alertView showAlertViewWithCancelButton:message completion:^(BOOL isOk) {
        if (handle) {
            handle(isOk);
        }
    }];
}

+ (void)br_showAlertViewHasInputViewMessage:(NSString *)message placeholder:(NSString *)placeholder contentText:(NSString *)contentText completion:(void (^)(BOOL, NSString *))handle    {
    BRAlertView *alertView = [[BRAlertView alloc] init];

    [alertView showAlertViewWithInputView:message placeholder:placeholder contentText:(NSString *)contentText completion:^(BOOL isOk, NSString * _Nullable text) {
        if (handle) {
            handle(isOk,text);
        }
    }];
}

+ (void)br_showAlertViewHasInputViewMessage:(NSString *)message okButtonTitle:(NSString *)buttonTitle placeholder:(NSString *)placeholder contentText:(NSString *)contentText completion:(void (^)(BOOL, NSString *))handle {
    BRAlertView *alertView = [[BRAlertView alloc] init];
    [alertView.okButton setTitle:buttonTitle forState:UIControlStateNormal];

    [alertView showAlertViewWithInputView:message placeholder:placeholder contentText:(NSString *)contentText completion:^(BOOL isOk, NSString * _Nullable text) {
        if (handle) {
            handle(isOk,text);
        }
    }];
}

+ (BRAlertView *)br_showAlertHasMobileInputMessage:(NSString *)message mobileText:(NSString *)mobileText okButtonTitle:(NSString *)buttonTitle placeholder:(NSString *)placeholder completion:(void (^)(BOOL, NSString *))handle{

    BRAlertView *alertView = [[BRAlertView alloc] init];
    [alertView.okButton setTitle:buttonTitle forState:UIControlStateNormal];

    [alertView showAlertViewWithInputMobile:message mobileText:mobileText placeholder:placeholder completion:^(BOOL isOk, NSString * _Nullable mobile) {
        if (handle) {
            handle(isOk, mobile);
        }
    }];

    return alertView;
}

+ (void)br_showAlertViewHasTextFieldMessage:(NSString *)message mobile:(NSString *)mobile getAuthcode:(void (^)(NSString *))authcodeHandle completion:(void (^)(BOOL, NSString *))handle {
    BRAlertView *alertView = [[BRAlertView alloc] init];

    [alertView showAlertViewWithTextField:message mobile:mobile getAuthcode:^(NSString * _Nullable mobile) {
        authcodeHandle(mobile);
    } completion:^(BOOL isShow, NSString * _Nonnull authCode) {
        handle(isShow,authCode);
    }];
}

+ (void)br_showShareViewClickCompletionIndex:(void(^)(NSInteger index))completion {

    BRActionSheetView *actionSheetView = [[BRActionSheetView alloc] init];
    actionSheetView.title = @"分享到";

    ShareContentView *contentView = [[ShareContentView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 230/2.0)];
    actionSheetView.customView = contentView;

    [[contentView.shareButton1 rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        completion(0);
        [actionSheetView close];
    }];

    [[contentView.shareButton2 rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        completion(1);
        [actionSheetView close];
    }];

    [[contentView.shareButton3 rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        completion(2);
        [actionSheetView close];
    }];

    [actionSheetView show];
}

+ (void)br_showCoverGuideView {

    [Config changeFirstOpenStateById:[NSString stringWithFormat:@"drugStoreCoverImage_%@",[UserManager shareInstance].getUserId]];

    UIImage *coverImage = [UIImage imageNamed:@"cover_img"];

    if (isiPhoneX) {
        coverImage = [UIImage imageNamed:@"cover_iphoneX_img"];
    }
    else if (isiPhone6P) {
        coverImage = [UIImage imageNamed:@"cover_6p_img"];
    }
    else if (isiPhone6) {
        coverImage = [UIImage imageNamed:@"cover_6_img"];
    }
    else if (isiPhone5) {
        coverImage = [UIImage imageNamed:@"cover_5_img"];
    }

    UIImageView *imageView = [[UIImageView alloc] initWithFrame:[UIScreen mainScreen].bounds];
    imageView.image = coverImage;
    imageView.userInteractionEnabled = YES;
    imageView.contentMode = UIViewContentModeScaleAspectFit;

    UIImage *coverKnowBtnImage = [UIImage imageNamed:@"cover_know_btn"];
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    [button setImage:coverKnowBtnImage forState:UIControlStateNormal];
    [imageView addSubview:button];

    [button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(@(kScreenHeight / 2.0 + 100));
        make.centerX.equalTo(imageView);
        make.size.mas_equalTo(coverKnowBtnImage.size);
    }];

    [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(__kindof UIControl * _Nullable x) {
        [imageView removeFromSuperview];
    }];

    AppDelegate *app =(AppDelegate *)[UIApplication sharedApplication].delegate;
    [app.window addSubview:imageView];
}

+ (MBProgressHUD *)createLoadingHUD {
    return [Utils createLoadingHUDWithTitle:nil];
}

+ (MBProgressHUD *)createLoadingHUDWithTitle:(NSString *)title {
    UIWindow *window;
    NSArray *windows = [[UIApplication sharedApplication] windows];
    for (UIWindow *eachWindow in windows) {
        if ([eachWindow isKeyWindow]) {
            window = eachWindow;
        }
    }

    MBProgressHUD *HUD = [[MBProgressHUD alloc] initWithView:window];
    if (title) {
        HUD.label.text = title;
    }
    HUD.mode = MBProgressHUDModeIndeterminate;
    HUD.tintColor = [[UIColor blackColor] colorWithAlphaComponent:0.7];
    //更改中间加载框背景色
    HUD.bezelView.style = MBProgressHUDBackgroundStyleSolidColor;
    HUD.bezelView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.7];
    //文字颜色
    HUD.label.textColor = [UIColor whiteColor];
    //progress 颜色
    [UIActivityIndicatorView appearanceWhenContainedInInstancesOfClasses:@[[MBProgressHUD class]]].color = [UIColor whiteColor];
    //
    HUD.minShowTime = 1;
    [window addSubview:HUD];
    [HUD showAnimated:YES];
    HUD.removeFromSuperViewOnHide = YES;
    return HUD;
}

+ (BOOL)validateMobile:(NSString *)mobileNum
{
    NSString *regex = @"^1[3|5|7|8][0-9]\\d{8}$";
    NSPredicate *pred = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];

    if(![pred evaluateWithObject:mobileNum])
    {
        return NO;
    }

    else
    {
        return YES;
    }
}

+ (NSString *)createMessageId {
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc]init];
    [dateFormatter setDateFormat:@"yyyyMMddhhmmssSSS"];
    NSString *dateStr = [dateFormatter stringFromDate:[Config currentServerDate]];

    int x = arc4random() % 1000;

    return [NSString stringWithFormat:@"%@%d0000",dateStr,x];
}

+ (void)showPopItemListViewFromSender:(UIBarButtonItem *)sender selectedIndex:(void (^)(NSInteger))selectedHandle{
    UIImage *actionInviteImage = [UIImage imageNamed:@"popover_invite_icon"];
    UIImage *actionGroupSendImage = [UIImage imageNamed:@"popover_groupsend_icon"];
    UIImage *actionGroupingImage = [UIImage imageNamed:@"popover_grouping_icon"];

    PopoverAction *actionInvite = [PopoverAction actionWithImage:actionInviteImage title:NSLocalizedString(@"invite patient", nil) handler:^(PopoverAction *action) {
        selectedHandle(0);
    }];

    PopoverAction *actionGroupSend = [PopoverAction actionWithImage:actionGroupSendImage title:NSLocalizedString(@"send group message", nil) handler:^(PopoverAction *action) {
        selectedHandle(1);
    }];

    PopoverAction *actionGrouping = [PopoverAction actionWithImage:actionGroupingImage title:NSLocalizedString(@"grouping manager", nil) handler:^(PopoverAction *action) {
        selectedHandle(2);
    }];

    PopoverView *popoverView = [PopoverView popoverView];
    popoverView.style = PopoverViewStyleDark;
    [popoverView showToPoint:CGPointMake(SCREEN_WIDTH - 20, 64) withActions:@[actionInvite,actionGroupSend,actionGrouping]];
}

+ (void)showActionSheetWithTitle:(NSString *)title buttons:(NSArray *)buttons complection:(void (^)(NSInteger))clickHandle {

    BRActionSheetView *actionsheet = [[BRActionSheetView alloc] init];
    actionsheet.title = title;
    actionsheet.buttons = buttons;
//    [actionsheet setWithdrawMethodButtons:buttons];

    __weak BRActionSheetView *weakActionSheet = actionsheet;
    actionsheet.clickActionButtonCallBack = ^(NSInteger index) {
        clickHandle(index);
        [weakActionSheet close];
    };
    [actionsheet show];
}

+ (void)showWithdrawActionSheetWithTitle:(NSString *)title buttons:(NSArray *)buttons complection:(void (^)(NSInteger))clickHandle {

    BRActionSheetView *actionsheet = [[BRActionSheetView alloc] init];
    actionsheet.title = title;
//    actionsheet.buttons = buttons;
    [actionsheet setWithdrawMethodButtons:buttons];

    __weak BRActionSheetView *weakActionSheet = actionsheet;
    actionsheet.clickActionButtonCallBack = ^(NSInteger index) {
        clickHandle(index);
        [weakActionSheet close];
    };
    [actionsheet show];
}

+ (NSString *)currentTimestamp {
    return [NSString stringWithFormat:@"%.0f", [[Config currentServerDate] timeIntervalSince1970] * 1000];
}

+ (NSString *)messageTimeformatForTimedate:(NSDate *)timeDate {
    NSString *showString = nil;

    if ([timeDate timeIntervalSince1970] == 0) {
        return @"";
    }
    //今天
    if ([timeDate isToday]) {
        showString = [timeDate formattedDateWithFormat:@"HH:mm"];
    }
    //昨天
    else if ([timeDate isYesterday]) {
        showString = [timeDate formattedDateWithFormat:@"昨天 HH:mm"];
    }
    //今年
    else if ([timeDate year] == [[Config currentServerDate] year]) {
        showString =[timeDate formattedDateWithFormat:@"MM/dd HH:mm"];
    }
    //超过一年
    else {
        showString = [timeDate stringWithFormat:@"YYYY/MM/dd HH:mm"];
    }

    return showString;
}

+ (CGFloat)fontScale {
    CGFloat scale = 1;
    if (isiPhone4) {
        scale = 1;
    }else if (isiPhone5){
        scale = 1;
    }else if (isiPhone6){
        scale = 1.1;
    }else if (isiPhone6P){
        scale = 1.1;
    }
    return scale;
}

+ (BOOL)isInOneMinutes:(NSDate *)date another:(NSDate *)date2 {

    if ([date isEarlierThan:date2]) {
        double minute = [date minutesEarlierThan:date2];
        if (minute <= 1) {
            return YES;
        }
    }else{
        double minute = [date minutesLaterThan:date2];
        if (minute <= 1) {
            return YES;
        }
    }
    return NO;
}

+ (NSString *)firstCharactor:(NSString *)aString
{
    //转成了可变字符串
    NSMutableString *str = [NSMutableString stringWithString:aString];
    //先转换为带声调的拼音
    CFStringTransform((CFMutableStringRef)str,NULL, kCFStringTransformMandarinLatin,NO);
    //再转换为不带声调的拼音
    CFStringTransform((CFMutableStringRef)str,NULL, kCFStringTransformStripDiacritics,NO);
    //转化为大写拼音
    NSString *pinYin = [str capitalizedString];
    //获取并返回首字母
    return [pinYin substringToIndex:1];
}

+ (BOOL)isStringContainEmoji:(NSString *)string {
    if (([self stringContainsEmoji:string] || [self hasEmoji:string]) && ![self isNineKeyBoard:string]) {
        return YES;
    }
    else {
        return NO;
    }
}

#pragma mark - 过滤表情符号
+ (BOOL)stringContainsEmoji:(NSString *)string
{
    // 过滤所有表情。returnValue为NO表示不含有表情，YES表示含有表情
    __block BOOL returnValue = NO;
    [string enumerateSubstringsInRange:NSMakeRange(0, [string length]) options:NSStringEnumerationByComposedCharacterSequences usingBlock:^(NSString *substring, NSRange substringRange, NSRange enclosingRange, BOOL *stop) {

        const unichar hs = [substring characterAtIndex:0];
        // surrogate pair
        if (0xd800 <= hs && hs <= 0xdbff) {
            if (substring.length > 1) {
                const unichar ls = [substring characterAtIndex:1];
                const int uc = ((hs - 0xd800) * 0x400) + (ls - 0xdc00) + 0x10000;
                if (0x1d000 <= uc && uc <= 0x1f77f) {
                    returnValue = YES;
                }
            }
        } else if (substring.length > 1) {
            const unichar ls = [substring characterAtIndex:1];
            if (ls == 0x20e3) {
                returnValue = YES;
            }
        } else {
            // non surrogate
            if (0x2100 <= hs && hs <= 0x27ff) {
                returnValue = YES;
            } else if (0x2B05 <= hs && hs <= 0x2b07) {
                returnValue = YES;
            } else if (0x2934 <= hs && hs <= 0x2935) {
                returnValue = YES;
            } else if (0x3297 <= hs && hs <= 0x3299) {
                returnValue = YES;
            } else if (hs == 0xa9 || hs == 0xae || hs == 0x303d || hs == 0x3030 || hs == 0x2b55 || hs == 0x2b1c || hs == 0x2b1b || hs == 0x2b50) {
                returnValue = YES;
            }
        }
    }];
    return returnValue;
}

+ (BOOL)hasEmoji:(NSString*)string;
{
    NSString *pattern = @"[^\\u0020-\\u007E\\u00A0-\\u00BE\\u2E80-\\uA4CF\\uF900-\\uFAFF\\uFE30-\\uFE4F\\uFF00-\\uFFEF\\u0080-\\u009F\\u2000-\\u201f\r\n]";
    NSPredicate *pred = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", pattern];
    BOOL isMatch = [pred evaluateWithObject:string];
    return isMatch;
}

+ (BOOL)isNineKeyBoard:(NSString *)string
{
    NSString *other = @"➋➌➍➎➏➐➑➒";
    int len = (int)string.length;
    for(int i=0;i<len;i++)
    {
        if(!([other rangeOfString:string].location != NSNotFound))
            return NO;
    }
    return YES;
}

+ (BOOL)isCurrentViewControllerVisible:(UIViewController *)viewController
{
    return (viewController.isViewLoaded && viewController.view.window);
}

+ (NSString *)getSmallImageUrlWithOriginalUrl:(NSString *)imageUrl {

    NSMutableArray *array = [NSMutableArray arrayWithArray:[imageUrl componentsSeparatedByString:@"."]];

    if (array.count < 2) {
        return imageUrl;
    }

    NSString *needsRePlaceString = [array objectAtIndex:array.count - 2];
    needsRePlaceString = [needsRePlaceString stringByAppendingString:@"_small"];
    [array replaceObjectAtIndex:array.count-2 withObject:needsRePlaceString];

    NSString *linkUrl = [array componentsJoinedByString:@"."];
    return linkUrl;
}

- (void)showPrivacyPopViewCompletion:(void (^)(BOOL))completion TapPrivacy:(void (^)(void))tapPrivacy tapUserAgreement:(void (^)(void))tapAgreement {

    CGFloat leftSpace = 40;
    CGFloat width = (kScreenWidth - leftSpace * 2);
    CGFloat height = 300;
    CGFloat currentY = (kScreenHeight - height) / 2.0;

    BRPrivacyPopView *customView = [[BRPrivacyPopView alloc] initWithFrame:CGRectMake(leftSpace, currentY, width, height)];

    LSTPopView *popView = [LSTPopView initWithCustomView:customView popStyle:LSTPopStyleScale dismissStyle:LSTDismissStyleFade];

    popView.hemStyle = LSTHemStyleCenter;
    popView.isClickBgDismiss = NO;

    self.popView = popView;

    [popView pop];

    __weak __typeof(self)weakSelf = self;
    customView.clickDisAgreeBlock = ^{
        [weakSelf.popView dismiss];
        exit(0);
    };

    customView.clickAgreeBlock = ^{
        [weakSelf.popView dismiss];

        [Config updateAgreePrivacy];
    };

    customView.tapPrivacyBlock = ^{
        [weakSelf.popView dismiss];

        if (tapPrivacy) {
            tapPrivacy();
        }
    };

    customView.tapUserProtocolBlock = ^{
        [weakSelf.popView dismiss];

        if (tapAgreement) {
            tapAgreement();
        }
    };

}

+ (BOOL)isInstalledWechat {
    return [WXApi isWXAppInstalled];
}

+ (BOOL)isInstalledQQ {
    return [[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:@"mqqapi://"]];
}

+ (void)clearFolderAtPath:(NSString *)folderPath {
    NSFileManager *fileManager = [NSFileManager defaultManager];

    if ([fileManager fileExistsAtPath:folderPath]) {
        NSArray *files = [fileManager contentsOfDirectoryAtPath:folderPath error:nil];

        for (NSString *file in files) {
            NSString *filePath = [folderPath stringByAppendingPathComponent:file];
            [fileManager removeItemAtPath:filePath error:nil];
        }
    } else {
        NSLog(@"The folder does not exist.");
    }
}

+ (BOOL)clearAllSessionsAndMessagesExceptForSystem {
    return [[IMDataBaseManager shareInstance] deleteALLIMSessionsAndIMMessagesExceptSystem];
}

#pragma mark - 保存用法用量信息

+ (void)saveUsageType:(NSString *)type text:(NSString *)allText withPatientId:(NSString *)patientId {
    NSString *userId = [UserManager shareInstance].getUserId;
    NSString *key = [NSString stringWithFormat:@"%@_UserId%@_PatientId%@",type,userId,patientId];
    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
    NSString *saveText = allText;
    if([saveText isEqualToString:@""]){
        saveText = @"-1";
    }
    [userDefault setValue:allText forKey:key];
    [userDefault synchronize];
}

+ (NSString *)getUsageType:(NSString *)type withPatientId:(NSString *)patientId{
    NSString *userId = [UserManager shareInstance].getUserId;

    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
    NSString *text = @"";
    NSString *key = [NSString stringWithFormat:@"%@_UserId%@_PatientId%@",type,userId,patientId];
    if([userDefault objectForKey:key]){
        text = [userDefault objectForKey:key];
    }

    if([text isEqualToString:@""]){
        text = @"-1";
    }

    return text;
}

//+ (void)saveUsageKandYTypeEveryDayAmount:(NSString *)amountText withPatientId:(NSString *)patientId {
//    NSString *userId = [UserManager shareInstance].getUserId;
//    NSString *key = [NSString stringWithFormat:@"%@_UserId%@_PatientId%@",kUsageKandYType_EveryDayAmount,userId,patientId];
//    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
//    NSString *saveText = amountText;
//    if([saveText isEqualToString:@""]){
//        saveText = @"-1";
//    }
//    [userDefault setValue:saveText forKey:key];
//    [userDefault synchronize];
//}
//
//+ (NSString *)getUsageKandYTypeEveryDayAmountWithPatientId:(NSString *)patientId {
//    NSString *userId = [UserManager shareInstance].getUserId;
//
//    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
//    NSString *text = @"";
//    NSString *key = [NSString stringWithFormat:@"%@_UserId%@_PatientId%@",kUsageKandYType_EveryDayAmount,userId,patientId];
//    if([userDefault objectForKey:key]){
//        text = [userDefault objectForKey:key];
//    }
//
//    if([text isEqualToString:@""]){
//        text = @"-1";
//    }
//    return text;
//}
//
//+ (void)saveUsageKandYTypeTimes:(NSString *)timeText withPatientId:(NSString *)patientId {
//
//    NSString *userId = [UserManager shareInstance].getUserId;
//    NSString *key = [NSString stringWithFormat:@"%@_UserId%@_PatientId%@",kUsageKandYType_times,userId,patientId];
//    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
//    NSString *saveText = timeText;
//    if([saveText isEqualToString:@""]){
//        saveText = @"-1";
//    }
//    [userDefault setValue:saveText forKey:key];
//    [userDefault synchronize];
//}
//
//+ (NSString *)getUsageKandYTypeTimesWithPatientId:(NSString *)patientId {
//    NSString *userId = [UserManager shareInstance].getUserId;
//
//    NSUserDefaults *userDefault = [NSUserDefaults standardUserDefaults];
//    NSString *text = @"";
//    NSString *key = [NSString stringWithFormat:@"%@_UserId%@_PatientId%@",kUsageKandYType_times,userId,patientId];
//    if([userDefault objectForKey:key]){
//        text = [userDefault objectForKey:key];
//    }
//
//    if([text isEqualToString:@""]){
//        text = @"-1";
//    }
//    return text;
//}

+ (BOOL)isChineseName:(NSString *)string {
    if (string.length <= 1) {
        return NO;
    }
    NSString *pattern = @"^[\\u4e00-\\u9fa5]{1,8}(·[\\u4e00-\\u9fa5]{1,8})?$";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", pattern];
    return [predicate evaluateWithObject:string];
}

+ (CGFloat)widthForString:(NSString *)string withFont:(UIFont *)font{
    NSDictionary *attributes = @{NSFontAttributeName: font};
    CGSize constraintSize = CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX);

    CGRect boundingRect = [string boundingRectWithSize:constraintSize
                                               options:NSStringDrawingUsesLineFragmentOrigin
                                            attributes:attributes
                                               context:nil];

    return ceil(boundingRect.size.width);
}


@end
