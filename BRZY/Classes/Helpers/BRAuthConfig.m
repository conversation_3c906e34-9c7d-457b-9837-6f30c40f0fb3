//
//  BRAuthConfig.m
//  BRZY
//
//  Created by <PERSON> on 2025/6/9.
//  Copyright © 2025年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRAuthConfig.h"

@implementation BRAuthConfig

+ (NSString *)getATAuthSDKAppKey {
    // 在这里配置您从阿里云控制台获取的App Key
    // 请替换下面的字符串为实际的App Key
    return @"YOUR_APP_KEY_HERE";
}

+ (BOOL)isATAuthSDKConfigured {
    NSString *appKey = [self getATAuthSDKAppKey];
    return ![appKey isEqualToString:@"YOUR_APP_KEY_HERE"] && appKey.length > 0;
}

@end