//
//  IMChatCollectionViewLayout.h
//  BRZY
//
//  Created by  xujiangta<PERSON> on 2017/9/11.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <UIKit/UIKit.h>

@protocol IMChatItemCellLayout;

NS_ASSUME_NONNULL_BEGIN

@protocol IMChatCollectionViewLayoutDelegate <UICollectionViewDelegate>

@required
- (NSArray<id<IMChatItemCellLayout>> *)cellLayouts;

@end

@interface IMChatCollectionViewLayout : UICollectionViewLayout

@property (nonatomic, assign) UIEdgeInsets inset;

- (instancetype)initWithInverted:(BOOL)inverted;

- (NSArray<UICollectionViewLayoutAttributes *> *)layoutAttributesForLayouts:(NSArray<id<IMChatItemCellLayout>> *)layouts containerWidth:(CGFloat)containerWidth maxHeight:(CGFloat)maxHeight contentHeight:(nullable CGFloat *)contentHeight;

- (BOOL)hasLayoutAttributes;

@end

NS_ASSUME_NONNULL_END
