//
//  IMClient.m
//  BRZY
//
//  Created by  xujiangtao on 2017/8/28.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMClient.h"
#import "IMDataBaseManager.h"
#import "IMSDKHelper.h"
#import "SocketManager.h"
#import "UserManager.h"

static IMClient *client = nil;

@interface IMClient ()
@property (strong, nonatomic) IMDataBaseManager *dataBaseManager;
@end

@implementation IMClient

+ (instancetype)shareInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        client = [[IMClient alloc] init];
    });
    return client;
}

- (void)initializationSDK {
    //初始化
    _sessionManager = [IMSessionManager shareInstance];
    _contactManager = [IMContactManager shareInstance];
    
    //创建存放数据库文件的目录
    [IMSDKHelper createFolderWithFolderPath:IMDataBasePath];
    
    //创建存放图片和音频文件的目录
    [IMSDKHelper createFolderWithFolderPath:[IMDocumentPath stringByAppendingPathComponent:IMImageFolder]];
    [IMSDKHelper createFolderWithFolderPath:[IMDocumentPath stringByAppendingPathComponent:IMVoiceFolder]];
    
    _dataBaseManager = [IMDataBaseManager shareInstance];
    
    //创建对应的数据表
    [_dataBaseManager createMessageTable];
    [_dataBaseManager createSessionTable];
    [_dataBaseManager createContactTable];
    //创建存放药典的数据表
    [_dataBaseManager createPharmacopeiaTable];
    //创建存未完成药方的数据表
    [_dataBaseManager createTemporaryPrescriptionTable];
    
    __weak IMSessionManager *weakSessionManager = _sessionManager;
    //添加socket代理关系
    [[SocketManager shareInstance] addDelegate:weakSessionManager];
    //设置之前为发送成功的消息为发送失败
    [_sessionManager updateAllDeliveringMessageToDeliveringFailed];
    
    //socket开始连接服务器
    [[SocketManager shareInstance] connectToHost];
    
}

- (void)startRequestReceiveMessage {
    NSMutableDictionary *dictionary = [NSMutableDictionary dictionary];
    [dictionary setObject:BRApiRequestReceiveMessage forKey:@"code"];
    [[SocketManager shareInstance] sendDataDict:dictionary];
}
@end
