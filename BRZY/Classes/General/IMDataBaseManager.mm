//
//  IMDataBaseManager.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/23.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMDataBaseManager.h"
#import <WCDB/WCDB.h>
#import "IMTableMessage.h"
#import "IMTableSession.h"
#import "IMTableContact.h"
#import "IMTableMessage+WCTTableCoding.h"
#import "IMTableContact+WCTTableCoding.h"
#import "IMTableSession+WCTTableCoding.h"

#import "BRTablePharmacopeia.h"
#import "BRTablePharmacopeia+WCTTableCoding.h"

#import "BRTemporaryPrescription.h"
#import "BRTemporaryPrescription+WCTTableCoding.h"
#import "BRPatientModel.h"

static IMDataBaseManager *dbManager = nil;

@interface IMDataBaseManager ()

@property (strong, nonatomic) WCTDatabase *database;

@end

@implementation IMDataBaseManager

#pragma mark - 初始化
+ (instancetype)shareInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        dbManager = [[IMDataBaseManager alloc] init];
    });
    return dbManager;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _database = [[WCTDatabase alloc] initWithPath:[IMDataBasePath stringByAppendingPathComponent:IMDataBaseName]];
        
//        [WCTStatistics SetGlobalSQLTrace:^(NSString *sql) {
//            NSLog(@"SQL:  %@",sql);
//        }];
//
//        [WCTStatistics SetGlobalErrorReport:^(WCTError *error) {
//            NSLog(@"[WCDB]%@", error);
//        }];
//
//        [WCTStatistics SetGlobalPerformanceTrace:^(WCTTag tag, NSDictionary<NSString *, NSNumber *> *sqls, NSInteger cost) {
//            NSLog(@"Database with tag:%d", tag);
//            NSLog(@"Run :");
//            [sqls enumerateKeysAndObjectsUsingBlock:^(NSString *sqls, NSNumber *count, BOOL *) {
//                NSLog(@"SQL %@ %@ times", sqls, count);
//            }];
//            NSLog(@"Total cost %ld nanoseconds", (long)cost);
//        }];
    }
    return self;
}

#pragma mark - 创建数据表
#pragma mark 创建联系人表
- (void)createContactTable {
    // 修改为使用新的 WCDB API：使用 createTable:withClass: 替代 createTableAndIndexesOfName:withClass:
    BOOL result = [self.database createTable:IM_Table_Contact withClass:IMTableContact.class];
    if (result) {
        BRLog(@"create contact table successfule");
    }else{
        BRLog(@"create contact table failed");
    }
}
#pragma mark 创建消息表
- (void)createMessageTable {
    // 修改为使用新的 WCDB API：使用 createTable:withClass: 替代 createTableAndIndexesOfName:withClass:
    BOOL result = [self.database createTable:IM_Table_Message withClass:IMTableMessage.class];
    if (result) {
        BRLog(@"create message table successful");
    }else{
        BRLog(@"create message table failed");
    }
}
#pragma mark 创建会话表
- (void)createSessionTable {
    // 修改为使用新的 WCDB API：使用 createTable:withClass: 替代 createTableAndIndexesOfName:withClass:
    BOOL result = [self.database createTable:IM_Table_Session withClass:IMTableSession.class];
    
    if (result) {
        BRLog(@"create session table successful");
    }else{
        BRLog(@"create session table failed");
    }
}

#pragma mark 创建药典数据表
- (void)createPharmacopeiaTable {
    // 修改为使用新的 WCDB API：使用 createTable:withClass: 替代 createTableAndIndexesOfName:withClass:
    BOOL rel = [self.database createTable:BR_Table_Pharmacopie withClass:BRTablePharmacopeia.class];
    if (rel) {
        BRLog(@"create pharmacopeia table successsfule");
    }else{
        BRLog(@"create pharmacopeia table failed");
    }
}

#pragma mark 创建临时药方表
- (void)createTemporaryPrescriptionTable {
    // 修改为使用新的 WCDB API：使用 createTable:withClass: 替代 createTableAndIndexesOfName:withClass:
    BOOL result = [_database createTable:BR_Table_Prescription withClass:BRTemporaryPrescription.class];
    if (result) {
        BRLog(@"create prescription table successfule");
    }
    else {
        BRLog(@"create prescription table failed");
    }
}

#pragma mark - 消息表操作
#pragma mark 保存消息
//保存消息
- (BOOL)saveIMTableMessage:(IMTableMessage *)aTableMessage {
    aTableMessage.isAutoIncrement = YES;
    
    // 使用 prepareInsert 方法替代 insertObject:into:
    BOOL rel = [[[[self.database prepareInsert] intoTable:IM_Table_Message] value:aTableMessage] execute];
    if (rel) {
        
        //不显示加好友患者发送的回话  并把消息设置为已读
        if ([[Config shareInstance].app_colseWelcome isEqualToString:@"1"] && ([aTableMessage.content hasSuffix:@"我刚在必然中医关注了您。"] || [aTableMessage.content hasSuffix:@"添加您为好友"])) {
            [self updateOneIMTableMessageLocalReadToReaded:aTableMessage.messageId];
        }
        else {
            //更新最后一条消息到对应会话表中
            [self updateLastMessageToSessionTable:aTableMessage];
        }
        
        //更新显示总未读消息数
        [self sendNotifi_updateSumUnReadCount];
    }
    
    return rel;
}
#pragma mark - 查询消息
//根据消息id查询单条消息
- (IMTableMessage *)getOneMessageByMessageId:(NSString *)aMessageId {
    // 修改为使用新的 WCDB API：使用 getObjectOfClass 替代 getOneObjectOfClass
    IMTableMessage *tableMessage = [_database getObjectOfClass:IMTableMessage.class
                               fromTable:IM_Table_Message 
                                   where:IMTableMessage.messageId == aMessageId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    return tableMessage;
}

- (IMTableMessage *)getOneMessageByFromId:(NSString *)aFromId {
    // 修改为使用新的 WCDB API：使用 getObjectOfClass 替代 getOneObjectOfClass
    IMTableMessage *tableMessage = [_database getObjectOfClass:IMTableMessage.class 
                               fromTable:IM_Table_Message 
                                   where:IMTableMessage.fromId == aFromId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    return tableMessage;
}

- (IMTableMessage *)getLastOneMessageBySessionId:(NSString *)aSessionId {
//    IMTableMessage *tableMessage = [_database getOneObjectOnResults:{IMTableMessage.serverTime.max(),IMTableMessage.serverTime,IMTableMessage.createTime,IMTableMessage.messageId,IMTableMessage.contentType,IMTableMessage.content,IMTableMessage.status} fromTable:IM_Table_Message where:IMTableMessage.sessionId == aSessionId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    
    // 修改为使用新的 WCDB API：使用 getObjectOfClass 替代 getOneObjectOfClass
    // 使用 orderBy 并传入 WCTOrderedDescending
    IMTableMessage *tableMessage = [_database getObjectOfClass:IMTableMessage.class 
                               fromTable:IM_Table_Message 
                                   where:IMTableMessage.sessionId == aSessionId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId && IMTableMessage.messageType == IMMessageTypeChat 
                                  orders:{IMTableMessage.serverTime.asOrder(WCTOrderedDescending),IMTableMessage.serverTime.asOrder(WCTOrderedDescending)}];
    
    return tableMessage;
}
//是否包含某条消息
- (BOOL)isContainMessageWithMessageId:(NSString *)aMessageId {
    // 修改为使用新的 WCDB API：使用 getObjectOfClass 替代 getOneObjectOfClass
    IMTableMessage *tableMessage = [_database getObjectOfClass:IMTableMessage.class 
                               fromTable:IM_Table_Message 
                                   where:IMTableMessage.messageId == aMessageId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    if (tableMessage) {
        return YES;
    }else{
        return NO;
    }
}

- (NSArray *)getPreIMTableMessagesWithSessionId:(NSString *)aSessionId serverTime:(NSDate *)serverTime limit:(NSInteger)limit {
    NSMutableArray *messages = [NSMutableArray arrayWithCapacity:0];
    if (serverTime) {
        // 修改为使用新的 WCDB API：使用 getObjectsOfClass 替代 getObjectsOnResultColumns
        // 并使用 asOrder 替代 order
        // 使用 [IMTableMessage allProperties] 替代 IMTableMessage.AllProperties
        NSArray *newArray = [_database getObjectsOfClass:IMTableMessage.class 
                                             fromTable:IM_Table_Message 
                                                 where:IMTableMessage.sessionId == aSessionId && IMTableMessage.serverTime < serverTime && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId && (IMTableMessage.messageType == IMMessageTypeChat || IMTableMessage.messageType == IMMessageTypeSystem)  
                                                orders:{IMTableMessage.serverTime.asOrder(WCTOrderedDescending),IMTableMessage.serverMessageId.asOrder(WCTOrderedDescending)} 
                                                 limit:limit];
        [messages addObjectsFromArray:newArray];
    }
    else {
        NSArray *newArray = [_database getObjectsOfClass:IMTableMessage.class 
                                             fromTable:IM_Table_Message 
                                                 where:IMTableMessage.sessionId == aSessionId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId && (IMTableMessage.messageType == IMMessageTypeChat || IMTableMessage.messageType == IMMessageTypeSystem) 
                                                orders:{IMTableMessage.serverTime.asOrder(WCTOrderedDescending),IMTableMessage.serverMessageId.asOrder(WCTOrderedDescending)} 
                                                 limit:limit];
        [messages addObjectsFromArray:newArray];
    }
    
    return messages;
}

- (NSInteger)getLocalUnReadMessageCountWithSessionId:(NSString *)aSessionId {
    // 修改为使用新的 WCDB API：使用 getValueOnResultColumn 替代 getOneValueOnResult
    NSNumber *result = [_database getValueOnResultColumn:IMTableMessage.isLocalRead.count() 
                                              fromTable:IM_Table_Message 
                                                  where:IMTableMessage.sessionId == aSessionId && IMTableMessage.from != [UserManager shareInstance].getUserId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId && IMTableMessage.isLocalRead == NO && (IMTableMessage.messageType == IMMessageTypeChat || IMTableMessage.messageType == IMMessageTypeSystem)];
    return [result integerValue];
}

- (NSInteger)getAllLocalUnReadMessageCount {
    // 修改为使用新的 WCDB API：使用 getValueOnResultColumn 替代 getOneValueOnResult
    NSNumber *result = [_database getValueOnResultColumn:IMTableMessage.isLocalRead.count() 
                                              fromTable:IM_Table_Message 
                                                  where:IMTableMessage.loginUserId == [UserManager shareInstance].getUserId && IMTableMessage.from != [UserManager shareInstance].getUserId && IMTableMessage.isLocalRead == NO && (IMTableMessage.messageType == IMMessageTypeChat || IMTableMessage.messageType == IMMessageTypeSystem) && (IMTableMessage.serverTime >= [[Config currentServerDate] dateBySubtractingDays:kMessageListShowDateDays])];
    return [result integerValue];
}
#pragma mark 更新消息
- (BOOL)updateIMTableMessageServerTime:(NSDate *)aSerVerTime status:(IMMessageStatus)aStatus serverMessageId:(NSString *)aServerMessageId messageId:(NSString *)aMessageId {
    IMTableMessage *tableMessage = [[IMTableMessage alloc] init];
    tableMessage.serverTime = aSerVerTime;
    tableMessage.status = aStatus;
    tableMessage.serverMessageId = aServerMessageId;
    
    // 修改为使用新的 WCDB API：使用 updateTable:setProperties:toObject:where: 替代 updateRowsInTable:onProperties:withObject:where:
    BOOL rel = [_database updateTable:IM_Table_Message 
                       setProperties:{IMTableMessage.serverTime,IMTableMessage.status,IMTableMessage.serverMessageId} 
                            toObject:tableMessage 
                               where:IMTableMessage.messageId == aMessageId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    
    if (rel) {
        //更新会话中最后一条消息的状态
        [self updateLastMessageStatusToSessionTableWithMessageId:aMessageId];
    }
    
    return rel;
}

- (BOOL)updateIMTableMessageWithFromId:(NSString *)aFromId status:(IMMessageStatus)aStatus {
    // 修改为使用新的 WCDB API：使用 updateTable:setProperty:toValue:where: 替代 updateRowsInTable:onProperty:withValue:where:
    BOOL rel = [_database updateTable:IM_Table_Message 
                        setProperty:IMTableMessage.status 
                            toValue:@(aStatus) 
                              where:IMTableMessage.fromId == aFromId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    
    if (rel) {
        //更新会话中最后一条消息的状态
        [self updateLastMessageStatusToSessionTableWithMessageId:aFromId];
    }
    
    return rel;
}

- (BOOL)updateIMTableMessageFileLocalPathWithPath:(NSString *)aFilePath length:(NSInteger)length messageId:(NSString *)aMessageId {
    IMTableMessage *tableMessage = [[IMTableMessage alloc] init];
    tableMessage.fileLocalPath = aFilePath;
    tableMessage.voiceLength = length;
    
    // 修改为使用新的 WCDB API：使用 updateTable:setProperties:toObject:where: 替代 updateRowsInTable:onProperties:withObject:where:
    BOOL rel = [_database updateTable:IM_Table_Message 
                       setProperties:{IMTableMessage.fileLocalPath,IMTableMessage.voiceLength} 
                            toObject:tableMessage 
                               where:IMTableMessage.messageId == aMessageId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    return rel;
}

- (BOOL)updateIMTableMessage:(IMTableMessage *)tableMessage {
    // 使用 prepareUpdate 方法进行链式调用
    BOOL rel = [[[[[[self.database prepareUpdate] table:IM_Table_Message] set:[IMTableMessage allProperties]] toObject:tableMessage] where:IMTableMessage.messageId == tableMessage.messageId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId] execute];
    if (rel) {
        //更新会话中最后一条消息状态
        [self updateLastMessageStatusToSessionTableWithMessageId:tableMessage.messageId];
    }
    return rel;
}

- (BOOL)updateIMTableMessageContentType:(IMContentType)aContentType content:(NSString *)aContent byFromId:(NSString *)aFromId {
    IMTableMessage *tableMessage = [[IMTableMessage alloc] init];
    tableMessage.content = aContent;
    tableMessage.contentType = aContentType;
    
    // 修改为使用新的 WCDB API：使用 updateTable:setProperties:toObject:where: 替代 updateRowsInTable:onProperties:withObject:where:
    BOOL rel = [_database updateTable:IM_Table_Message 
                       setProperties:{IMTableMessage.contentType,IMTableMessage.content} 
                            toObject:tableMessage 
                               where:IMTableMessage.fromId == aFromId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    //更新会话中最后一条消息状态
    if (rel) {
        [self updateLastMessageStatusToSessionTableWithMessageId:aFromId];
    }
    return rel;
}

- (BOOL)updateSessionIMTableMessageLocalReadToReadedWithSessionId:(NSString *)aSessionId {
    //更新某个会话对应的所有接收到的消息为已读   不包含Notice类型消息
    // 修改为使用新的 WCDB API：使用 updateTable:setProperty:toValue:where: 替代 updateRowsInTable:onProperty:withValue:where:
    BOOL rel = [_database updateTable:IM_Table_Message 
                        setProperty:IMTableMessage.isLocalRead 
                            toValue:[NSNumber numberWithBool:YES] 
                              where:IMTableMessage.sessionId == aSessionId && IMTableMessage.from != [UserManager shareInstance].getUserId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId && IMTableMessage.messageType != IMMessageTypeNotice];
    if (rel) {
        //更新消息列表
        [self sendNotifi_updateSessionListFromDataBase];
        //更新总的未读数
        [self sendNotifi_updateSumUnReadCount];
    }
    return rel;
}

- (BOOL)updateOneIMTableMessageLocalReadToReaded:(NSString *)aMessageId {
    //更新指定消息id的某条消息为已读
    // 修改为使用新的 WCDB API：使用 updateTable:setProperty:toValue:where: 替代 updateRowsInTable:onProperty:withValue:where:
    BOOL rel = [_database updateTable:IM_Table_Message 
                        setProperty:IMTableMessage.isLocalRead 
                            toValue:[NSNumber numberWithBool:YES] 
                              where:IMTableMessage.messageId == aMessageId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    if (rel) {
        [self sendNotifi_updateSumUnReadCount];
    }
    return rel;
}

- (BOOL)updateAllIMTableMessageSendDeliveringToSendFailed {
    // 修改为使用新的 WCDB API：使用 updateTable:setProperty:toValue:where: 替代 updateRowsInTable:onProperty:withValue:where:
    BOOL rel = [_database updateTable:IM_Table_Message 
                        setProperty:IMTableMessage.status 
                            toValue:@(IMMessageStatusFailure) 
                              where:IMTableMessage.loginUserId == [UserManager shareInstance].getUserId && IMTableMessage.from == [UserManager shareInstance].getUserId && IMTableMessage.status == IMMessageStatusDelivering];
    return rel;
}

- (BOOL)updateIMTableMessageRemotePathWithMessageId:(NSString *)aMessageId remotePath:(NSString *)aRemotePath {
    // 修改为使用新的 WCDB API：使用 updateTable:setProperty:toValue:where: 替代 updateRowsInTable:onProperty:withValue:where:
    BOOL rel = [_database updateTable:IM_Table_Message 
                        setProperty:IMTableMessage.fileRemotePath 
                            toValue:aRemotePath 
                              where:IMTableMessage.loginUserId == [UserManager shareInstance].getUserId && IMTableMessage.messageId == aMessageId];
    
    return rel;
}
#pragma mark 删除消息
- (BOOL)deleteAllIMTableMessageWithSessionId:(NSString *)aSessionId {
    // 修改为使用新的 WCDB API：使用 deleteFromTable:where: 替代 deleteObjectsFromTable:where:
    BOOL rel = [_database deleteFromTable:IM_Table_Message 
                                    where:IMTableMessage.sessionId == aSessionId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    return rel;
}

- (BOOL)deleteAllIMTableMessageWithSessionIdsArray:(NSArray *)aSessionIdsArray {
    // 修改为使用新的 WCDB API：使用 deleteFromTable:where: 替代 deleteObjectsFromTable:where:
    BOOL rel = [_database deleteFromTable:IM_Table_Message 
                                    where:IMTableMessage.loginUserId == [UserManager shareInstance].getUserId && IMTableMessage.sessionId.in(aSessionIdsArray)];
    return rel;
}

- (BOOL)deleteIMTableMessageWithMessageId:(NSString *)aMessageId {
    // 修改为使用新的 WCDB API：使用 deleteFromTable:where: 替代 deleteObjectsFromTable:where:
    BOOL rel = [_database deleteFromTable:IM_Table_Message 
                                    where:IMTableMessage.messageId == aMessageId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    return rel;
}

- (BOOL)updateAllUnReadMessageToReaded {
    // 修改为使用新的 WCDB API：使用 updateTable:setProperty:toValue:where: 替代 updateRowsInTable:onProperty:withValue:where:
    BOOL rel = [_database updateTable:IM_Table_Message 
                        setProperty:IMTableMessage.isLocalRead 
                            toValue:[NSNumber numberWithBool:YES] 
                              where:IMTableMessage.loginUserId == [UserManager shareInstance].getUserId && IMTableMessage.from != [UserManager shareInstance].getUserId && IMTableMessage.messageType != IMMessageTypeNotice];
    if (rel) {
        //更新消息列表
        [self sendNotifi_updateSessionListFromDataBase];
        //更新总的未读数
        [self sendNotifi_updateSumUnReadCount];
    };
    
    return rel;
}

- (BOOL)deleteAllIMMessages {
    // 修改为使用新的 WCDB API：使用 deleteFromTable: 替代 deleteAllObjectsFromTable:
    return [_database deleteFromTable:IM_Table_Message];
}

#pragma mark - 联系人表操作
- (BOOL)saveIMTableContact:(IMTableContact *)aTableContact {
    // 使用 prepareInsert 方法替代 insertObject:into:
    return [[[[self.database prepareInsert] intoTable:IM_Table_Contact] value:aTableContact] execute];
}
- (BOOL)isContainContactWithRosterUserId:(NSString *)aRosterUserId {
    // 修改为使用新的 WCDB API：使用 getObjectOfClass 替代 getOneObjectOfClass
    IMTableContact *tableContact = [_database getObjectOfClass:IMTableContact.class 
                               fromTable:IM_Table_Contact 
                                   where:IMTableContact.rosterUserId == aRosterUserId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    if (tableContact) {
        return YES;
    }else{
        return NO;
    }
}

- (BOOL)updateIMTableContact:(IMTableContact *)aTableContact {
    // 修改为使用新的 WCDB API：使用 updateTable:setProperties:toObject:where: 替代 updateRowsInTable:onProperties:withObject:where:
    // 使用 [IMTableContact allProperties] 替代 IMTableContact.AllProperties
    BOOL rel = [_database updateTable:IM_Table_Contact 
                       setProperties:[IMTableContact allProperties]
                            toObject:aTableContact 
                               where:IMTableContact.rosterUserId == aTableContact.rosterUserId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
    
    ////在IMContactManager中更新完后近刷新  否则没添加一个联系人就需要刷新一次界面
//    if (rel) {
//        //保存成功 更新联系人列表ui
//        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateContactListFromDataBase object:nil];
//        //更新会话列表 主要刷新头像
//        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateSessionListFromDataBase object:nil];
//
//    }
    return rel;
}

- (NSArray *)getIMContactFirstCharactors {
   // 修改为使用带有 DISTINCT 关键字的查询
   // 需要构建 StatementSelect 并使用 distinct() 方法
   WCDB::StatementSelect select = WCDB::StatementSelect().select(IMTableContact.firstSpell).distinct().from(IM_Table_Contact).where(IMTableContact.rosterUserId != 1000 && IMTableContact.loginUserId == [UserManager shareInstance].getUserId && (IMTableContact.updateTime >= [[Config currentServerDate] dateBySubtractingDays:kContactListShowDateDays])).order(IMTableContact.firstSpell.asOrder());
   
   return [_database getColumnFromStatement:select];
}

- (NSArray *)getIMContactObjectByFirstCharactor:(NSString *)firstCharactor {
    // 此方法的 API 已经符合最新标准，不需要修改
    return [_database getObjectsOfClass:IMTableContact.class 
                             fromTable:IM_Table_Contact 
                                 where:IMTableContact.firstSpell == firstCharactor && IMTableContact.loginUserId == [UserManager shareInstance].getUserId && (IMTableContact.updateTime >= [[Config currentServerDate] dateBySubtractingDays:kContactListShowDateDays])];
}

- (NSString *)getIMContactHeadImgUrlByRosterUserId:(NSString *)aRosterUserId {
    // 修改为使用新的 WCDB API：使用 getValueOnResultColumn 替代 getOneValueOnResult
    NSString *headImgUrl = [_database getValueOnResultColumn:IMTableContact.headImgUrl 
                                                 fromTable:IM_Table_Contact 
                                                     where:IMTableContact.loginUserId == [UserManager shareInstance].getUserId && IMTableContact.rosterUserId == aRosterUserId];
    if (!headImgUrl) {
        headImgUrl = @"";
    }
    return headImgUrl;
}

- (IMTableContact *)getIMContactByRosterUserId:(NSString *)aRosterUserId {
    // 修改为使用新的 WCDB API：使用 getObjectOfClass 替代 getOneObjectOfClass
    return [_database getObjectOfClass:IMTableContact.class 
                           fromTable:IM_Table_Contact 
                               where:IMTableContact.rosterUserId == aRosterUserId && IMTableContact.loginUserId == [UserManager shareInstance].getUserId];
}

- (BOOL)deleteIMContactBlackListByRosterUserIdArray:(NSArray *)blacklistidsArray {
    // 修改为使用新的 WCDB API：使用 deleteFromTable:where: 替代 deleteObjectsFromTable:where:
    BOOL rel = [_database deleteFromTable:IM_Table_Contact 
                                    where:IMTableContact.rosterUserId.in(blacklistidsArray) && IMTableContact.loginUserId == [UserManager shareInstance].getUserId];
    if (rel) {
        //删除黑名单联系人成功  更新用户列表
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateContactListFromDataBase object:nil];
    }
    return rel;
}

- (BOOL)updateIMTableMessageTagWithRosterUserId:(NSString *)aRosterUserId tagname:(NSString *)tagname {
    // 修改为使用新的 WCDB API：使用 updateTable:setProperty:toValue:where: 替代 updateRowsInTable:onProperty:withValue:where:
    BOOL rel = [_database updateTable:IM_Table_Contact 
                        setProperty:IMTableContact.tag1 
                            toValue:tagname 
                              where:IMTableContact.loginUserId == [UserManager shareInstance].getUserId && IMTableContact.rosterUserId == aRosterUserId];
    if (rel) {
        //更新联系人列表
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateContactListFromDataBase object:nil];
    }
    return rel;
}

- (BOOL)updateIMTableContactRemarkWithRosterUserId:(NSString *)aRosterUserid remark:(NSString *)remark
{
    // 修改为使用新的 WCDB API：使用 updateTable:setProperty:toValue:where: 替代 updateRowsInTable:onProperty:withValue:where:
    BOOL rel = [_database updateTable:IM_Table_Contact 
                        setProperty:IMTableContact.remark 
                            toValue:remark 
                              where:IMTableContact.loginUserId == [UserManager shareInstance].getUserId && IMTableContact.rosterUserId == aRosterUserid];
    
    if (rel) {
        //
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateContactListFromRemoteServer object:nil];
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateSessionListFromDataBase object:nil];
    }
    return rel;
}

- (BOOL)updateIMTableContactTagsWithRosterUserId:(NSString *)aRosterUserId tags:(NSArray *)tags
{
    // 修改为使用新的 WCDB API：使用 updateTable:setProperty:toValue:where: 替代 updateRowsInTable:onProperty:withValue:where:
    BOOL rel = [_database updateTable:IM_Table_Contact 
                        setProperty:IMTableContact.tags 
                            toValue:tags 
                              where:IMTableContact.loginUserId == [UserManager shareInstance].getUserId && IMTableContact.rosterUserId == aRosterUserId];
    if (rel) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateContactListFromDataBase object:nil];
    }
    return rel;
}

#pragma mark - 会话操作

#pragma mark 保存会话
- (BOOL)saveIMTableSession:(IMTableSession *)aTableSession {
    // 使用 prepareInsert 方法替代 insertObject:into:
    BOOL rel = [[[[self.database prepareInsert] intoTable:IM_Table_Session] value:aTableSession] execute];
    if (rel) {
        //更新会话列表
        [self sendNotifi_updateSessionListFromDataBase];
    }
    return rel;
}

- (BOOL)updateIMTableSession:(IMTableSession *)aTableSession {
    // 使用 prepareUpdate 方法进行链式调用
    BOOL rel = [[[[[[self.database prepareUpdate] table:IM_Table_Session] set:{IMTableSession.sessionId,IMTableSession.name,IMTableSession.username,IMTableSession.telephone,IMTableSession.status,IMTableSession.userType,IMTableSession.thirdType,IMTableSession.messageId,IMTableSession.messageUpdateTime,IMTableSession.contentType,IMTableSession.content,IMTableSession.messageStatus,IMTableSession.chatStatusType}] toObject:aTableSession] where:IMTableSession.loginUserId == [UserManager shareInstance].getUserId && IMTableSession.sessionId == aTableSession.sessionId] execute];
    return rel;
}

#pragma mark 获取会话
- (NSArray *)getAllSessions {
    // 修改为使用 asOrder 而不是 order
    NSArray *array = [_database getObjectsOfClass:IMTableSession.class 
                                       fromTable:IM_Table_Session 
                                           where:IMTableSession.loginUserId == [UserManager shareInstance].getUserId && IMTableSession.messageId != NULL && (IMTableSession.messageUpdateTime >= [[Config currentServerDate] dateBySubtractingDays:kMessageListShowDateDays]) 
                                          orders:IMTableSession.messageUpdateTime.asOrder(WCTOrderedDescending)];
    return array;
}

//获取指定的一个会话
- (IMTableSession *)getOneTableSessionWithSessionId:(NSString *)aSessoinId {
    // 修改为使用新的 WCDB API：使用 getObjectOfClass 替代 getOneObjectOfClass
    IMTableSession *tableSession = [_database getObjectOfClass:IMTableSession.class 
                               fromTable:IM_Table_Session 
                                   where:IMTableSession.sessionId == aSessoinId && IMTableSession.loginUserId == [UserManager shareInstance].getUserId];
    return tableSession;
}

//是否存在指定的会话id的会话
- (BOOL)isContainSessionWithSessionId:(NSString *)aSessionId {
    // 修改为使用新的 WCDB API：使用 getObjectOfClass 替代 getOneObjectOfClass
    IMTableSession *session = [_database getObjectOfClass:IMTableSession.class 
                           fromTable:IM_Table_Session 
                               where:IMTableSession.loginUserId == [UserManager shareInstance].getUserId && IMTableSession.sessionId == aSessionId];
    if (session) {
        return YES;
    }else{
        return NO;
    }
}

//根据关键字搜索会话
- (NSArray *)getSessionByMatchName:(NSString *)aName {

    //匹配消息表
    NSArray *idsOneArray = [self getSessionIdByMatchMessageInfoKeyworkds:aName];
    //匹配联系人信息
    NSArray *idsTwoArray = [self getSessionIdByMatchContactInfoWithKeywords:aName];
    
    //去重
    NSMutableSet *set = [NSMutableSet setWithArray:idsOneArray];
    [set addObjectsFromArray:idsTwoArray];
    
    NSArray *mergedArray = [set allObjects];

    // 此方法的 API 已经符合最新标准，不需要修改
    NSArray *array = [_database getObjectsOfClass:IMTableSession.class 
                                       fromTable:IM_Table_Session 
                                           where:IMTableSession.loginUserId == [UserManager shareInstance].getUserId && IMTableSession.sessionId.in(mergedArray) && (IMTableSession.messageUpdateTime >= [[Config currentServerDate] dateBySubtractingDays:kMessageListShowDateDays])];
    return array;
}

//根据状态搜索会话
- (NSArray *)getSessionByMatchChatStatus:(NSInteger)chatStatus {
    // 此方法的 API 已经符合最新标准，不需要修改
    NSArray *array = [_database getObjectsOfClass:IMTableSession.class 
                                       fromTable:IM_Table_Session 
                                           where:IMTableSession.loginUserId == [UserManager shareInstance].getUserId && IMTableSession.chatStatusType == chatStatus && (IMTableSession.messageUpdateTime >= [[Config currentServerDate] dateBySubtractingDays:kMessageListShowDateDays])];
    return array;
}

//根据关键字和状态搜索会话
- (NSArray *)getSessionByMathName:(NSString *)aName chatStatus:(NSInteger)chatStatus {
    
    //匹配消息表
    NSArray *idsOneArray = [self getSessionIdByMatchMessageInfoKeyworkds:aName];
    //匹配联系人信息
    NSArray *idsTwoArray = [self getSessionIdByMatchContactInfoWithKeywords:aName];
    
    //去重
    NSMutableSet *set = [NSMutableSet setWithArray:idsOneArray];
    [set addObjectsFromArray:idsTwoArray];
    
    NSArray *mergedArray = [set allObjects];
    
    // 此方法的 API 已经符合最新标准，不需要修改
    NSArray *array = [_database getObjectsOfClass:IMTableSession.class 
                                       fromTable:IM_Table_Session 
                                           where:IMTableSession.loginUserId == [UserManager shareInstance].getUserId && IMTableSession.chatStatusType == chatStatus && IMTableSession.sessionId.in(mergedArray) && (IMTableSession.messageUpdateTime >= [[Config currentServerDate] dateBySubtractingDays:kMessageListShowDateDays])];
    return array;
}

//匹配消息表信息
- (NSArray *)getSessionIdByMatchMessageInfoKeyworkds:(NSString *)keyword {
    // 修改为使用带有 DISTINCT 关键字的查询
    // 需要构建 StatementSelect 并使用 distinct() 方法
    WCDB::StatementSelect select = WCDB::StatementSelect().select(IMTableMessage.sessionId).distinct().from(IM_Table_Message).where(IMTableMessage.sessionId != 1000 && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId && IMTableMessage.content.like([NSString stringWithFormat:@"%%%@%%",[keyword stringByTrim]]));
    
    return [_database getColumnFromStatement:select];
}

//匹配联系人信息
- (NSArray *)getSessionIdByMatchContactInfoWithKeywords:(NSString *)keyword {
    // 修改为使用带有 DISTINCT 关键字的查询
    // 需要构建 StatementSelect 并使用 distinct() 方法
    WCDB::StatementSelect select = WCDB::StatementSelect().select(IMTableContact.rosterUserId).distinct().from(IM_Table_Contact).where(IMTableContact.rosterUserId != 1000 && IMTableContact.loginUserId == [UserManager shareInstance].getUserId && (IMTableContact.nickName.like([NSString stringWithFormat:@"%%%@%%",keyword]) || IMTableContact.remark.like([NSString stringWithFormat:@"%%%@%%",keyword]) || IMTableContact.tag1.like([NSString stringWithFormat:@"%%%@%%",keyword]) || IMTableContact.tag2.like([NSString stringWithFormat:@"%%%@%%",keyword])));
    
    return [_database getColumnFromStatement:select];
}

//获取会话中最后一条消息的时间
- (NSDate *)getIMSessionLastMessageTimeDateBySessionId:(NSString *)aSessionId {
    // 修改为使用新的 WCDB API：使用 getObjectOfClass 替代 getOneObjectOfClass
    IMTableSession *tableSession = [_database getObjectOfClass:IMTableSession.class 
                               fromTable:IM_Table_Session 
                                   where:IMTableSession.sessionId == aSessionId && IMTableSession.loginUserId == [UserManager shareInstance].getUserId];
    
    return tableSession.messageUpdateTime;
}

//删除黑名单会话
- (BOOL)deleteIMSessionBlackListBySessionIdArray:(NSArray *)blacklistidsArray {
    // 修改为使用新的 WCDB API：使用 deleteFromTable:where: 替代 deleteObjectsFromTable:where:
    BOOL rel = [_database deleteFromTable:IM_Table_Session 
                                    where:IMTableSession.sessionId.in(blacklistidsArray) && IMTableSession.loginUserId == [UserManager shareInstance].getUserId];
    
    //删除黑名单会话对应的消息
    [self deleteAllIMTableMessageWithSessionIdsArray:blacklistidsArray];
    
//    if (delMsgRel) {
//        NSLog(@"删除黑名单对应消息成功");
//    }
//    else {
//        NSLog(@"删除黑名单对应消息失败");
//    }
    
    if (rel) {
        //更新会话列表
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateSessionListFromDataBase object:nil];
        //更新总未读消息条数
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateSumUnReadCount object:nil];
    }
    return rel;
}

//删除指定sessionId的会话
- (BOOL)deleteIMSessionBySessionId:(NSString *)aSessionId {
    // 修改为使用新的 WCDB API：使用 deleteFromTable:where: 替代 deleteObjectsFromTable:where:
    BOOL rel = [_database deleteFromTable:IM_Table_Session 
                                    where:IMTableSession.sessionId == aSessionId && IMTableSession.loginUserId == [UserManager shareInstance].getUserId];
    if (rel) {
        //更新会话列表
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateSessionListFromDataBase object:nil];
    }
    return rel;
}
#pragma mark - 更新最后一条消息到会话表中
- (void)updateLastMessageToSessionTable:(IMTableMessage *)tableMessage {
    dispatch_queue_t globleQueue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
    dispatch_async(globleQueue, ^{
        
        //如果消息类型不是CHAT 则不进行处理
        if ((tableMessage.messageType != IMMessageTypeChat)  && (tableMessage.messageType != IMMessageTypeSystem)) {
            return;
        }
        
        IMTableSession *tableSession = [[IMTableSession alloc] init];
        tableSession.messageId = tableMessage.messageId;
        tableSession.contentType = tableMessage.contentType;
        tableSession.content = tableMessage.content;
        tableSession.messageStatus = tableMessage.status;
        
        tableSession.sessionId = tableMessage.sessionId;
        
        tableSession.messageUpdateTime = tableMessage.serverTime;
        
//        NSLog(@"tablemessage content = %@",tableMessage.content);
        
        
        ChatStatusType chatStatusType = ChatStatusTypeNone;
        
        //如果为问诊单 则更新chatStatus  或者未系统消息状态
        if (tableMessage.contentType == IMContentTypeWZDQuestion) {
            chatStatusType = ChatStatusTypeWzdSended;
        }
        else if (tableMessage.contentType == IMContentTypeWZDAnswer) {
            chatStatusType = ChatStatusTypeWzdFilled;
        }
        else if (tableMessage.contentType == IMContentTypeFZDQuestion) {
            chatStatusType = ChatStatusTypeFzdSended;
        }
        else if (tableMessage.contentType == IMContentTypeFZDAnswer) {
            chatStatusType = ChatStatusTypeFzdFilled;
        }
        //未支付  已支付  已发货
        if (tableMessage.contentType == IMContentTypeSystem) {
            if ((tableMessage.openType == IMMessageOpenTypeOrder) && [tableMessage.content containsString:@"等待对方支付"]) {
                chatStatusType = ChatStatusTypeOrderNoPay;
            }
            //已支付
            else if ((tableMessage.openType == IMMessageOpenTypeOrder) && [tableMessage.content containsString:@"已支付"]) {
                chatStatusType = ChatStatusTypeOrderPayed;
            }
            //已发货
            else if (tableMessage.openType == IMMessageOpenTypeLogistics && [tableMessage.extra1 length] > 0) {
                chatStatusType = ChatStatusTypeDeliverGoods;
            }
        }
        
        //更新状态
        if (chatStatusType != ChatStatusTypeNone) {
            [self updateIMTableSessionChatStatusBySessionId:tableSession.sessionId chatStatus:chatStatusType];
        }
        
        //如果更新的消息的时间早于会话中最后一条消息时间  则不进行更新
        NSDate *lastUpdateTime = [[IMDataBaseManager shareInstance] getIMSessionLastMessageTimeDateBySessionId:tableMessage.sessionId];
        
        if (lastUpdateTime && tableSession.messageUpdateTime && [lastUpdateTime isLaterThan:tableSession.messageUpdateTime]) {
            return;
        }
        
        //如果session表中不存在对应的session 则在session表中添加对应的会话信息
        if (![self isContainSessionWithSessionId:tableMessage.sessionId]) {
            [self saveIMTableSession:tableSession];
            //重新从服务器上拉取会话信息
            [self sendNotifi_updateSessionListFromRemoteServer];
        }
        
        // 使用 prepareUpdate 方法进行链式调用
        BOOL rel = [[[[[[self.database prepareUpdate] table:IM_Table_Session] set:{IMTableSession.messageId,IMTableSession.contentType,IMTableSession.content,IMTableSession.messageStatus,IMTableSession.messageUpdateTime}] toObject:tableSession] where:IMTableSession.sessionId == tableMessage.sessionId && IMTableSession.loginUserId == [UserManager shareInstance].getUserId] execute];
        
        if (rel) {
            //更新完成会话最后一条消息
            [self sendNotifi_updateSessionListFromDataBase];
        }
        
    });
}

- (BOOL)updateIMTableSessionChatStatusBySessionId:(NSString *)aSessionId chatStatus:(ChatStatusType)chatStatusType {
    // 修改为使用新的 WCDB API：使用 updateTable:setProperty:toValue:where: 替代 updateRowsInTable:onProperty:withValue:where:
    BOOL rel = [_database updateTable:IM_Table_Session 
                        setProperty:IMTableSession.chatStatusType 
                            toValue:@(chatStatusType) 
                              where:IMTableSession.sessionId == aSessionId && IMTableSession.loginUserId == [UserManager shareInstance].getUserId];
    return rel;
}

//更新最后一条消息状态  如从发送中更改为已发送或者发送失败  或者消息被撤回等
- (void)updateLastMessageStatusToSessionTableWithMessageId:(NSString *)aMessageId {
    dispatch_queue_t globleQueue =dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
    dispatch_async(globleQueue, ^{
        
        //查询传来的消息id对应的消息
        // 修改为使用 getObjectOfClass 替代 getOneObjectOnResults 和 AllProperties
        IMTableMessage *tableMessage = [_database getObjectOfClass:IMTableMessage.class fromTable:IM_Table_Message where:IMTableMessage.messageId == aMessageId && IMTableMessage.loginUserId == [UserManager shareInstance].getUserId];
        
        //如果查询不到或者消息类型不为CHAT类型 则不进行处理
        if (!tableMessage || tableMessage.messageType != IMMessageTypeChat ) {
            return;
        }
        //获取对应会话最后一条消息 如果会话表保存的对应最后一条消息和要更新的最后一条消息相同 则进行操作
        IMTableSession *tableSession = [self getOneTableSessionWithSessionId:tableMessage.sessionId];

        if ([tableSession.messageId isEqualToString:tableMessage.messageId]) {
            //进行更新
            IMTableSession *newSession = [[IMTableSession alloc] init];
            newSession.messageId = tableMessage.messageId;
            newSession.contentType = tableMessage.contentType;
            newSession.content = tableMessage.content;
            newSession.messageStatus = tableMessage.status;
            
            newSession.sessionId = tableMessage.sessionId;
            
            newSession.messageUpdateTime = tableMessage.serverTime;
            
            // 使用 prepareUpdate 方法进行链式调用
            BOOL rel = [[[[[[self.database prepareUpdate] table:IM_Table_Session] set:{IMTableSession.messageId,IMTableSession.content,IMTableSession.contentType,IMTableSession.messageStatus,IMTableSession.messageUpdateTime}] toObject:newSession] where:IMTableSession.sessionId == newSession.sessionId && IMTableSession.loginUserId == [UserManager shareInstance].getUserId] execute];
            
            if (rel) {
                //更新完成会话最后一条消息状态
                [self sendNotifi_updateSessionListFromDataBase];
            }
        }
    });
}

#pragma mark- 会话表草稿操作
- (BOOL)updateIMTableSessionDraftContentBySessionId:(NSString *)aSessionId draftContent:(NSString *)draftContent draftDate:(NSDate *)draftDate{
    IMTableSession *session = [[IMTableSession alloc] init];
    session.draftContent = draftContent;
    session.draftDate = draftDate;
    
    // 修改为使用新的 WCDB API：使用 updateTable:setProperties:toObject:where: 替代 updateRowsInTable:onProperties:withObject:where:
    BOOL rel = [_database updateTable:IM_Table_Session 
                       setProperties:{IMTableSession.draftContent,IMTableSession.draftDate} 
                            toObject:session 
                               where:IMTableSession.sessionId == aSessionId && IMTableSession.loginUserId == [UserManager shareInstance].getUserId];
    if (rel) {
        [self sendNotifi_updateSessionListFromDataBase];
    }
    return rel;
}

- (NSString *)getIMTableSessionDraftContentBySessionId:(NSString *)aSessionId {
    // 修改为使用新的 WCDB API：使用 getValueOnResultColumn 替代 getOneValueOnResult
    return [_database getValueOnResultColumn:IMTableSession.draftContent 
                                  fromTable:IM_Table_Session 
                                      where:IMTableSession.sessionId == aSessionId && IMTableSession.loginUserId == [UserManager shareInstance].getUserId];
}
- (NSString *)getIMTableContactRemarkWithRosterUserId:(NSString *)aRosterUserid
{
    // 修改为使用新的 WCDB API：使用 getValueOnResultColumn 替代 getOneValueOnResult
    return [_database getValueOnResultColumn:IMTableContact.remark 
                                  fromTable:IM_Table_Contact 
                                      where:IMTableContact.rosterUserId == aRosterUserid && IMTableContact.loginUserId == [UserManager shareInstance].getUserId];
}

- (IMTableSession *)getTableSessionWithUserId:(NSString *)aRosterUserId {
    // 修改为使用新的 WCDB API：使用 getObjectOfClass 替代 getOneObjectOfClass
    return [_database getObjectOfClass:IMTableSession.class 
                           fromTable:IM_Table_Session 
                               where:IMTableSession.sessionId == aRosterUserId && IMTableSession.loginUserId == [UserManager shareInstance].getUserId];
}

- (BOOL)deleteAllIMSessions {
    // 修改为使用新的 WCDB API：使用 deleteFromTable: 替代 deleteAllObjectsFromTable:
    return [_database deleteFromTable:IM_Table_Session];
}

- (BOOL)deleteALLIMSessionsAndIMMessagesExceptSystem {
    // 使用 runTransaction 方法替代 beginTransaction/commitTransaction/rollbackTransaction
    // 需要正确指定 WCTHandle 参数
    BOOL rel = [_database runTransaction:^BOOL(WCTHandle* handle) {
        // 执行数据库操作 删除15天以前的消息
        BOOL success1 = [self->_database deleteFromTable:IM_Table_Session 
                                                  where:(IMTableSession.sessionId != 0) && IMTableSession.messageUpdateTime <= [[Config currentServerDate] dateBySubtractingDays:15]];
        
        BOOL success2 = [self->_database deleteFromTable:IM_Table_Message 
                                                  where:IMTableMessage.serverTime <= [[Config currentServerDate] dateBySubtractingDays:15]];
        
        return success1 && success2;
    }];
    
    if (rel) {
        // 如果清除成功 刷新会话列表 并刷新未读消息数
        [self sendNotifi_updateSumUnReadCount];
    }
    return rel;
}

#pragma mark - 发送更新通知

- (void)sendNotifi_updateSessionListFromDataBase {
    [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateSessionListFromDataBase object:nil];
}

- (void)sendNotifi_updateSessionListFromRemoteServer {
    [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateSessionListFromRemoteServer object:nil];
}

- (void)sendNotifi_updateSumUnReadCount {
    [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateSumUnReadCount object:nil];
    //更新总消息数时再次刷新消息列表
    [self sendNotifi_updateSessionListFromDataBase];
}

#pragma mark - 药典表
- (NSArray *)getAllPharamacopeias {
    // 修改为使用新的 WCDB API：使用 getObjectsOfClass 替代 getAllObjectsOfClass
    return [_database getObjectsOfClass:BRTablePharmacopeia.class fromTable:BR_Table_Pharmacopie];
}

- (NSArray *)getPharamacopeiasBySpell:(NSString *)spell {
    NSString *spellLike = [NSString stringWithFormat:@"%%%@%%",spell];
    NSString *drugLike = [NSString stringWithFormat:@"%%%@%%",spell];
    // 此方法的 API 已经符合最新标准，不需要修改
    return [_database getObjectsOfClass:BRTablePharmacopeia.class 
                             fromTable:BR_Table_Pharmacopie 
                                 where:BRTablePharmacopeia.spell.like(spell) || BRTablePharmacopeia.drugName.like(drugLike)];
}

- (NSArray *)getPharamacopeiasByDrugName:(NSString *)drugName spell:(NSString *)spell {
    NSString *spellLike = [NSString stringWithFormat:@"%%%@%%",spell];
    NSString *drugLike = [NSString stringWithFormat:@"%%%@%%",drugName];
    NSString *secondNameLike = [NSString stringWithFormat:@"%%%@%%",drugName];
    
    return [_database getObjectsOfClass:BRTablePharmacopeia.class fromTable:BR_Table_Pharmacopie where:BRTablePharmacopeia.spell.like(spellLike) || BRTablePharmacopeia.drugName.like(drugLike) || BRTablePharmacopeia.secondName.like(secondNameLike)];
}

- (BOOL)savePharamacopeias:(NSArray *)pharamacopeiaArr {
    // 使用 prepareInsert 方法替代 insertOrReplaceObjects:into:
    return [[[[[self.database prepareInsert] orReplace] intoTable:BR_Table_Pharmacopie] values:pharamacopeiaArr] execute];
}

- (BOOL)deleteAllPharamacopeias {
    // 修改为使用新的 WCDB API：使用 deleteFromTable: 替代 deleteAllObjectsFromTable:
    return [_database deleteFromTable:BR_Table_Pharmacopie];
}

#pragma mark - 临时药方表操作
//保存未完成药方
- (BOOL)saveTemporaryPrescription:(BRTemporaryPrescription *)prescription {
    // 使用 prepareInsert 方法替代 insertOrReplaceObject:into:
    return [[[[[self.database prepareInsert] orReplace] intoTable:BR_Table_Prescription] value:prescription] execute];
}
//删除未完成药方
- (BOOL)deleteTemporaryPrescriptionWithPrimaryKey:(NSString *)primaryKey {
    // 修改为使用新的 WCDB API：使用 deleteFromTable:where: 替代 deleteObjectsFromTable:where:
    return [_database deleteFromTable:BR_Table_Prescription 
                               where:BRTemporaryPrescription.primaryKey == primaryKey];
}
//更新未完成药方
- (BOOL)updateTemporaryPrescription:(BRTemporaryPrescription *)prescription {
    // 修改为使用新的 WCDB API：使用 updateTable:setProperties:toObject:where: 替代 updateRowsInTable:onProperties:withObject:where:
    return [_database updateTable:BR_Table_Prescription 
                   setProperties:{BRTemporaryPrescription.prescriptionStr} 
                        toObject:prescription 
                           where:BRTemporaryPrescription.primaryKey == prescription.primaryKey];
}
//获取未完成药方
- (BRTemporaryPrescription *)getTemporaryPrescriptionWithPrimaryKey:(NSString *)primaryKey {
    // 此处使用的是 getObjectsOfClass，查询结果集后取 lastObject
    // 更好的做法是直接使用 getObjectOfClass 查询单个对象，并增加排序确保正确的对象被返回
    return [_database getObjectOfClass:BRTemporaryPrescription.class
                            fromTable:BR_Table_Prescription
                                where:BRTemporaryPrescription.primaryKey == primaryKey];
}

//修改未完成药方中的患者信息
- (void)updateTemporaryPrescriptionPatientInfoWithPatientInfoDict:(NSDictionary *)patientInfoDict {
    
    NSString *userId = [patientInfoDict objectForKey:@"userId"];
    NSString *takerId = [patientInfoDict objectForKey:@"takerId"];
    
    //先检查本地中有没有该患者的未完成药方信息
    NSString *primaryKey = [NSString stringWithFormat:@"%@%@",[UserManager shareInstance].getUserId,userId];
    BRTemporaryPrescription * temPresModel = [self getTemporaryPrescriptionWithPrimaryKey:primaryKey];
    
    //如果本地不存在该患者的未完成的处方信息直接返回
    if (!temPresModel || !temPresModel.prescriptionStr) {
        return ;
    }
    
    NSString *prescriptionStr = temPresModel.prescriptionStr;
    NSDictionary *presDict = [prescriptionStr mj_JSONObject];
    
    BRPatientModel *patientModel = [[BRPatientModel alloc]init];
    patientModel.patientId = [presDict objectForKey:@"takerId"];
    patientModel.isPregnant = [presDict objectForKey:@"takerIsPregnant"];
    /*
    patientModel.sex = [presDict objectForKey:@"takerSex"];
    patientModel.name = [presDict objectForKey:@"takerName"];
    patientModel.age = [presDict objectForKey:@"takerAge"];
     */
    
    //如果不是该患者的子患者不予更新
    if (![takerId isEqualToString:patientModel.patientId]) {
        return ;
    }
    
    //如果怀孕状态不一样直接删除
    if (![patientModel.isPregnant isEqualToString:[patientInfoDict objectForKey:@"isPregnent"]]) {
        
        [self deleteTemporaryPrescriptionWithPrimaryKey:primaryKey];
        return ;
        
    }
    
    NSMutableDictionary *newPresDict = [NSMutableDictionary dictionaryWithDictionary:presDict];
    [newPresDict setObject:[patientInfoDict objectForKey:@"age"] forKey:@"takerAge"];
    [newPresDict setObject:[patientInfoDict objectForKey:@"isPregnent"] forKey:@"takerIsPregnant"];
    [newPresDict setObject:[patientInfoDict objectForKey:@"sex"] forKey:@"takerSex"];
    [newPresDict setObject:[patientInfoDict objectForKey:@"name"] forKey:@"takerName"];
    
    NSString *newPrescriptionStr = [newPresDict mj_JSONString];
    temPresModel.prescriptionStr = newPrescriptionStr;
    
    //更新本地数据库
    [self updateTemporaryPrescription:temPresModel];
    
}

@end


