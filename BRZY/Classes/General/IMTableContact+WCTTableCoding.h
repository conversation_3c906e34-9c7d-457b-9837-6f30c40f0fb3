//
//  IMTableContact+WCTTableCoding.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/6.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMTableContact.h"
#import <WCDB/WCDB.h>

@interface IMTableContact (WCTTableCoding) <WCTTableCoding>

WCDB_PROPERTY(loginUserId)

WCDB_PROPERTY(userId)
WCDB_PROPERTY(rosterUserId)
WCDB_PROPERTY(city)
WCDB_PROPERTY(nickName)
WCDB_PROPERTY(sex)
WCDB_PROPERTY(firstSpell)
WCDB_PROPERTY(mobile)
WCDB_PROPERTY(updateTime)
WCDB_PROPERTY(province)
WCDB_PROPERTY(headImgUrl)
WCDB_PROPERTY(createdTime)
WCDB_PROPERTY(age)
WCDB_PROPERTY(remark)
WCDB_PROPERTY(tags)
WCDB_PROPERTY(tag1)
WCDB_PROPERTY(tag2)

WCDB_PROPERTY(r_description)

WCDB_PROPERTY(status)
WCDB_PROPERTY(groupName)
WCDB_PROPERTY(subType)

@end
