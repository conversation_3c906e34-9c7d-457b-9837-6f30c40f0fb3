//
//  BaseViewController.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/1.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BaseViewController.h"

#import <AssetsLibrary/AssetsLibrary.h>
#import <AVFoundation/AVCaptureDevice.h>
#import <AVFoundation/AVMediaFormat.h>
#import "BRAlertView.h"
#import "CameraManager.h"

@interface BaseViewController ()<UINavigationControllerDelegate,UIImagePickerControllerDelegate>

@end

@implementation BaseViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    self.view.backgroundColor = [UIColor br_backgroundColor];
    
}

- (void)showNavBackItem {
    UIBarButtonItem *backItem = [[UIBarButtonItem alloc] initWithImage:[[UIImage imageNamed:@"navi_back_btn"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal] style:UIBarButtonItemStylePlain target:self action:@selector(clickBackButton:)];
    self.navigationItem.leftBarButtonItem = backItem;
}

- (void)showNavQRItem {
    UIImage *barcodeImage = [[UIImage imageNamed:@"qrcode_small"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    UIBarButtonItem *barcodeButtonItem = [[UIBarButtonItem alloc] initWithImage:barcodeImage style:UIBarButtonItemStylePlain target:self action:@selector(clickQRCodeButton:)];
    
    self.navigationItem.leftBarButtonItem = barcodeButtonItem;
}

- (void)showNavAddItem {
    UIImage *addImage = [[UIImage imageNamed:@"navi_add_btn"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    UIBarButtonItem *addButtonItem = [[UIBarButtonItem alloc] initWithImage:addImage style:UIBarButtonItemStylePlain target:self action:@selector(clickAddButton:)];
    
    self.navigationItem.rightBarButtonItem = addButtonItem;
}

- (void)showNavSearchItem {
    UIImage *searchImage = [[UIImage imageNamed:@"navi_zoom_btn"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    UIBarButtonItem *searchButtonItem = [[UIBarButtonItem alloc] initWithImage:searchImage style:UIBarButtonItemStylePlain target:self action:@selector(clickZoomButton:)];
    
    self.navigationItem.rightBarButtonItem = searchButtonItem;
}

- (void)hideNavSearchItem {
    
    UIBarButtonItem *searchButtonItem = [[UIBarButtonItem alloc] initWithTitle:nil style:UIBarButtonItemStyleDone target:self action:nil];
    self.navigationItem.rightBarButtonItem = searchButtonItem;
}
#pragma mark - Click Event
- (void)clickBackButton:(UIButton *)sender {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)clickQRCodeButton:(UIButton *)sender {
    
}

- (void)clickAddButton:(UIButton *)sender {
    
}

- (void)clickZoomButton:(UIButton *)sender {
    
}
#pragma mark - 调用相册相机上传图片

- (void)presentCameraVC
{
    
    __weak typeof(self) weakSelf = self;
    [Utils showActionSheetWithTitle:@"选择" buttons:@[@"拍摄",@"从相册选择"] complection:^(NSInteger buttonIndex) {
        
        NSUInteger sourceType = 0;
        if (buttonIndex == 0) {
            
            // 相机
            sourceType = UIImagePickerControllerSourceTypeCamera;
            
        } else {
            
            // 相册
            sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
            
        }
        
        [weakSelf judgeIfHaveAuthorityWithSourceType:sourceType];
    }];
}

- (void)judgeIfHaveAuthorityWithSourceType:(UIImagePickerControllerSourceType)sourceType {
    
    BOOL cameraAuthority = YES;
    BOOL photoLibraryAuthority = YES;
    
    ALAuthorizationStatus author = [ALAssetsLibrary authorizationStatus];
    if (author == kCLAuthorizationStatusRestricted || author ==kCLAuthorizationStatusDenied)
    {
        //无权限访问相册
        photoLibraryAuthority = NO;
    }
    
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
    if (authStatus == AVAuthorizationStatusRestricted || authStatus ==AVAuthorizationStatusDenied)
    {
        //无权限访问相机
        cameraAuthority = NO;
    }
    
    
    if (photoLibraryAuthority == YES && sourceType == UIImagePickerControllerSourceTypePhotoLibrary) {
        [self gotoPhotoLibraryAndPhotoAlbumWithSourceType:sourceType];
    }
    else if (photoLibraryAuthority == NO && sourceType == UIImagePickerControllerSourceTypePhotoLibrary){
        
        BRAlertView *alertView = [[BRAlertView alloc] init];
        alertView.isHideWhenTapBackground = YES;
        [alertView.okButton setTitle:@"确定" forState:UIControlStateNormal];
        [alertView showAlertView:@"请在iPhone的\"设置-隐私-照片\"选项中，允许必然中医访问你的照片" completion:^{
            [alertView close];
        }];
    }
    else if (cameraAuthority == YES && sourceType ==UIImagePickerControllerSourceTypeCamera){
        [self gotoPhotoLibraryAndPhotoAlbumWithSourceType:sourceType];
    }
    else {
        
        BRAlertView *alertView = [[BRAlertView alloc] init];
        alertView.isHideWhenTapBackground = YES;
        [alertView.okButton setTitle:@"确定" forState:UIControlStateNormal];
        [alertView showAlertView:@"请在iPhone的\"设置-隐私-相机\"选项中，允许必然中医访问你的手机相机" completion:^{
            [alertView close];
        }];
    }
    
}

- (void)gotoPhotoLibraryAndPhotoAlbumWithSourceType:(NSInteger)sourceType {
    
    // 跳转到相机或相册页面
    UIImagePickerController *imagePickerController = [[UIImagePickerController alloc] init];
    imagePickerController.delegate = self;
    imagePickerController.allowsEditing = NO;
    imagePickerController.sourceType = sourceType;
    [self presentViewController:imagePickerController animated:YES completion:^{}];
    
}

#pragma mark - UIImagePickerViewControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingImage:(UIImage *)image editingInfo:(NSDictionary *)editingInfo
{
    
}
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary *)info
{
    [picker dismissViewControllerAnimated:YES completion:nil];
    UIImage *image = [info objectForKey:UIImagePickerControllerOriginalImage];
    if (!image) {
        return;
    }
    NSData* imageZoom = [CameraManager imageSizeAfterZoom:image];
    if ([self respondsToSelector:@selector(selectImageFinished:)]) {
        [self selectImageFinished:imageZoom];
    }
}
- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [picker dismissViewControllerAnimated:YES completion:nil];
}

- (void)selectImageFinished:(NSData *)image
{
    
}

- (void)removeNotification
{
    
}
- (void)addNotification
{
    
}

- (BOOL)requestForUploadFile:(NSData *)file superImageView:(UIImageView *)imageView requestFinished:(RequestFinished)finished
{
    
    NSString *type = imageView?@"image":@"voice";
    
    NSDictionary *dict = @{@"method_code":@"000032",
                           @"resourceType":type};
    [HTTPRequest HttpPostRequsetWithUrl:kServerDomain params:dict file:file progress:^(NSProgress *progress) {
        NSLog(@"%f", progress.completedUnitCount/(CGFloat)progress.totalUnitCount);
    } sucess:^(id responsedObject, NSURLSessionDataTask *task) {
        
        if (imageView) {
            imageView.image = [UIImage imageWithData:file];
        }
        if (finished) {
            finished(responsedObject[@"url"]);
        }
    } error:^(NSError *errMsg, NSURLSessionDataTask *task) {
        
    }];
    return YES;
}
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];

}

@end
