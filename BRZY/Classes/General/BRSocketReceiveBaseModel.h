//
//  BRSocketReceiveBaseModel.h
//  BRZY
//
//  Created by  xujiangtao on 2017/8/29.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface BRSocketReceiveBaseModel : NSObject

@property (copy, nonatomic) NSString *code;
@property (copy, nonatomic) NSString *resultCode;
@property (copy, nonatomic) NSString *connectionId;
@property (copy, nonatomic) NSString *socketId;
@property (copy, nonatomic) NSString *resultDescription;
@property (copy, nonatomic) NSString *app_isHaveTakePictureAuth;
//接收消息时
@property (copy, nonatomic) NSString *userid;//发送消息的用户id
//撤回消息时用到
@property (copy, nonatomic) NSString *fromId;
@end
