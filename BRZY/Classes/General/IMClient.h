//
//  IMClient.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON>ta<PERSON> on 2017/8/28.
//  Copyright © 2017年 Yi <PERSON>ang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "IMSessionManager.h"
#import "IMContactManager.h"

#import "IMMessage.h"
#import "IMSession.h"
#import "IMContact.h"

@interface IMClient : NSObject

@property (strong, nonatomic) IMSessionManager *sessionManager;
@property (strong, nonatomic) IMContactManager *contactManager;

+ (instancetype)shareInstance;

- (void)initializationSDK;
- (void)startRequestReceiveMessage;

@end
