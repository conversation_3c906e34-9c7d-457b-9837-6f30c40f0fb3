//
//  IMDataBaseManager.h
//  BRZY
//
//  Created by  <PERSON><PERSON>jiangta<PERSON> on 2017/8/23.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@class IMTableMessage;
@class IMTableContact;
@class IMTableSession;

@class BRTemporaryPrescription;

@interface IMDataBaseManager : NSObject

+ (instancetype)shareInstance;
#pragma mark - 创建表
//创建联系人表
- (void)createContactTable;
//创建消息表
- (void)createMessageTable;
//创建会话表
- (void)createSessionTable;
//创建药典表
- (void)createPharmacopeiaTable;
//创建临时药方表
- (void)createTemporaryPrescriptionTable;

#pragma mark - 消息表
/************************** 消 息 **************************/

//保存消息
- (BOOL)saveIMTableMessage:(IMTableMessage *)aTableMessage;
//取出单条消息
- (IMTableMessage *)getOneMessageByMessageId:(NSString *)aMessageId;
- (IMTableMessage *)getOneMessageByFromId:(NSString *)aFromId;
//对应会话中最后一条消息
- (IMTableMessage *)getLastOneMessageBySessionId:(NSString *)aSessionId;
//获取对应的会话的未读消息数
- (NSInteger)getLocalUnReadMessageCountWithSessionId:(NSString *)aSessionId;
//获取当前登录的用户所有的未读消息数
- (NSInteger)getAllLocalUnReadMessageCount;

//是否包含某条消息
- (BOOL)isContainMessageWithMessageId:(NSString *)aMessageId;

//从数据库中获取指定数量的消息 按照时间倒序排列
- (NSArray *)getPreIMTableMessagesWithSessionId:(NSString *)aSessionId
                                     serverTime:(NSDate *)serverTime
                                          limit:(NSInteger)limit;

- (BOOL)updateIMTableMessageServerTime:(NSDate *)aSerVerTime
                                status:(IMMessageStatus)aStatus
                       serverMessageId:(NSString *)aServerMessageId
                             messageId:(NSString *)aMessageId;

- (BOOL)updateIMTableMessageWithFromId:(NSString *)aFromId status:(IMMessageStatus)aStatus;

//下载语音等文件后更新数据库中本地文件地址 
- (BOOL)updateIMTableMessageFileLocalPathWithPath:(NSString *)aFilePath
                                           length:(NSInteger)length
                                        messageId:(NSString *)aMessageId;

//更新tableMessage所有字段
- (BOOL)updateIMTableMessage:(IMTableMessage *)tableMessage;
//根据fromId更新tableMessage字段  根据fromId来确定具体更新哪条数据
- (BOOL)updateIMTableMessageContentType:(IMContentType)aContentType content:(NSString *)aContent byFromId:(NSString *)aFromId;

//更新对应会话id下的消息为本地已读
- (BOOL)updateSessionIMTableMessageLocalReadToReadedWithSessionId:(NSString *)aSessionId;
//更新指定消息id 的消息为本地已读
- (BOOL)updateOneIMTableMessageLocalReadToReaded:(NSString *)aMessageId;
//更新所有的正在发送中的消息为发送失败
- (BOOL)updateAllIMTableMessageSendDeliveringToSendFailed;
//更新远程地址
- (BOOL)updateIMTableMessageRemotePathWithMessageId:(NSString *)aMessageId remotePath:(NSString *)aRemotePath;
//删除指定会话下的所有消息
- (BOOL)deleteAllIMTableMessageWithSessionId:(NSString *)aSessionId;
//删除指定会话数组下的所有消息
- (BOOL)deleteAllIMTableMessageWithSessionIdsArray:(NSArray *)aSessionIdsArray;
//删除指定消息id的消息
- (BOOL)deleteIMTableMessageWithMessageId:(NSString *)aMessageId;
//更新所有消息为已读
- (BOOL)updateAllUnReadMessageToReaded;
//清空所有会话消息
- (BOOL)deleteAllIMMessages;

#pragma mark - 联系人表
/************************** 联系人 **************************/

//保存联系人
- (BOOL)saveIMTableContact:(IMTableContact *)aTableContact;
//是否已经存在某个联系人
- (BOOL)isContainContactWithRosterUserId:(NSString *)aRosterUserId;
//更新联系人
- (BOOL)updateIMTableContact:(IMTableContact *)aTableContact;
//获取联系人首字母数组
- (NSArray *)getIMContactFirstCharactors;
//根据首字母获取对应的数组
- (NSArray *)getIMContactObjectByFirstCharactor:(NSString *)firstCharactor;
//获取指定联系人的头像地址  RosterUserId 即 患者的用户id  也为sessionId patientId
- (NSString *)getIMContactHeadImgUrlByRosterUserId:(NSString *)aRosterUserId;
//删除联系人表中黑名单用户
- (BOOL)deleteIMContactBlackListByRosterUserIdArray:(NSArray *)blacklistidsArray;
//根据RosterUserId获取单个联系人
- (IMTableContact *)getIMContactByRosterUserId:(NSString *)aRosterUserId;
//修改联系人tag值 tagname
- (BOOL)updateIMTableMessageTagWithRosterUserId:(NSString *)aRosterUserId tagname:(NSString *)tagname;
//修改联系人备注名称
- (BOOL)updateIMTableContactRemarkWithRosterUserId:(NSString *)aRosterUserid remark:(NSString *)remark;
//修改联系人标签
- (BOOL)updateIMTableContactTagsWithRosterUserId:(NSString *)aRosterUserId tags:(NSArray *)tags;

//获取指定备注
- (NSString *)getIMTableContactRemarkWithRosterUserId:(NSString *)aRosterUserid;
//根据会话id获取会话
- (IMTableSession *)getTableSessionWithUserId:(NSString *)aRosterUserId;

#pragma mark - 会话表
/************************** 会话列表 **************************/
//保存会话
- (BOOL)saveIMTableSession:(IMTableSession *)aTableSession;
//更新会话
- (BOOL)updateIMTableSession:(IMTableSession *)aTableSession;
//从数据库中获取会话列表
- (NSArray *)getAllSessions;
//根据关键字搜索会话列表
- (NSArray *)getSessionByMatchName:(NSString *)aName;
//根据状态搜索会话
- (NSArray *)getSessionByMatchChatStatus:(NSInteger)chatStatus;
//根据关键字和状态搜索会话
- (NSArray *)getSessionByMathName:(NSString *)aName chatStatus:(NSInteger)chatStatus;
//根据关键字搜索消息中包含关键字的会话，其绘画对象的列表
- (NSArray *)getSessionIdByMatchMessageInfoKeyworkds:(NSString *)keyword;
//删除会话列表中黑名单会话
- (BOOL)deleteIMSessionBlackListBySessionIdArray:(NSArray *)blacklistidsArray;
//获取会话最后一条消息的时间
- (NSDate *)getIMSessionLastMessageTimeDateBySessionId:(NSString *)aSessionId;
//删除指定sessionId的会话
- (BOOL)deleteIMSessionBySessionId:(NSString *)aSessionId;
//是否存在指定的会话id的会话
- (BOOL)isContainSessionWithSessionId:(NSString *)aSessionId;
//更新状态
- (BOOL)updateIMTableSessionChatStatusBySessionId:(NSString *)aSessionId chatStatus:(ChatStatusType)chatStatusType;
//更新草稿
- (BOOL)updateIMTableSessionDraftContentBySessionId:(NSString *)aSessionId draftContent:(NSString *)draftContent draftDate:(NSDate *)draftDate;
//获取指定某条会话的草稿
- (NSString *)getIMTableSessionDraftContentBySessionId:(NSString *)aSessionId;

//清空会话表
- (BOOL)deleteAllIMSessions;

//清空会话列表和消息数据
- (BOOL)deleteALLIMSessionsAndIMMessagesExceptSystem;


#pragma mark - 药典操作
//获取所有的药典信息

- (NSArray *)getAllPharamacopeias;

- (NSArray *)getPharamacopeiasBySpell:(NSString *)spell;

- (NSArray *)getPharamacopeiasByDrugName:(NSString *)drugName spell:(NSString *)spell;

- (BOOL)savePharamacopeias:(NSArray *)pharamacopeiaArr;

- (BOOL)deleteAllPharamacopeias;

#pragma mark - 临时药方操作
//保存未完成药方
- (BOOL)saveTemporaryPrescription:(BRTemporaryPrescription *)prescription;
//删除未完成药方
- (BOOL)deleteTemporaryPrescriptionWithPrimaryKey:(NSString *)primaryKey;
//更新未完成药方
- (BOOL)updateTemporaryPrescription:(BRTemporaryPrescription *)prescription;
//获取未完成药方
- (BRTemporaryPrescription *)getTemporaryPrescriptionWithPrimaryKey:(NSString *)primaryKey;
//修改未完成药方中的患者信息
- (void)updateTemporaryPrescriptionPatientInfoWithPatientInfoDict:(NSDictionary *)patientInfoDict;

@end
