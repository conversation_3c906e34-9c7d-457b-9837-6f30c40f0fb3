//
//  CameraManager.m
//  ZYZS
//
//  Created by wu on 15/11/18.
//  Copyright © 2015年 wu. All rights reserved.
//

#import "CameraManager.h"

@implementation CameraManager
+ (UIImage *)scaleToSize:(UIImage *)img size:(CGSize)size{
    // 创建一个bitmap的context
    // 并把它设置成为当前正在使用的context
    UIGraphicsBeginImageContext(size);
    // 绘制改变大小的图片
    //    CGFloat imageWidth = MAX(size.width, size.height);
    //    CGFloat smaller = MIN(size.width, size.height);
    //    [img drawInRect:CGRectMake((size.width-smaller)/2, (size.height-smaller)/2, smaller, smaller)];
    [img drawInRect:CGRectMake(0, 0, size.width, size.height)];
    
    // 从当前context中创建一个改变大小后的图片
    UIImage* scaledImage = UIGraphicsGetImageFromCurrentImageContext();
    // 使当前的context出堆栈
    UIGraphicsEndImageContext();
    // 返回新的改变大小后的图片
    return scaledImage;
}

+ (NSData*)imageSizeAfterZoom:(UIImage*)image
{
    CGFloat scaleImage = 0;
    CGSize imageSize = [image size];
    CGFloat imageWidth = imageSize.width;
    CGFloat imageHeight = imageSize.height;
    CGSize imageSizeZoom = CGSizeZero;
    if (imageWidth < imageHeight) {
        scaleImage = imageHeight/1080;
    }else{
        scaleImage = imageWidth/1080;
    }
    imageSizeZoom = CGSizeMake(imageWidth/scaleImage, imageHeight/scaleImage);
    UIImage* imageZoom = [self scaleToSize:image size:imageSizeZoom];
    
    NSUInteger dataLenthDefault = 614400;
    
    NSData* data = UIImageJPEGRepresentation(imageZoom, 1.0);
    
    if ([data length] > dataLenthDefault) {
        
        float scaleSize = (CGFloat)dataLenthDefault/(CGFloat)[data length];
        
        data = UIImageJPEGRepresentation(imageZoom, scaleSize);
        
    }
    
    return data;
}
@end
