//
//  IMMessage.h
//  BRZY
//
//  Created by  xujiangtao on 2017/8/28.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface IMMessage : NSObject

@property (copy, nonatomic) NSString *messageId;
@property (copy, nonatomic) NSString *serverMessageId;//服务器消息Id
@property (nonatomic) int localId;  //数据库保存Id
@property (copy, nonatomic) NSString *from;        //发送方id
@property (copy, nonatomic) NSString *to;          //接收方id
@property (copy, nonatomic) NSString *fromId;      //发送方的消息ID

@property (nonatomic) IMSessionType sessionType;
@property (nonatomic) IMContentType contentType;
@property (nonatomic) IMMessageType messageType;
@property (nonatomic) IMMessageOpenType openType;   //用于交流页面系统消息 订单 物流 其他
//@property (copy, nonatomic) NSString *openInfo;     //交流页面系统消息 当openType为订单或者物流时  打开的订单号或者物流单号

@property (copy, nonatomic) NSString *sessionId;    //会话id

@property (nonatomic)   IMMessageStatus status;     //消息发送状态
//@property (strong, nonatomic) NSDate *createTime;
@property (strong, nonatomic) NSDate *serverTime;
@property (copy, nonatomic) NSString *text;
@property (copy, nonatomic) NSString *filePath;     //文件地址
@property (nonatomic)   BOOL isOriginal;            //生成时间
@property (nonatomic)   NSInteger length;           //语音长度
//@property (strong, nonatomic) NSData *fileData;     //附件data

@property (copy, nonatomic) NSString *linkUrl;    //问诊单 系统消息等链接地址

@property (copy, nonatomic) NSString *finalLinkUrl; //问诊单 最终链接地址  you

//如果为Notice等
@property (copy, nonatomic) NSString *wzdId;
@property (copy, nonatomic) NSString *title;
@property (copy, nonatomic) NSString *wzdType;
@property (copy, nonatomic) NSString *wzdVersion;
@property (copy, nonatomic) NSString *extra1;
@property (copy, nonatomic) NSString *extra2;
@property (copy, nonatomic) NSString *extra3;


#pragma mark - 创建消息
+ (IMMessage *)createTextSendMessgeWithText:(NSString *)text toUserId:(NSString *)userId;

+ (IMMessage *)createImageSendMessageWithData:(NSData *)data toUserId:(NSString *)userId;

+ (IMMessage *)createAudioSendMessageWithPath:(NSString *)audioPath length:(NSInteger)length toUserId:(NSString *)userId;

+ (IMMessage *)createDoctorStartSendMessageToUserId:(NSString *)userId;

+ (IMMessage *)createDoctorFinishSendMessageToUserId:(NSString *)userId;

+ (IMMessage *)createSupplementQuestionMessageWithGroupId:(NSString *)groupId toUserId:(NSString *)userId;
@end
