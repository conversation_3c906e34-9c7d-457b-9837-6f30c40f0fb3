//
//  IMTableMessage+WCTTableCoding.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/6.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMTableMessage.h"
#import <WCDB/WCDB.h>

@interface IMTableMessage (WCTTableCoding) <WCTTableCoding>

WCDB_PROPERTY(loginUserId)

WCDB_PROPERTY(localId)
WCDB_PROPERTY(messageId)
WCDB_PROPERTY(messageType)
WCDB_PROPERTY(contentType)
WCDB_PROPERTY(content)
//WCDB_PROPERTY(createTime)
WCDB_PROPERTY(serverTime)
WCDB_PROPERTY(sessionId)
WCDB_PROPERTY(sessionType)
WCDB_PROPERTY(fromId)
WCDB_PROPERTY(from)
WCDB_PROPERTY(to)
WCDB_PROPERTY(serverId)
WCDB_PROPERTY(serverIp)
WCDB_PROPERTY(isLocalRead)
WCDB_PROPERTY(isRemoteRead)
WCDB_PROPERTY(proxyId)
WCDB_PROPERTY(proxyName)
WCDB_PROPERTY(fileRemotePath)
WCDB_PROPERTY(fileLocalPath)
WCDB_PROPERTY(status)
WCDB_PROPERTY(serverMessageId)
WCDB_PROPERTY(voiceLength)
WCDB_PROPERTY(linkUrl)
//WCDB_PROPERTY(orderNumber)
//WCDB_PROPERTY(logisticNumber)
WCDB_PROPERTY(openType)
WCDB_PROPERTY(title)
WCDB_PROPERTY(extra1)
WCDB_PROPERTY(extra2)
WCDB_PROPERTY(extra3)
WCDB_PROPERTY(version)
WCDB_PROPERTY(wzdType)

@end
