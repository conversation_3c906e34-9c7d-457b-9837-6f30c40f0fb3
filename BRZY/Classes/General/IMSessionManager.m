//
//  IMSessionManager.m
//  BRZY
//
//  Created by  xujiangta<PERSON> on 2017/9/4.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMSessionManager.h"
#import "IMSession.h"
#import "IMMessage.h"
#import "IMTableMessage.h"
#import "IMSDKHelper.h"
#import "IMDataBaseManager.h"
#import "BRMessagesModel.h"
#import "BRMessageModel.h"
#import "BRError.h"
#import "SDWebImageDownloader.h"
#import "SDImageCache.h"
#import "BRSessionListModel.h"
#import "IMTableSession.h"

static IMSessionManager *sessionManager = nil;

@interface IMSessionManager ()

@property (strong, nonatomic) NSMutableSet *delegateSet;
@property (copy, nonatomic) void (^withdrawHandle)(IMMessage *message);

@end

@implementation IMSessionManager

+ (instancetype)shareInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sessionManager = [[IMSessionManager alloc] init];
    });
    return sessionManager;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        //从本地数据库更新会话列表并更新ui界面
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(notificationUpdateSessionList) name:kIMNotificationUpdateSessionListFromDataBase object:nil];
        //从服务器重新更新会话列表
//        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateSessionListFromServer) name:kIMNotificationUpdateSessionListFromRemoteServer object:nil];
    }
    return self;
}

- (NSMutableSet *)delegateSet {
    if (!_delegateSet) {
        _delegateSet = [NSMutableSet set];
    }
    return _delegateSet;
}

- (void)addDelegate:(id<IMSessionManagerDelegate>)delegate {
    [self.delegateSet addObject:delegate];
}

- (void)removeDelegate:(id<IMSessionManagerDelegate>)delegate {
    [self.delegateSet removeObject:delegate];
}
#pragma mark - 未读消息数目
- (NSInteger)getAllUnReadMessageCount {
    return [[IMDataBaseManager shareInstance] getAllLocalUnReadMessageCount];
}
#pragma mark - 发送消息
- (void)sendMessage:(IMMessage *)aMessage {
    
    //发送文字消息
    if (aMessage.contentType == IMContentTypeText) {
        [self dealSendTextMessage:aMessage];
    }
    //发送图片消息
    else if (aMessage.contentType == IMContentTypeImage){
        [self dealSendImageMessage:aMessage];
    }
    //发送语音消息
    else if (aMessage.contentType == IMContentTypeAudio){
        [self dealSendAudioMessage:aMessage];
    }
    //发送医生开启会话消息
    else if (aMessage.contentType == IMContentTypeStartChatDoctor) {
        [self dealSendDoctorStartMessage:aMessage];
    }
    //发送医生关闭会话消息
    else if (aMessage.contentType == IMContentTypeFinishChatDoctor) {
        [self dealSendDoctorFinishMessage:aMessage];
    }
    //发送补充问题问题消息
    else if (aMessage.contentType == IMContentTypeSupplementQuestion) {
        [self dealSendSupplementQuestionMessage:aMessage];
    }
}

#pragma mark 文本消息
- (void)dealSendTextMessage:(IMMessage *)aMessage {
    IMTableMessage *tableMessage = [IMSDKHelper convertIMMessageToIMTableMessage:aMessage];
    BOOL rel = [[IMDataBaseManager shareInstance] saveIMTableMessage:tableMessage];
    
    if (rel) {
        //保存成功 组装发送消息字典
        NSMutableDictionary *contentDict = [NSMutableDictionary dictionaryWithCapacity:0];
        [contentDict setObject:tableMessage.content forKey:@"content"];
        [contentDict setObject:[kIMContentTypeArray objectAtIndex:IMContentTypeText] forKey:@"type"];
        
        NSMutableDictionary *messagesDict = [NSMutableDictionary dictionaryWithCapacity:0];
        [messagesDict setObject:contentDict forKey:@"content"];
        [messagesDict setObject:tableMessage.from forKey:@"from"];
        [messagesDict setObject:tableMessage.fromId forKey:@"fromId"];
        [messagesDict setObject:tableMessage.to forKey:@"to"];
        [messagesDict setObject:@"OBJECT" forKey:@"contentType"];
        
        NSMutableDictionary *sendDict = [NSMutableDictionary dictionaryWithCapacity:0];
        [sendDict setObject:@[messagesDict] forKey:@"messages"];
        [sendDict setObject:BRApiMessage forKey:@"code"];
        
        [[SocketManager shareInstance] sendDataDict:sendDict];
    }
    else{
        //保存失败
        NSLog(@"保存消息到数据库失败");
    }
}
#pragma mark 图片消息
- (void)dealSendImageMessage:(IMMessage *)aMessage {
    //发送图片消息
    if (!aMessage.filePath) {
        return;
    }
    
    //转为数据库保存对象
    IMTableMessage *tableMessage = [IMSDKHelper convertIMMessageToIMTableMessage:aMessage];
    
    //保存到数据库中
    BOOL rel = [[IMDataBaseManager shareInstance] saveIMTableMessage:tableMessage];
    
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:0];
    [params setObject:@"image" forKey:@"resourceType"];
    [params setObject:@"000032" forKey:@"method_code"];
    
    //转发图片
    if ([aMessage.filePath hasPrefix:@"http://"] || [aMessage.filePath hasPrefix:@"https://"]) {
        [[IMDataBaseManager shareInstance] updateIMTableMessageRemotePathWithMessageId:tableMessage.messageId remotePath:aMessage.filePath];
        //保存成功  组装发送消息字典
        NSMutableDictionary *contentDict = [NSMutableDictionary dictionaryWithCapacity:0];
        [contentDict setObject:aMessage.filePath forKey:@"content"];
        [contentDict setObject:[kIMContentTypeArray objectAtIndex:IMContentTypeImage] forKey:@"type"];
        
        NSMutableDictionary *messagesDict = [NSMutableDictionary dictionaryWithCapacity:0];
        [messagesDict setObject:contentDict forKey:@"content"];
        [messagesDict setObject:tableMessage.from forKey:@"from"];
        [messagesDict setObject:tableMessage.fromId forKey:@"fromId"];
        [messagesDict setObject:tableMessage.to forKey:@"to"];
        [messagesDict setObject:@"OBJECT" forKey:@"contentType"];
        
        NSMutableDictionary *sendDict = [NSMutableDictionary dictionaryWithCapacity:0];
        [sendDict setObject:@[messagesDict] forKey:@"messages"];
        [sendDict setObject:BRApiMessage forKey:@"code"];
        
        [[SocketManager shareInstance] sendDataDict:sendDict];
    }
    //发送图片
    else {
        NSString *finalPath = [IMDocumentPath stringByAppendingPathComponent:aMessage.filePath];
        NSData *imageData = [NSData dataWithContentsOfFile:finalPath];
        
        //向服务器上传图片
        [HTTPRequest POST:kServerDomain parameters:params constructingBodyWithBlock:^(id<AFMultipartFormData> formData) {
            [formData appendPartWithFileData:imageData name:@"file" fileName:@"fileName.jpg" mimeType:@"image/jpg"];
        } progress:^(NSProgress *progress) {
            
        } success:^(NSURLSessionDataTask *task, id responseObject) {
            NSString *code = [responseObject objectForKey:@"code"];
            if ([code isEqualToString:@"0000"]) {
                NSString *urlString = [responseObject objectForKey:@"url"];
                //            tableMessage.fileRemotePath = urlString;
                
                [[IMDataBaseManager shareInstance] updateIMTableMessageRemotePathWithMessageId:tableMessage.messageId remotePath:urlString];
                //保存成功  组装发送消息字典
                NSMutableDictionary *contentDict = [NSMutableDictionary dictionaryWithCapacity:0];
                [contentDict setObject:urlString forKey:@"content"];
                [contentDict setObject:[kIMContentTypeArray objectAtIndex:IMContentTypeImage] forKey:@"type"];
                
                NSMutableDictionary *messagesDict = [NSMutableDictionary dictionaryWithCapacity:0];
                [messagesDict setObject:contentDict forKey:@"content"];
                [messagesDict setObject:tableMessage.from forKey:@"from"];
                [messagesDict setObject:tableMessage.fromId forKey:@"fromId"];
                [messagesDict setObject:tableMessage.to forKey:@"to"];
                [messagesDict setObject:@"OBJECT" forKey:@"contentType"];
                
                NSMutableDictionary *sendDict = [NSMutableDictionary dictionaryWithCapacity:0];
                [sendDict setObject:@[messagesDict] forKey:@"messages"];
                [sendDict setObject:BRApiMessage forKey:@"code"];
                
                [[SocketManager shareInstance] sendDataDict:sendDict];
            }
            else{
                NSString *errorMessage = [responseObject objectForKey:@"errorMsg"];
                NSLog(@"upload image error message = %@",errorMessage);
                //消息发送失败
                [self dealWithMessageSendFaildWithFromId:tableMessage.messageId];
            }
        } failure:^(NSURLSessionDataTask *task, NSError *error) {
            NSLog(@"上传图片失败 = %@",error.description);
            //更新消息发送失败
            [self dealWithMessageSendFaildWithFromId:tableMessage.messageId];
        }];
    }
}



#pragma mark 语音消息
- (void)dealSendAudioMessage:(IMMessage *)aMessage {
    //发送语音消息
    if (!aMessage.filePath || !aMessage.length) {
        return;
    }
//    NSData *voiceData = [NSData dataWithContentsOfFile:aMessage.filePath];
    
    NSString *finalPath = [IMDocumentPath stringByAppendingPathComponent:aMessage.filePath];
    
    NSString *voiceType = [[finalPath componentsSeparatedByString:@"."] lastObject];
    
//    NSData *voiceData = [NSData dataWithContentsOfURL:[NSURL URLWithString:finalPath]];
    NSData *voiceData = [NSData dataWithContentsOfFile:finalPath];
    //转为数据库保存对象
    IMTableMessage *tableMessage = [IMSDKHelper convertIMMessageToIMTableMessage:aMessage];
    
    //保存到本地数据库中
    BOOL rel = [[IMDataBaseManager shareInstance] saveIMTableMessage:tableMessage];
    
    //向服务器上传语音
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:0];
    [params setObject:@"voice" forKey:@"resourceType"];
    [params setObject:@"000032" forKey:@"method_code"];
    
    [HTTPRequest POST:kServerDomain parameters:params constructingBodyWithBlock:^(id<AFMultipartFormData> formData) {
        [formData appendPartWithFileData:voiceData name:@"file" fileName:[NSString stringWithFormat:@"filename.%@",voiceType] mimeType:@"audio/AMR"];
    } progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            NSString *urlString = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"url"]];
            
            //更新远程地址到本地数据库
            [[IMDataBaseManager shareInstance] updateIMTableMessageRemotePathWithMessageId:tableMessage.messageId remotePath:urlString];
            
            //保存成功  组装发送消息的字典
            NSMutableDictionary *contentDict = [NSMutableDictionary dictionaryWithCapacity:0];
            [contentDict setObject:urlString forKey:@"content"];
            [contentDict setObject:@(tableMessage.voiceLength) forKey:@"extra1"];
            [contentDict setObject:[kIMContentTypeArray objectAtIndex:IMContentTypeAudio] forKey:@"type"];
            
            NSMutableDictionary *messagesDict = [NSMutableDictionary dictionaryWithCapacity:0];
            [messagesDict setObject:contentDict forKey:@"content"];
            [messagesDict setObject:tableMessage.from forKey:@"from"];
            [messagesDict setObject:tableMessage.fromId forKey:@"fromId"];
            [messagesDict setObject:tableMessage.to forKey:@"to"];
            [messagesDict setObject:@"OBJECT" forKey:@"contentType"];
            
            NSMutableDictionary *sendDict = [NSMutableDictionary dictionaryWithCapacity:0];
            [sendDict setObject:@[messagesDict] forKey:@"messages"];
            [sendDict setObject:BRApiMessage forKey:@"code"];
            
            [[SocketManager shareInstance] sendDataDict:sendDict];
        }
        else{
            NSString *errormMessage = [responseObject objectForKey:@"errorMsg"];
            NSLog(@"upload audio error message = %@",errormMessage);
            //消息发送失败
            [self dealWithMessageSendFaildWithFromId:tableMessage.messageId];
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"上传语音失败 = %@",error.description);
        //更新消息发送失败
        [self dealWithMessageSendFaildWithFromId:tableMessage.messageId];
    }];
}
#pragma mark 医生开启会话
- (void)dealSendDoctorStartMessage:(IMMessage *)aMessage {
    IMTableMessage *tableMessage = [IMSDKHelper convertIMMessageToIMTableMessage:aMessage];
    BOOL rel = [[IMDataBaseManager shareInstance] saveIMTableMessage:tableMessage];
    if (rel) {
        //保存成功 组装发送消息字典
//        NSMutableDictionary *contentDict = [NSMutableDictionary dictionaryWithCapacity:0];
//        [contentDict setObject:tableMessage.content forKey:@"content"];
//        [contentDict setObject:[kIMContentTypeArray objectAtIndex:IMContentTypeStartChatDoctor] forKey:@"type"];
//
//        NSMutableDictionary *messagesDict = [NSMutableDictionary dictionaryWithCapacity:0];
//        [messagesDict setObject:contentDict forKey:@"content"];
//        [messagesDict setObject:tableMessage.from forKey:@"from"];
//        [messagesDict setObject:tableMessage.fromId forKey:@"fromId"];
//        [messagesDict setObject:tableMessage.to forKey:@"to"];
//        [messagesDict setObject:@"OBJECT" forKey:@"contentType"];
//
//        NSMutableDictionary *sendDict = [NSMutableDictionary dictionaryWithCapacity:0];
//        [sendDict setObject:@[messagesDict] forKey:@"messages"];
//        [sendDict setObject:BRApiMessage forKey:@"code"];
        
//        [[SocketManager shareInstance] sendDataDict:sendDict];
    }
    //保存失败
    else{
        NSLog(@"保存医生开启会话数据库失败");
    }
}
#pragma mark  医生关闭会话
- (void)dealSendDoctorFinishMessage:(IMMessage *)aMessage {
    IMTableMessage *tableMessage = [IMSDKHelper convertIMMessageToIMTableMessage:aMessage];
    BOOL rel = [[IMDataBaseManager shareInstance] saveIMTableMessage:tableMessage];
    
    //保存成功 组装发送消息字典
    if (rel) {
//        NSMutableDictionary *contentDict = [NSMutableDictionary dictionaryWithCapacity:0];
//        [contentDict setObject:tableMessage.content forKey:@"content"];
//        [contentDict setObject:[kIMContentTypeArray objectAtIndex:IMContentTypeFinishChatDoctor] forKey:@"type"];
//
//        NSMutableDictionary *messagesDict = [NSMutableDictionary dictionaryWithCapacity:0];
//        [messagesDict setObject:contentDict forKey:@"content"];
//        [messagesDict setObject:tableMessage.from forKey:@"from"];
//        [messagesDict setObject:tableMessage.fromId forKey:@"fromId"];
//        [messagesDict setObject:tableMessage.to forKey:@"to"];
//        [messagesDict setObject:@"OBJECT" forKey:@"contentType"];
//
//        NSMutableDictionary *sendDict = [NSMutableDictionary dictionaryWithCapacity:0];
//        [sendDict setObject:@[messagesDict] forKey:@"messages"];
//        [sendDict setObject:BRApiMessage forKey:@"code"];
        
//        [[SocketManager shareInstance] sendDataDict:sendDict];
    }
    //保存失败
    else{
        NSLog(@"保存医生关闭会话数据库失败");
    }
}
#pragma mark 补充问题问题
- (void)dealSendSupplementQuestionMessage:(IMMessage *)aMessage {
    IMTableMessage *tableMessage = [IMSDKHelper convertIMMessageToIMTableMessage:aMessage];
    BOOL rel = [[IMDataBaseManager shareInstance] saveIMTableMessage:tableMessage];
    
    //保存成功 组装发送消息字典
    if (rel) {
        NSMutableDictionary *contentDict = [NSMutableDictionary dictionaryWithCapacity:0];
        [contentDict setObject:tableMessage.content forKey:@"content"];
        [contentDict setObject:tableMessage.extra1 forKey:@"extra1"];
        [contentDict setObject:tableMessage.linkUrl forKey:@"url"];
        [contentDict setObject:[kIMContentTypeArray objectAtIndex:IMContentTypeSupplementQuestion] forKey:@"type"];
        
        NSMutableDictionary *messagesDict = [NSMutableDictionary dictionaryWithCapacity:0];
        [messagesDict setObject:contentDict forKey:@"content"];
        [messagesDict setObject:tableMessage.from forKey:@"from"];
        [messagesDict setObject:tableMessage.fromId forKey:@"fromId"];
        [messagesDict setObject:tableMessage.to forKey:@"to"];
        [messagesDict setObject:@"OBJECT" forKey:@"contentType"];
        
        NSMutableDictionary *sendDict = [NSMutableDictionary dictionaryWithCapacity:0];
        [sendDict setObject:@[messagesDict] forKey:@"messages"];
        [sendDict setObject:BRApiMessage forKey:@"code"];
        
        [[SocketManager shareInstance] sendDataDict:sendDict];
    }
    else {
        NSLog(@"保存补充问题数据失败");
    }
}
#pragma mark - 接收消息 所有类型分发
- (void)socketManagerDidSDKReceiveMessages:(BRMessagesModel *)messagesModel {
    BRMessageModel *messageModel = [messagesModel.messages objectAtIndex:0];
    
    //聊天消息
    if ([messageModel.messageType isEqualToString:[kIMMessageTypeArray objectAtIndex:IMMessageTypeChat]]) {
        [self dealReceivedChatMessage:messageModel];
    }
    //系统消息
    else if ([messageModel.messageType isEqualToString:[kIMMessageTypeArray objectAtIndex:IMMessageTypeSystem]]){
        NSLog(@"收到系统消息==");
        [self dealReceivedSystemMessage:messageModel];
    }
    //发送给公共服务号的消息
    else if ([messageModel.messageType isEqualToString:[kIMMessageTypeArray objectAtIndex:IMMessageTypePublicService]]){
        NSLog(@"收到公共服务号的消息==");
    }
    //促销/中奖/抽奖
    else if ([messageModel.messageType isEqualToString:[kIMMessageTypeArray objectAtIndex:IMMessageTypeSales]]){
        NSLog(@"收到促销消息==");
    }
    //通知通告
    else if ([messageModel.messageType isEqualToString:[kIMMessageTypeArray objectAtIndex:IMMessageTypeNotice]]){
        [self dealReceivedNoticeMessage:messageModel];
    }
    //广告
    else if ([messageModel.messageType isEqualToString:[kIMMessageTypeArray objectAtIndex:IMMessageTypeAdvertisent]]){
        NSLog(@"收到广告的消息==");
    }
}

#pragma mark- 接收的聊天消息 分发处理
- (void)dealReceivedChatMessage:(BRMessageModel *)messageModel {
    
    //过滤消息
    if ([self filterMessageModelNotDeal:messageModel]) {
        return;
    }
    
    NSString *type = messageModel.content.type;
    
    //文字消息
    if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeText]]) {
        [self dealReceivedChatTextMessage:messageModel];
    }
    //图片消息
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeImage]]) {
        [self dealReceivedChatImageMessage:messageModel];
    }
    //语音消息
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeAudio]]) {
        [self dealReceivedChatAudioMessage:messageModel];
    }
    //问诊单 问题
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeWZDQuestion]]) {
        [self dealReceivedChatWzdQuestionMessage:messageModel];
    }
    //问诊单 答案
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeWZDAnswer]]){
        [self dealReceivedChatWzdAnswerMessage:messageModel];
    }
    //复诊单 问题
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeFZDQuestion]]) {
        [self dealReceivedChatFzdQuestionMessage:messageModel];
    }
    //复诊单 答案
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeFZDAnswer]]){
        [self dealReceivedChatFzdAnswerMessage:messageModel];
    }
    //补充问题 问题
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeSupplementQuestion]]){
        [self dealReceivedChatSupplementQuestionMessage:messageModel];
    }
    //补充问题 答案
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeSupplementAnswer]]){
        [self dealReceivedChatSupplementAnswerMessage:messageModel];
    }
    //交流页面系统消息
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeSystem]]) {
        [self dealReceivedChatSystemMessage:messageModel];
    }
    //物流消息
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeLogistics]]) {
        
    }
    //付费患者开启会话
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeStartChatPatient]]){
        [self dealReceivedChatStartChatPatientMessage:messageModel];
    }
    //医生开启会话
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeStartChatDoctor]]) {
        [self dealReceivedChatStartChatDoctorMessage:messageModel];
    }
    //医生结束会话
    else if ([type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeFinishChatDoctor]]) {
        [self dealReceivedChatFinishChatDoctorMessage:messageModel];
    }
    else{
        
        //
        [self sendMessageReceive:messageModel.serverMessageId];
    }
}

#pragma mark 文本消息
- (void)dealReceivedChatTextMessage:(BRMessageModel *)messageModel {
    
    IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
    [self sendDelegate_didReceiveMessage:message];
}

#pragma mark 图片消息
- (void)dealReceivedChatImageMessage:(BRMessageModel *)messageModel {
    
    IMTableMessage *tableMessage = [IMSDKHelper convertBRMessageModelToIMTableMessage:messageModel];
    
    //包含 则更新
    if ([[IMDataBaseManager shareInstance] isContainMessageWithMessageId:tableMessage.messageId]) {
//        [[IMDataBaseManager shareInstance] updateIMTableMessage:tableMessage];
        [self sendMessageReceive:tableMessage.serverMessageId];
        return;
    }
    //不包含 保存
    else {
        [[IMDataBaseManager shareInstance] saveIMTableMessage:tableMessage];
    }

    IMTableMessage *relTableMessage = [[IMDataBaseManager shareInstance] getOneMessageByMessageId:tableMessage.messageId];
    
    if (!relTableMessage || !relTableMessage.fileRemotePath) {
        //如果保存消息失败  则发送回执  忽略此消息
        [self sendMessageReceive:tableMessage.serverMessageId];
        
        return;
    }
    
    IMMessage *message = [IMSDKHelper convertIMTableMessageToIMMessage:relTableMessage];
    
    NSString *imgUrl = [message.filePath stringByReplacingOccurrencesOfString:@".jpg" withString:@"_middle.jpg"];
    
    //下载图片
    [[SDWebImageDownloader sharedDownloader] downloadImageWithURL:[NSURL URLWithString:imgUrl] options:SDWebImageDownloaderHighPriority progress:^(NSInteger receivedSize, NSInteger expectedSize) {
        
    } completed:^(UIImage *image, NSData *data, NSError *error, BOOL finished) {
        //将下载图片缓存到本地磁盘
        [[SDImageCache sharedImageCache] storeImage:image forKey:imgUrl toDisk:YES];
        //        message.fileData = data;
        //返回接收完成回执
        [self sendMessageReceive:relTableMessage.serverMessageId];
        //主线程中更新UI
        [self sendDelegate_didReceiveMessage:message];
    }];
}

#pragma mark 语音消息
- (void)dealReceivedChatAudioMessage:(BRMessageModel *)messageModel {
    IMTableMessage *tableMessage = [IMSDKHelper convertBRMessageModelToIMTableMessage:messageModel];
    
    
    if ([[IMDataBaseManager shareInstance] isContainMessageWithMessageId:tableMessage.messageId]) {
        [self sendMessageReceive:tableMessage.serverMessageId];
        return;
    }
    else {
        [[IMDataBaseManager shareInstance] saveIMTableMessage:tableMessage];
    }
    
    IMTableMessage *relTableMessage = [[IMDataBaseManager shareInstance] getOneMessageByMessageId:tableMessage.messageId];
    IMMessage *message = [IMSDKHelper convertIMTableMessageToIMMessage:relTableMessage];
    
    //下载语音
    NSString *voiceType = [[message.filePath componentsSeparatedByString:@"."] lastObject];
    
    NSString *voiceStorePath = [[IMDocumentPath stringByAppendingPathComponent:IMVoiceFolder] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.%@",message.messageId,voiceType]];
    
    NSURLSessionDownloadTask *task = [HTTPRequest download:message.filePath progress:^(NSProgress *progress) {
        
    } destination:^NSURL *(NSURL *targetPath, NSURLResponse *response) {
        return [NSURL fileURLWithPath:voiceStorePath];
    } completion:^(NSURLResponse *defaultManager, NSURL *filePath, NSError *error) {
        NSLog(@"voice store path = %@",filePath);
        
        message.filePath = [filePath.path stringByReplacingOccurrencesOfString:IMDocumentPath withString:@""];
        //返回接收完成回执
        [self sendMessageReceive:relTableMessage.serverMessageId];
        //主线程中更新UI
        [self sendDelegate_didReceiveMessage:message];
    }];
    
    [task resume];
}

#pragma mark 问诊单问题
- (void)dealReceivedChatWzdQuestionMessage:(BRMessageModel *)messageModel {
    
    IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
    [self sendDelegate_didReceiveMessage:message];
}
#pragma mark 问诊单答案
- (void)dealReceivedChatWzdAnswerMessage:(BRMessageModel *)messageModel {
    
    IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
    [self sendDelegate_didReceiveMessage:message];
}
#pragma mark 复诊单问题
- (void)dealReceivedChatFzdQuestionMessage:(BRMessageModel *)messageModel {
    
    IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
    [self sendDelegate_didReceiveMessage:message];
}
#pragma mark 复诊单答案
- (void)dealReceivedChatFzdAnswerMessage:(BRMessageModel *)messageModel {
    
    IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
    [self sendDelegate_didReceiveMessage:message];
}
#pragma mark 补充问题问题
- (void)dealReceivedChatSupplementQuestionMessage:(BRMessageModel *)messageModel {
    
    IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
    [self sendDelegate_didReceiveMessage:message];
}
#pragma mark 补充问题答案
- (void)dealReceivedChatSupplementAnswerMessage:(BRMessageModel *)messageModel {
    
    IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
    [self sendDelegate_didReceiveMessage:message];
}
#pragma mark 交流界面系统消息
- (void)dealReceivedChatSystemMessage:(BRMessageModel *)messageModel {
    
    IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
    [self sendDelegate_didReceiveMessage:message];
}
#pragma mark  付费患者开启会话
- (void)dealReceivedChatStartChatPatientMessage:(BRMessageModel *)messageModel {
    
    IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
    if (message) {
        [self sendDelegate_didReceiveMessage:message];
    }
}

#pragma mark 医生开启会话
- (void)dealReceivedChatStartChatDoctorMessage:(BRMessageModel *)messageMdoel {
    
    IMMessage *message = [self dealReceivedNoFileMessage:messageMdoel];
    if (message) {
        [self sendDelegate_didReceiveMessage:message];
    }
}

#pragma mark 医生结束会话
- (void)dealReceivedChatFinishChatDoctorMessage:(BRMessageModel *)messageModel {
    
    IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
    if (message) {
        [self sendDelegate_didReceiveMessage:message];
    }
}

#pragma mark - 系统消息
- (void)dealReceivedSystemMessage:(BRMessageModel *)messageModel {
    NSString *type = messageModel.content.type;
    
    //完善个人信息推送的消息类型
    if ([type isEqualToString:@"UPDATE_PREFECT"]) {
        NSLog(@"收到完善个人信息推送类型=====");
        NSLog(@"update user info = %@",messageModel.content.userInfo);
        
        //更新医技服务费的开关 extra3为 1显示 0不显示
        if ([messageModel.content.content isEqualToString:@"app_ShowMedicalServiceFeeRule"]) {
//            [[UserManager shareInstance] updateApp_ShowMedicalServiceFeeRule:messageModel.content.extra3];
            [self sendMessageReceive:messageModel.serverMessageId];
            return;
        }
        
        [[IMDataBaseManager shareInstance]updateTemporaryPrescriptionPatientInfoWithPatientInfoDict:messageModel.content.userInfo];
        
        [self sendMessageReceive:messageModel.serverMessageId];
        
        //更新患者信息
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUPdatePatientInfo object:nil userInfo:messageModel.content.userInfo];
        //从服务器拉取更新联系人
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateContactListFromRemoteServer object:nil];
        //从服务器拉取更新消息列表
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateSessionListFromRemoteServer object:nil];
    }
    //好友添加成功消息
    else if ([type isEqualToString:@"SM003"]) {
        [self sendMessageReceive:messageModel.serverMessageId];
        //从服务器拉取更新联系
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateContactListFromRemoteServer object:nil];
        
        IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
        if (message) {
            [self sendDelegate_didReceiveMessage:message];
        }
    }
    //
    else {
        IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
        if (message) {
            [self sendDelegate_didReceiveMessage:message];
        }
    }
}

#pragma mark - 通知消息
- (void)dealReceivedNoticeMessage:(BRMessageModel *)messageModel {
    
    IMMessage *message = [self dealReceivedNoFileMessage:messageModel];
    //反馈处理
    if (message) {
        [self sendDelegate_didNoticeMessage:message];
    }
}
#pragma mark - 接收无附件消息处理
- (IMMessage *)dealReceivedNoFileMessage:(BRMessageModel *)messageModel {
    IMTableMessage *tableMessage = [IMSDKHelper convertBRMessageModelToIMTableMessage:messageModel];
    
    //如果消息已经存在 直接忽略
    if ([[IMDataBaseManager shareInstance] isContainMessageWithMessageId:tableMessage.messageId]) {
        [self sendMessageReceive:tableMessage.serverMessageId];
//        NSLog(@"messageId = %@数据表中已经存在，忽略",tableMessage.messageId);
        return nil;
    }
    
    //消息不存在进行保存
    else {
        BOOL rel = [[IMDataBaseManager shareInstance] saveIMTableMessage:tableMessage];
        
        if (!rel) {
            NSLog(@"messageId = %@消息保存失败",tableMessage.messageId);
            return nil;
        }
    }
    
    IMTableMessage *relTableMesssage = [[IMDataBaseManager shareInstance] getOneMessageByMessageId:tableMessage.messageId];
    
    IMMessage *message = [IMSDKHelper convertIMTableMessageToIMMessage:relTableMesssage];
    
    //发送接收完成回执
    [self sendMessageReceive:relTableMesssage.serverMessageId];
    
    return message;
}

#pragma mark- 历史消息
- (void)socketManagerDidSDKReceiveHistoryMessages:(BRMessagesModel *)messagesModel {
    
    NSMutableArray *messages = [NSMutableArray arrayWithCapacity:0];
    
    for (int i = 0; i < messagesModel.messages.count; i++) {
        BRMessageModel *messageModel = [messagesModel.messages objectAtIndex:i];
        IMMessage *message = [IMSDKHelper convertBRMessageModelToIMMessage:messageModel];
        if ([self filterMessageNotShow:message]) {
            continue;
        }
        [messages addObject:message];
    }
    
    [self sendDelegate_didHistoryUpdate:messages];
}

#pragma mark- 接收到服务器返回的已经发送的消息
- (void)socketManagerDidSDKReceiveSendedMessages:(BRMessagesModel *)messagesModel {
    
    if (messagesModel.messages.count == 0) {
        return;
    }
    
    BRMessageModel *messageModel = [messagesModel.messages objectAtIndex:0];
    
    NSDate *serverTimeDate = [NSDate dateWithString:messageModel.createdTime format:@"yyyy-MM-dd HH:mm:ss" timeZone:[NSTimeZone timeZoneWithName:@"GMT+0800"] locale:nil];
    
    BOOL rel = [[IMDataBaseManager shareInstance] updateIMTableMessageServerTime:serverTimeDate status:IMMessageStatusDeliverd serverMessageId:messageModel.serverMessageId messageId:messageModel.fromId];
    if (rel) {
        IMTableMessage *relTableMessage = [[IMDataBaseManager shareInstance] getOneMessageByMessageId:messageModel.fromId];
        //页面更新消息
        IMMessage *message = [IMSDKHelper convertIMTableMessageToIMMessage:relTableMessage];
        
        if (message.contentType == IMContentTypeImage) {
            //下载图片
            if (message.filePath) {
                [[SDWebImageDownloader sharedDownloader] downloadImageWithURL:[NSURL URLWithString:message.filePath] options:SDWebImageDownloaderHighPriority progress:^(NSInteger receivedSize, NSInteger expectedSize) {
                    
                } completed:^(UIImage *image, NSData *data, NSError *error, BOOL finished) {
                    //将下载图片缓存到本地磁盘
                    [[SDImageCache sharedImageCache] storeImage:image forKey:message.filePath toDisk:YES];
                    //更新图片消息
                    [self sendDelegate_didMessageUpdate:message];
                }];
            }
        }
        else if (message.contentType == IMContentTypeAudio) {
            //更新语音消息为本地位置
            message.filePath = relTableMessage.fileLocalPath;
            [self sendDelegate_didMessageUpdate:message];
        }
        else if (message.contentType == IMContentTypeText) {
            [self sendDelegate_didMessageUpdate:message];
        }
    }
}

#pragma mark - 消息撤回
//消息撤回
- (void)withdrawMessage:(NSString *)aMessageId completion:(void (^)(IMMessage *))handle{
    
    _withdrawHandle = handle;
    
    IMTableMessage *tableMessage = [[IMDataBaseManager shareInstance] getOneMessageByMessageId:aMessageId];
    
    //消息撤回
    NSDictionary *dictionary = @{
                                 @"code" : @"0023",
                                 @"messageId" : tableMessage.serverMessageId,
                                 @"fromId" : tableMessage.fromId,
                                 @"id" : tableMessage.messageId,
                                 @"userid" : [UserManager shareInstance].getUserId,
                                 @"otherUserId" : tableMessage.to
                                 };
    NSLog(@"消息撤回参数= %@",dictionary);
    
    [[SocketManager shareInstance] sendDataDict:dictionary];
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(15 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (_withdrawHandle) {
            _withdrawHandle(nil);
        }
    });
}

#pragma mark 接收到消息撤回返回状态
- (void)socketManagerDidSDKReceiveWithdrawedFromId:(NSString *)aFromId {
    //更新对应消息数据库中消息为 已撤回的消息
    BOOL rel = [[IMDataBaseManager shareInstance] updateIMTableMessageContentType:IMContentTypeMsgRevoke content:@"消息已经被撤回！！" byFromId:aFromId];
    
    if (rel) {
        //更新数据库成功  从数据库中获取新的对应消息
        IMTableMessage *tableMessage = [[IMDataBaseManager shareInstance] getOneMessageByFromId:aFromId];
        //更新对应的消息
        IMMessage *message = [IMSDKHelper convertIMTableMessageToIMMessage:tableMessage];
        
        _withdrawHandle(message);
    }
    else{
        _withdrawHandle(nil);
    }
    
    _withdrawHandle = nil;
}
#pragma mark - 获取指定的某条消息
- (IMMessage *)getOneMessageWithMessageId:(NSString *)aMessageId {
    IMTableMessage *tableMessage = [[IMDataBaseManager shareInstance] getOneMessageByMessageId:aMessageId];
    IMMessage *message = [IMSDKHelper convertIMTableMessageToIMMessage:tableMessage];
    return message;
}

- (IMSession *)getOneSessionWithSessionId:(NSString *)aSessionId {
    IMTableSession *tableSession = [[IMDataBaseManager shareInstance] getTableSessionWithUserId:aSessionId];
    IMSession *session = [IMSDKHelper convertIMTableSessionToIMSession:tableSession];
    return session;
}

#pragma mark - 会话管理

#pragma mark 从服务器更新最近会话
- (void)updateSessionListFromServer {
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:0];
    [dict setObject:BRApiGetLatestSessionsList forKey:@"code"];
    //时间改为最近一个月的
//    [dict setObject:[Config convertDateToFormatTime:[IMSDKHelper getLastUpdateSessionListTimeDate]] forKey:@"afterTime"];
//    [dict setObject:[Config convertDateToFormatTime:[[Config currentServerDate] dateBySubtractingMonths:1]] forKey:@"afterTime"];
    [dict setObject:[Config convertDateToFormatTime:[[Config currentServerDate] dateBySubtractingDays:15]] forKey:@"afterTime"];
    [dict setObject:@"100" forKey:@"maxSize"];
    
    [[SocketManager shareInstance] sendDataDict:dict];
}

#pragma mark 接收到的最近消息会话列表
- (void)socketManagerDidSDKReceiveSessionLists:(BRSessionListModel *)sessionLisetModel {
    
//    NSLog(@"session list = %@",sessionLisetModel);
    
    BOOL flag = YES;
    
    for (int i = 0; i < sessionLisetModel.list.count; i++) {
        BRSessionListItemModel *itemModel = [sessionLisetModel.list objectAtIndex:i];
        IMTableSession *tableSession = [IMSDKHelper convertBRSessionListItemModelToIMTableSession:itemModel];
        
        if ([tableSession.sessionId isEqualToString:@""] || !tableSession.sessionId) {
            continue;
        }
        IMTableMessage *tableMessage = [[IMDataBaseManager shareInstance] getLastOneMessageBySessionId:tableSession.sessionId];
        
        //如果会话有对应的最后一条消息 则更新到会话表中
        if (tableMessage) {
            //最后一条消息
            tableSession.messageId = tableMessage.messageId;
            tableSession.contentType = tableMessage.contentType;
            tableSession.content = tableMessage.content;
            tableSession.messageStatus = tableMessage.status;
            tableSession.messageUpdateTime = tableMessage.serverTime;
        }
        
        
        //如果最后一条消息为需要过滤的消息 则不显示
        if ([[Config shareInstance].app_colseWelcome isEqualToString:@"1"] && ([tableMessage.content hasSuffix:@"我刚在必然中医关注了您。"] || [tableMessage.content hasSuffix:@"添加您为好友"]) ) {
            
            continue;
        }
        
        
        //状态
        ChatStatusType chatStatusType = ChatStatusTypeNone;
        
        if ([itemModel.props objectForKey:@"chatState"] && ![[itemModel.props objectForKey:@"chatState"] isEqualToString:@""]) {
            tableSession.chatStatusType = (ChatStatusType)[[itemModel.props objectForKey:@"chatState"] integerValue];
        }
        
        BOOL rel = [[IMDataBaseManager shareInstance] isContainSessionWithSessionId:tableSession.sessionId];
        
        if (rel) {
            [[IMDataBaseManager shareInstance] updateIMTableSession:tableSession];
        }
        else {
            [[IMDataBaseManager shareInstance] saveIMTableSession:tableSession];
        }
        
        //更新状态
        if (chatStatusType != ChatStatusTypeNone) {
            [[IMDataBaseManager shareInstance] updateIMTableSessionChatStatusBySessionId:tableSession.sessionId chatStatus:chatStatusType];
        }
    }
    
    //获取会话列表
    NSArray *sessionsArray = [self loadAllSessionListFromDataBase];
    //更新会话列表
    [self sendDelegate_didSessionsUpdate:sessionsArray];
}

#pragma mark 获取会话列表
- (NSArray *)loadAllSessionListFromDataBase {
    NSArray *tableSessionArray = [[IMDataBaseManager shareInstance] getAllSessions];
    
    NSMutableArray *sessions = [NSMutableArray arrayWithCapacity:0];
    
    for (int i = 0; i< tableSessionArray.count; i++) {
        IMTableSession *tableSession = [tableSessionArray objectAtIndex:i];
        
        //获取会话对应用户的头像
        tableSession.headImgUrl = [[IMDataBaseManager shareInstance] getIMContactHeadImgUrlByRosterUserId:tableSession.sessionId];
        
        //系统消息
        if ([tableSession.sessionId isEqualToString:kSystemId]) {
            tableSession.name = @"系统消息";
        }
        //小然客服
        else if ([tableSession.sessionId isEqualToString:kXiaoRanId]) {
            tableSession.name = @"客服小然";
            tableSession.headImgUrl = kXiaoRanHeadImg;
        }
        
        IMSession *session = [IMSDKHelper convertIMTableSessionToIMSession:tableSession];
        
        [sessions addObject:session];
    }
    
    NSArray *sortedArray = [sessions sortedArrayUsingComparator:^NSComparisonResult(IMSession * session1, IMSession* session2) {
        NSDate *date1 = [session1.messageDate isLaterThanOrEqualTo:session1.draftDate] ? session1.messageDate : session1.draftDate;
        NSDate *date2 = [session2.messageDate isLaterThanOrEqualTo:session2.draftDate] ? session2.messageDate : session2.draftDate;
        
        if ([date1 isLaterThanOrEqualTo:date2]) {
            return NSOrderedAscending;
        }
        else {
            return NSOrderedDescending;
        }
    }];
    
    NSArray *sorted2Array = [sortedArray sortedArrayUsingComparator:^NSComparisonResult(IMSession* session1, IMSession* session2) {
        int count1 = [session1 unReadMessageCount];
        int count2 = [session2 unReadMessageCount];
        
        if(count1 == 0 && count2 > 0){
            return NSOrderedDescending;
        }else if (count1 > 0 && count2 == 0){
            return NSOrderedAscending;
        }
        return NSOrderedAscending;
    }];
    
    return sorted2Array;
}

#pragma mark - 接收更新通知
- (void)notificationUpdateSessionList {
    NSArray *sessionsArray = [self loadAllSessionListFromDataBase];
    
    //更新会话列表
    [self sendDelegate_didSessionsUpdate:sessionsArray];
    
}
#pragma mark - 搜索 本地会话
#pragma mark 本地会话
- (NSArray *)searchSessionsByPatientName:(NSString *)keywords {
    NSArray *tableSessionArray = [[IMDataBaseManager shareInstance] getSessionByMatchName:keywords];
    
//    NSMutableArray *sessions = [NSMutableArray arrayWithCapacity:0];
//
//    for (int i = 0; i< tableSessionArray.count; i++) {
//        IMTableSession *tableSession = [tableSessionArray objectAtIndex:i];
//        //获取会话对应用户的头像
//        tableSession.headImgUrl = [[IMDataBaseManager shareInstance] getIMContactHeadImgUrlByRosterUserId:tableSession.sessionId];
//
//        IMSession *session = [IMSDKHelper convertIMTableSessionToIMSession:tableSession];
//        [sessions addObject:session];
//    }
//
//    return sessions;
    return [self getheadImgUrlByTableSessons:tableSessionArray];
}

- (NSArray *)searchSessionsByChatStatusType:(ChatStatusType)chatStatusType {
    NSArray *tableSessionArray = [[IMDataBaseManager shareInstance] getSessionByMatchChatStatus:chatStatusType];
    
    return [self getheadImgUrlByTableSessons:tableSessionArray];
}

- (NSArray *)searchSessionsByPatientName:(NSString *)keywords chatStatusType:(ChatStatusType)chatStatusType {
    NSArray *tableSessionArray = [[IMDataBaseManager shareInstance] getSessionByMathName:keywords chatStatus:chatStatusType];
    
    return [self getheadImgUrlByTableSessons:tableSessionArray];
}

//- (NSArray *)searchSessionByMessageListFuzzyMatchingWithKeyworkds:(NSString *)keywords {
//    NSArray *matchSessionArray = []
//    
//}


- (NSArray *)getheadImgUrlByTableSessons:(NSArray *)tableSessionArray {
    NSMutableArray *sessions = [NSMutableArray arrayWithCapacity:0];
    
    for (int i = 0; i< tableSessionArray.count; i++) {
        IMTableSession *tableSession = [tableSessionArray objectAtIndex:i];
        //获取会话对应用户的头像
        tableSession.headImgUrl = [[IMDataBaseManager shareInstance] getIMContactHeadImgUrlByRosterUserId:tableSession.sessionId];
        
        IMSession *session = [IMSDKHelper convertIMTableSessionToIMSession:tableSession];
        [sessions addObject:session];
    }
    return sessions;
}


#pragma mark - 删除本地会话
- (BOOL)deleteSessionWithSessionId:(NSString *)aSessionId {
    return [[IMDataBaseManager shareInstance] deleteIMSessionBySessionId:aSessionId];
}

#pragma mark - 从服务器更新未读消息列表
- (void)updateUnReadMessagesFromServer {
//    NSString *timeString = [Config convertDateToFormatTime:[[Config currentServerDate] dateBySubtractingMonths:1]];
    NSString *timeString = [Config convertDateToFormatTime:[[Config currentServerDate] dateBySubtractingDays:15]];
    NSDictionary *dictionary = @{
                                 @"method_code" : @"000274",
                                 @"afterTime" : timeString
                                 };
    
    [HTTPRequest POST:kServerDomain parameters:dictionary progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
//        NSLog(@"未读消息 == %@",responseObject);
        NSString *code = [responseObject objectForKey:@"code"];
        
        if ([code isEqualToString:@"0000"]) {
            
            NSArray *msgItems = [responseObject objectForKey:@"items"];
            
            for (int j = 0; j < msgItems.count; j++) {
                NSDictionary *item = [msgItems objectAtIndex:j];
                NSArray *messages = [item objectForKey:@"messages"];
                
                for (int i = (messages.count - 1); i >= 0; i--) {
                    NSDictionary *dict = [messages objectAtIndex:i];
                    BRMessageModel *messageModel = [BRMessageModel mj_objectWithKeyValues:dict];
                    //处理消息
                    [self dealReceivedChatMessage:messageModel];
                }
            }
        }
        else{
            NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
            NSLog(@"000274  errorMsg = %@",errorMsg);
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"从服务器获取未读消息失败");
    }];
}

#pragma mark - 消息发送失败
- (void)socketManagerDidSDKSendMessageFailed:(NSString *)aFromId {
    [self dealWithMessageSendFaildWithFromId:aFromId];
}

- (void)dealWithMessageSendFaildWithFromId:(NSString *)aFromId {
    BOOL rel = [[IMDataBaseManager shareInstance] updateIMTableMessageWithFromId:aFromId status:IMMessageStatusFailure];
    //更新UI界面
    if (rel) {
        IMTableMessage *tableMessage = [[IMDataBaseManager shareInstance] getOneMessageByFromId:aFromId];
        IMMessage *message = [IMSDKHelper convertIMTableMessageToIMMessage:tableMessage];
        
        [self sendDelegate_didMessageUpdate:message];
    }
}
#pragma mark - 更新所有的发送中消息为发送失败
- (BOOL)updateAllDeliveringMessageToDeliveringFailed {
    return [[IMDataBaseManager shareInstance] updateAllIMTableMessageSendDeliveringToSendFailed];
}

#pragma mark - 更新所有未读消息为已读
- (BOOL)updateAllUnReadMessagesToLocalRead {
    return [[IMDataBaseManager shareInstance] updateAllUnReadMessageToReaded];
}

#pragma mark - 请求开始接收消息失败
- (void)socketManagerDidRequestReceiveMessageFailed {
    
}

#pragma mark - 接收消息发送已经接收  private
- (void)sendMessageReceive:(NSString *)aMessageId {
    if (aMessageId) {
        NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:0];
        [dict setObject:BRApiMessageReceiveSuccess forKey:@"code"];
        [dict setObject:aMessageId forKey:@"messageid"];
        [dict setObject:[[UserManager shareInstance] getUserId] forKey:@"messageTargetUserId"];
        
        [[SocketManager shareInstance] sendDataDict:dict];
    }
}

- (void)sendDelegate_didReceiveMessage:(IMMessage *)message {
    dispatch_async(dispatch_get_main_queue(), ^{
        for (id<IMSessionManagerDelegate> delegate in [self.delegateSet copy]) {
            if ([delegate respondsToSelector:@selector(didReceiveMessage:)]) {
                [delegate didReceiveMessage:message];
            }
        }
    });
}

- (void)sendDelegate_didMessageUpdate:(IMMessage *)message {
    dispatch_async(dispatch_get_main_queue(), ^{
        for (id<IMSessionManagerDelegate> delegate in [self.delegateSet copy]) {
            if ([delegate respondsToSelector:@selector(didMessageUpdate:)]) {
                [delegate didMessageUpdate:message];
            }
        }
    });
}

- (void)sendDelegate_didSessionsUpdate:(NSArray *)array {
    dispatch_async(dispatch_get_main_queue(), ^{
        for (id<IMSessionManagerDelegate> delegate in [self.delegateSet copy]) {
            if ([delegate respondsToSelector:@selector(didSessionsUpdate:)]) {
                [delegate didSessionsUpdate:array];
            }
        }
    });
}

- (void)sendDelegate_didHistoryUpdate:(NSArray *)array {
    dispatch_async(dispatch_get_main_queue(), ^{
        for (id<IMSessionManagerDelegate> delegate in [self.delegateSet copy]) {
            if ([delegate respondsToSelector:@selector(didReceiveHistoryMessages:)]) {
                [delegate didReceiveHistoryMessages:array];
            }
        }
    });
}

- (void)sendDelegate_didNoticeMessage:(IMMessage *)message {
    dispatch_async(dispatch_get_main_queue(), ^{
        for (id<IMSessionManagerDelegate> delegate in [self.delegateSet copy]) {
            if ([delegate respondsToSelector:@selector(didReceiveNoticeMessage:)]) {
                [delegate didReceiveNoticeMessage:message];
            }
        }
    });
}
#pragma mark - 消息过滤
//拉取到的历史消息过滤
//返回YES 则不显示
- (BOOL)filterMessageNotShow:(IMMessage *)message {
    if (message.contentType == IMContentTypeTextFzd ||
        message.contentType == IMContentTypeTextWzd ||
        message.contentType == IMContentTypePrescriptImg ||
        message.contentType == IMContentTypeFeeCon ||
        message.contentType == IMContentTypeFeeSuccess ||
        message.contentType == IMContentTypeWelcomeDoctor ||
        message.contentType == IMContentTypeNoticeText ||
        message.contentType == IMContentTypeDoctorWorkPlan ||
        message.contentType == IMContentTypeTextPerfected ||
        message.contentType == IMContentTypeWorkPay ||
        message.contentType == IMContentTypeTextPay
        ) {
        return YES;
    }
    else if (message.contentType == IMContentTypeSystem && [message.from isEqualToString:[UserManager shareInstance].getUserId]) {
        return YES;
    }
    else if ((message.contentType == IMContentTypeStartChatPatient ||
              message.contentType == IMContentTypeStartChatDoctor ||
              message.contentType == IMContentTypeFinishChatDoctor) &&
             [message.from isEqualToString:[UserManager shareInstance].getUserId]) {
        return YES;
    }
    else if ((message.contentType == IMContentTypeWZDQuestion ||
             message.contentType == IMContentTypeWZDAnswer ||
             message.contentType == IMContentTypeFZDQuestion ||
             message.contentType == IMContentTypeFZDAnswer
//           ||   message.contentType == IMContentTypeSupplementQuestion
             || message.contentType == IMContentTypeSupplementAnswer
              ) &&
             [message.from isEqualToString:[UserManager shareInstance].getUserId]
             ) {
        return YES;
    }
//    //显示这些类型的消息
//    else if ((message.contentType != IMContentTypeText) &&
//             (message.contentType != IMContentTypeImage) &&
//             (message.contentType != IMContentTypeAudio) &&
//             (message.contentType != IMContentTypeWZDQuestion) &&
//             (message.contentType != IMContentTypeWZDAnswer) &&
//             (message.contentType != IMContentTypeFZDQuestion) &&
//             (message.contentType != IMContentTypeFZDAnswer) &&
//             (message.contentType != IMContentTypeSupplementQuestion) &&
//             (message.contentType != IMContentTypeSupplementAnswer) &&
//             (message.contentType != IMContentTypeStartChatDoctor) &&
//             (message.contentType != IMContentTypeStartChatPatient) &&
//             (message.contentType != IMContentTypeFinishChatDoctor) &&
//             (message.contentType != IMContentTypeSystem) &&
//             (message.contentType != IMContentTypeMsgRevoke) &&
//             (message.contentType != IMContentTypeSM0003)
//             ) {
//        return NO;
//    }
    
    else {
        return NO;
    }
}
//保存到数据库之前过滤
- (BOOL)filterMessageModelNotDeal:(BRMessageModel *)messageModel {
    [self sendMessageReceive:messageModel.serverMessageId];
    
    if ([messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeTextFzd]] ||
        [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeTextWzd]] ||
        [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypePrescriptImg]] ||
        [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeFeeCon]] ||
        [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeFeeSuccess]] ||
        [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeWelcomeDoctor]] ||
        [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeNoticeText]] ||
        [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeDoctorWorkPlan]] ||
        [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeTextPerfected]] ||
        [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeWorkPay]] ||
        [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeTextPay]]
        ) {
        
        return YES;
    }
    else if (([messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeWZDQuestion]] ||
              [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeWZDAnswer]] ||
              [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeFZDQuestion]] ||
              [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeFZDAnswer]] ||
              [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeSupplementQuestion]] ||
              [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeSupplementAnswer]]) &&
             [messageModel.from isEqualToString:[UserManager shareInstance].getUserId]
             ) {
        return YES;
    }
    else if ([messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeSystem]] &&
             [messageModel.from isEqualToString:[UserManager shareInstance].getUserId]) {
        return YES;
    }
    //医生开启会话  医生结束会话 付费患者开启会话
    else if (([messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeStartChatDoctor]] ||
             [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeStartChatPatient]] ||
             [messageModel.content.type isEqualToString:[kIMContentTypeArray objectAtIndex:IMContentTypeFinishChatDoctor]]) &&
             [messageModel.from isEqualToString:[UserManager shareInstance].getUserId]
             ) {
        return YES;
    }
    
    return NO;
}

@end
