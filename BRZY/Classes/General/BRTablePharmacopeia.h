//
//  BRTablePharmacopeia.h
//  BRZY
//
//  Created by  xujiangtao on 2017/12/26.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface BRTablePharmacopeia : NSObject

@property (nonatomic, copy) NSString      *detailId;//id用于查询详情
@property (nonatomic, copy) NSString      *drugName;//名称
@property (nonatomic, copy) NSString      *secondName;//第二性状
@property (nonatomic, copy) NSString      *fristLeter;//首字母
@property (nonatomic, copy) NSString      *spell;//简拼
@property (nonatomic, copy) NSString      *disableDrug;//不宜配伍的药
@property (nonatomic, copy) NSString      *pregnantDisable;//孕妇禁忌 (是 否 慎)
@property (nonatomic, copy) NSString      *isHavePoison;//是否有毒  否 大 中 小
@property (nonatomic, copy) NSString      *decoctMin;//煎汤内服最小用量
@property (nonatomic, copy) NSString      *decoctMax;//煎汤内服最大用量
@property (nonatomic, copy) NSString      *decoctMethod;//煎汤内服方法
@property (nonatomic, copy) NSString      *pillsMin;//入丸及其他内服最小用量
@property (nonatomic, copy) NSString      *pillsMax;//入丸及其他内服最大用量
@property (nonatomic, copy) NSString      *pillsNum;//入丸及其他内服最大次数
@property (nonatomic, copy) NSString      *pillsMethod;//入丸及其他内服方法
@property (nonatomic, copy) NSString      *drugStandard;//颗粒的剂量，比如最小剂量为3g一包
@property (nonatomic, copy) NSString      *charge;//颗粒单位的价钱

@end
