//
//  HTTPRequest.m
//  BRZY
//
//  Created by  <PERSON><PERSON>jiangta<PERSON> on 2017/8/22.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "HTTPRequest.h"
#import "AFNetworking.h"
#import "Config.h"

@implementation HTTPRequest

+ (void)GET:(NSString *)URLString parameters:(NSDictionary *)parameters progress:(requestProgressBlock)progress success:(requestSuccessBlock)success failure:(requestErrorBlock)failure {
    
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    manager.requestSerializer = [AFHTTPRequestSerializer serializer];
    manager.responseSerializer.acceptableContentTypes = [NSSet setWithObjects:@"application/json", @"text/plain",@"text/json", @"text/javascript", nil];
    manager.requestSerializer.timeoutInterval = 10;
    
    //添加公共参数
    NSMutableDictionary *paramsDict = [self getCommonParams:parameters];
    
    if ([UserManager shareInstance].getTelephone) {
        [paramsDict setObject:[UserManager shareInstance].getTelephone forKey:@"tel"];
    }
    
    NSLog(@"parames dict = %@",paramsDict);
    
    [manager GET:URLString parameters:parameters progress:^(NSProgress * _Nonnull downloadProgress) {
        progress(downloadProgress);
    } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        success(task,responseObject);
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        failure(task,error);
    }];
}

+ (NSURLSessionTask *)POST:(NSString *)URLString parameters:(NSDictionary *)parameters progress:(requestProgressBlock)progress success:(requestSuccessBlock)success failure:(requestErrorBlock)failure {

    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    manager.requestSerializer = [AFHTTPRequestSerializer serializer];
    manager.responseSerializer.acceptableContentTypes = [NSSet setWithObjects:@"application/json", @"text/plain",@"text/json", @"text/javascript", nil];
    manager.requestSerializer.timeoutInterval = 10;
    
    //添加公共参数
    NSMutableDictionary *paramsDict = [self getCommonParams:parameters];
    
    if ([UserManager shareInstance].getTelephone && ![[UserManager shareInstance].getTelephone isEqualToString:@""]) {
        [paramsDict setObject:[UserManager shareInstance].getTelephone forKey:@"tel"];
    }
    
    NSMutableString *requestString = [[NSMutableString alloc] initWithString:URLString];
    
   BOOL flag = YES;

    NSArray *allkeys = paramsDict.allKeys;
    for (NSString *key in allkeys) {
        NSString *value = [paramsDict objectForKey:key];
        [requestString appendString:[NSString stringWithFormat:@"%@%@=%@",(flag?@"?":@"&"),key,value]];
        flag = NO;
    }
    
    NSLog(@"%@",requestString);
    
    NSURLSessionTask *task = [manager POST:URLString parameters:paramsDict progress:^(NSProgress * _Nonnull uploadProgress) {
        if (progress) {
            progress(uploadProgress);
        }
    } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        if (success) {
            success(task,responseObject);
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if (failure) {
            failure(task,error);
        }
    }];
    
    return task;
}

+ (NSURLSessionTask *)POST:(NSString *)URLString parameters:(NSDictionary *)parameters constructingBodyWithBlock:(constructingBodyBlock)formDataBlock progress:(requestProgressBlock)progress success:(requestSuccessBlock)success failure:(requestErrorBlock)failure {
    
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    manager.requestSerializer = [AFHTTPRequestSerializer serializer];
    manager.responseSerializer.acceptableContentTypes = [NSSet setWithObjects:@"application/json", @"text/plain",@"text/json", @"text/javascript", nil];
    manager.requestSerializer.timeoutInterval = 10;
    
    //添加公共参数
    NSMutableDictionary *paramsDict = [self getCommonParams:parameters];
    
    if ([UserManager shareInstance].getTelephone) {
        [paramsDict setObject:[UserManager shareInstance].getTelephone forKey:@"tel"];
    }
    
    NSLog(@"%@  parames dict = %@",URLString,paramsDict);
    
    NSURLSessionTask *task = [manager POST:URLString parameters:paramsDict constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
        if (formDataBlock) {
            formDataBlock(formData);
        }
    } progress:^(NSProgress * _Nonnull uploadProgress) {
        if (progress) {
            progress(uploadProgress);
        }
    } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        if (success) {
            success(task,responseObject);
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if (failure) {
            failure(task,error);
        }
    }];
    
    return task;
}

+ (NSURLSessionDownloadTask *)download:(NSString *)URLString progress:(requestProgressBlock)progress destination:(destinationBlock)destination completion:(completionHandleBlock)completion {
    
    NSURLSessionConfiguration *configuration = [NSURLSessionConfiguration defaultSessionConfiguration];
    
    AFURLSessionManager *manager = [[AFURLSessionManager alloc] initWithSessionConfiguration:configuration];
    
    NSURL *URL = [NSURL URLWithString:URLString];
    NSURLRequest *request = [NSURLRequest requestWithURL:URL];
    
    NSURLSessionDownloadTask *downloadTask = [manager downloadTaskWithRequest:request progress:^(NSProgress * downloadProgress) {
        if (progress) {
            progress(downloadProgress);
        }
    } destination:^NSURL *(NSURL * targetPath, NSURLResponse * response) {
        return destination(targetPath,response);
    } completionHandler:^(NSURLResponse * response, NSURL * filePath, NSError * error) {
        if (completion) {
            completion(response,filePath,error);
        }
    }];
    
    return downloadTask;
}

+(NSURLSessionDataTask*)HttpPostRequsetWithUrl:(NSString*)urlAdress
                                        params:(NSDictionary*)dict
                                          file:(NSData*)file
                                      progress:(HttpRequestProgress)progressBlock
                                        sucess:(HttpRequestSucess)sucessBlock
                                         error:(HttpRequestError)errorBlock
{
    
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    manager.requestSerializer = [AFHTTPRequestSerializer serializer];
    manager.responseSerializer.acceptableContentTypes = [NSSet setWithObjects:@"application/json", @"text/plain",@"text/json", @"text/javascript", nil];
    manager.requestSerializer.timeoutInterval = 10;

    //添加公共参数
    NSMutableDictionary *paramsDict = [self getCommonParams:dict];
    
    if ([UserManager shareInstance].getTelephone) {
        [paramsDict setObject:[UserManager shareInstance].getTelephone forKey:@"tel"];
    }
    
    NSURLSessionDataTask *task = [manager POST:urlAdress parameters:paramsDict constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
        if (file) {
            NSString *type = [paramsDict objectForKey:@"resourceType"];
            if ([type isEqualToString:@"image"]) {
                [formData appendPartWithInputStream:[[NSInputStream alloc] initWithData:file] name:@"resourceData" fileName:@"head.jpg" length:file.length mimeType:@"image/jpeg"];
            }else if ([type isEqualToString:@"voice"]){
                [formData appendPartWithInputStream:[[NSInputStream alloc] initWithData:file] name:@"resourceData" fileName:@"voice.amr" length:file.length mimeType:@"audio/AMR"];
            }
        }
    } progress:^(NSProgress * _Nonnull uploadProgress) {
        NSLog(@"%f", uploadProgress.completedUnitCount/(CGFloat)uploadProgress.totalUnitCount);
        if (progressBlock) {
            progressBlock(uploadProgress);
        }
    } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        NSLog(@"method %@ respond is %@",[paramsDict objectForKey:@"method_code"], responseObject);
        if (sucessBlock) {
            sucessBlock(responseObject, task);
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        NSLog(@"%@ Error: %@",paramsDict, error);
        if (errorBlock) {
            errorBlock(error, task);
        }
    }];
    
    return task;
}

#pragma mark - 公共参数

+ (NSMutableDictionary *)getCommonParams:(NSDictionary *)dict {
    NSMutableDictionary *paramsDict = [NSMutableDictionary dictionaryWithDictionary:dict];
    [paramsDict setObject:[UIApplication sharedApplication].appVersion forKey:@"app_version"];
    [paramsDict setObject:[UIDevice currentDevice].identifierForVendor.UUIDString forKey:@"imei"];
    [paramsDict setObject:[Config uuid] forKey:@"mac"];
    [paramsDict setObject:@"2" forKey:@"sys_type"];
    [paramsDict setObject:@"1" forKey:@"userType"];
    [paramsDict setObject:[UserManager shareInstance].getUserId forKey:@"userId"];
    [paramsDict setObject:@"biran" forKey:@"terminalType"];
    //添加sessionToken
    if (![[Config shareInstance].sessionToken isEqualToString:@""]) {
        [paramsDict setObject:[Config shareInstance].sessionToken forKey:@"sessionToken"];
    }
    //添加调试参数
    NSString *dispatchTo = [Config getDebugParamsDisPatchto];
    if (dispatchTo) {
        [paramsDict setObject:dispatchTo forKey:@"dispatchTo"];
    }
    return paramsDict;
}

@end
