//
//  IMMessage.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON>ta<PERSON> on 2017/8/28.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMMessage.h"

@interface IMMessage ()

@end

@implementation IMMessage

- (NSString *)finalLinkUrl {
    
    NSString *doctorId = [UserManager shareInstance].getUserId;
    
    NSString *telStr = [UserManager shareInstance].getTelephone;
    NSString *openId = @"";
    
    //问诊单 问题
    if (_contentType == IMContentTypeWZDQuestion) {
        //新版
        if (_wzdVersion) {
            NSDictionary *dict = @{
                                   @"docId" : doctorId,
                                   @"userId" : _from,
                                   @"flag" : @"0",
                                   @"wzdId" : _wzdId,
                                   @"wzdType": _wzdType,
                                   @"version" : _wzdVersion
                                   };
            return [Config generateUrlWithServerDomain:_linkUrl params:dict];
        }
        //旧版
        else{
            NSString *urlStr = [NSString stringWithFormat:@"%@wxSubmit/goWenzd?flag=0&diff=wzd&tel=%@&openId=%@&wzdId=%@",kDomain,telStr,openId,_wzdId];
            NSRange range = [_wzdId rangeOfString:@"卍1卍"];
            //过渡版
            if (range.location != NSNotFound) {
                urlStr = [NSString stringWithFormat:@"%@wxSubmit/goWenzdNew?flag=0&tel=%@&openId=%@&wzdParam=%@",kDomain,telStr,openId,_wzdId];
            }
            
            return urlStr;
        }
    }
    //问诊单 答案
    else if (_contentType == IMContentTypeWZDAnswer) {
        //新版
        if (_wzdVersion) {
            NSDictionary *dict = @{
                                   @"docId" : doctorId,
                                   @"userId" : _from,
                                   @"flag" : @"0",
                                   @"wzdId" : _wzdId,
                                   @"wzdType": _wzdType,
                                   @"version" : _wzdVersion
                                   };
            return [Config generateUrlWithServerDomain:_linkUrl params:dict];
        }
        //旧版
        else{
            NSString *urlStr = [NSString stringWithFormat:@"%@appQuestion/SeePatientAnswer?flag=0&wzdAnswerId=%@",kDomain,_wzdId];
            
            if ([urlStr hasSuffix:@"tabIndex"]) {
                //过渡版地址
                urlStr = [NSString stringWithFormat:@"%@wxSubmit/skimFinishFirstReport?docTel=%@&openId=%@&flag=0&wzdId=%@",kDomain,telStr,openId,_wzdId];
            }
            
            return urlStr;
        }

    }
    //复诊单 问题
    else if (_contentType == IMContentTypeFZDQuestion) {
        //新版
        if (_wzdVersion) {
            NSDictionary *dict = @{
                                   @"docId" : doctorId,
                                   @"userId" : _from,
                                   @"flag" : @"0",
                                   @"wzdId" : _wzdId,
                                   @"wzdType": _wzdType,
                                   @"version" : _wzdVersion,
                                   @"takerId" : _extra3
                                   };
            return [Config generateUrlWithServerDomain:_linkUrl params:dict];
        }
        //旧版
        else{
            NSString *urlStr = [NSString stringWithFormat:@"%@wxSubmit/goWenzd?flag=0&diff=zhsf&tel=%@&openId=%@&wzdId=%@",kDomain,telStr,openId,_wzdId];
            
            NSRange range = [_wzdId rangeOfString:@"卍1卍"];
            NSRange range2 = [_wzdId rangeOfString:@"$1$"];
            
            if (range.location != NSNotFound || range2.location != NSNotFound) {
                urlStr = [NSString stringWithFormat:@"%@wxSubmit/sendReturnVisitFromPage?flag=0&docTel=%@&openId=%@&wzdParam=%@",kDomain,telStr,openId,_wzdId];
            }
            return urlStr;
        }
    }
    //复诊单 答案
    else if (_contentType == IMContentTypeFZDAnswer) {
        //新版
        if (_wzdVersion) {
            NSDictionary *dict = @{
                                   @"docId" : doctorId,
                                   @"userId" : _from,
                                   @"flag" : @"0",
                                   @"answerId" : _wzdId,
                                   @"wzdType": _wzdType,
                                   @"version" : _wzdVersion
                                   };
            return [Config generateUrlWithServerDomain:_linkUrl params:dict];
        }
        //旧版
        else {
            NSString *urlStr = [NSString stringWithFormat:@"%@appQuestion/ShowZhsfAnswer?flag=0&zhsfId=%@",kDomain,_wzdId];
            
            if ([urlStr hasSuffix:@"tabIndex"]) {
                urlStr = [NSString stringWithFormat:@"%@wxSubmit/skimFinishReturnVisitForm?flag=0&docTel=%@&openId=%@&answerId=%@",kDomain,telStr,openId,_wzdId];
            }
            return urlStr;
        }
        
    }
    //补充问题 问题
    else if (_contentType == IMContentTypeSupplementQuestion) {
        NSDictionary *dict = @{
                               @"docId" : doctorId,
                               @"userId" : _to,
                               @"flag" : @"0",
                               @"groupId" : _wzdId
                               };
        return [Config generateUrlWithServerDomain:_linkUrl params:dict];
    }
    //补充问题 答案
    else if (_contentType == IMContentTypeSupplementAnswer) {
        NSDictionary *dict = @{
                               @"answerId" : _wzdId,
                               @"docId" : doctorId,
                               @"userId" : _from,
                               @"flag" : @"0",
                               @"wzdType": _wzdType,
                               @"version" : _wzdVersion
                               };
        return [Config generateUrlWithServerDomain:_linkUrl params:dict];
    }
    //交流界面系统消息
    else if (_contentType == IMContentTypeSystem) {
        //物流信息  _extra1 即订单号不为空
        if (_openType == IMMessageOpenTypeLogistics && ![_extra1 isEqualToString:@""]) {
            // 使用新的URL拼接规则
            return [NSString stringWithFormat:@"%@wxSubmit/delivery?flag=0&preId=%@", kDomain, _extra1];
        }
        
    }
    return @"";
}

+ (IMMessage *)createTextSendMessgeWithText:(NSString *)text toUserId:(NSString *)userId{
    IMMessage *message = [[IMMessage alloc] init];
    
    message.messageId = [Utils createMessageId];
    message.from = [[UserManager shareInstance] getUserId];
    message.to = userId;
    message.fromId = message.messageId;
    message.sessionType = IMSessionTypeOneToOne;
    message.contentType = IMContentTypeText;
    message.sessionId = userId;//使用接收方的id为会话id
    message.status = IMMessageStatusDelivering;
    message.serverTime = [Config currentServerDate];
    
    message.text = text;
    
    return message;
}

+ (IMMessage *)createImageSendMessageWithData:(NSData *)data toUserId:(NSString *)userId {
    IMMessage *message = [[IMMessage alloc] init];
    
    message.messageId = [Utils createMessageId];
    message.from = [[UserManager shareInstance] getUserId];
    message.to = userId;
    message.fromId = message.messageId;
    message.sessionType = IMSessionTypeOneToOne;
    message.contentType = IMContentTypeImage;
    message.sessionId = userId;
    message.status = IMMessageStatusDelivering;
    message.serverTime = [Config currentServerDate];
    
    //保存图片到沙盒
    NSString *relatePath = [IMImageFolder stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.jpg",[Utils currentTimestamp]]];
    
    message.filePath = relatePath;
    
    NSString *finalPath = [IMDocumentPath stringByAppendingPathComponent:relatePath];
    
    BOOL rel = [data writeToFile:finalPath atomically:YES];
    if (rel) {
        NSLog(@"创建图片消息写入图片到本地成功");
    }
    else{
        NSLog(@"创建图片消息写入图片到本地失败");
    }
    
    return message;
}

+ (IMMessage *)createAudioSendMessageWithPath:(NSString *)audioPath length:(NSInteger)length toUserId:(NSString *)userId {
    IMMessage *message = [[IMMessage alloc] init];
    
    message.messageId = [Utils createMessageId];
    message.from = [[UserManager shareInstance] getUserId];
    message.to = userId;
    message.fromId = message.messageId;
    message.sessionType = IMSessionTypeOneToOne;
    message.contentType = IMContentTypeAudio;
    message.sessionId = userId;
    message.status = IMMessageStatusDelivering;
    message.serverTime = [Config currentServerDate];
    message.filePath = audioPath;
    message.length = length;
    
    return message;
}

+ (IMMessage *)createDoctorStartSendMessageToUserId:(NSString *)userId {
    IMMessage *message = [[IMMessage alloc] init];
    
    message.messageId = [Utils createMessageId];
    message.from = [[UserManager shareInstance] getUserId];
    message.to = userId;
    message.fromId = message.messageId;
    message.sessionType = IMSessionTypeOneToOne;
    message.contentType = IMContentTypeStartChatDoctor;
    message.sessionId = userId;
    message.status = IMMessageStatusDelivering;
    message.serverTime = [Config currentServerDate];
    
    message.text = @"与患者建立连接，开始会话。\n结束后，请点击\"结束会话\"";
    
    return message;
}

+ (IMMessage *)createDoctorFinishSendMessageToUserId:(NSString *)userId {
    IMMessage *message = [[IMMessage alloc] init];
    
    message.messageId = [Utils createMessageId];
    message.from = [UserManager shareInstance].getUserId;
    message.to = userId;
    message.fromId = message.messageId;
    message.sessionType = IMSessionTypeOneToOne;
    message.contentType = IMContentTypeFinishChatDoctor;
    message.sessionId = userId;
    message.status = IMMessageStatusDelivering;
    message.serverTime = [Config currentServerDate];
    
    message.text = @"本次会话已结束";
    
    return message;
}

+ (IMMessage *)createSupplementQuestionMessageWithGroupId:(NSString *)groupId toUserId:(NSString *)userId {
    IMMessage *message = [[IMMessage alloc] init];
    
    message.wzdId = groupId;
    
    message.messageId = [Utils createMessageId];
    message.from = [UserManager shareInstance].getUserId;
    message.to = userId;
    message.fromId = message.messageId;
    message.sessionType = IMSessionTypeOneToOne;
    message.contentType = IMContentTypeSupplementQuestion;
    message.sessionId = userId;
    message.status = IMMessageStatusDelivering;
    message.serverTime = [Config currentServerDate];
    message.linkUrl = [NSString stringWithFormat:@"%@wxWzd/toSupplementaryProblems",kDomain];
    
    message.text = @"请认真填写定制问诊单，这对我的判断很重要。";
    
    return message;
}

#pragma mark - 如果为null 则处理为@""

- (NSString *)linkUrl {
    if (!_linkUrl) {
        return @"";
    }
    else {
        return _linkUrl;
    }
}

- (NSString *)extra1 {
    if (!_extra1) {
        return @"";
    }
    else {
        return _extra1;
    }
}

- (NSString *)extra2 {
    if (!_extra2) {
        return @"";
    }
    else {
        return _extra2;
    }
}

- (NSString *)extra3 {
    if (!_extra3) {
        return @"";
    }
    else {
        return _extra3;
    }
}

- (NSString *)wzdType {
    if (!_wzdType) {
        return @"";
    }
    else {
        return _wzdType;
    }
}

@end
