//
//  IMTableMessage.h
//  BRZY
//
//  Created by  <PERSON>ujiangta<PERSON> on 2017/9/5.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface IMTableMessage : NSObject

@property (copy, nonatomic) NSString *loginUserId;//当前登录的用户id

@property (assign, nonatomic) int localId;
@property (copy, nonatomic) NSString *messageId;
@property (nonatomic) IMMessageType messageType;
@property (nonatomic) IMContentType contentType;    //内容类型
@property (copy, nonatomic) NSString *content;      //消息内容
//@property (strong, nonatomic) NSDate *createTime;   //创建时间 本地时间
@property (strong, nonatomic) NSDate *serverTime;   //服务器时间
@property (copy, nonatomic) NSString *sessionId;    //消息所属会话id
@property (nonatomic) IMSessionType sessionType;    //会话类型（预留）
@property (copy, nonatomic) NSString *fromId;       //发送方的消息id
@property (copy, nonatomic) NSString *from;         //发送方id
@property (copy, nonatomic) NSString *to;           //接收方id
@property (nonatomic) int voiceLength;              //语音长度
@property (copy, nonatomic) NSString *linkUrl;      //问诊单等相关链接 url

@property (copy, nonatomic) NSString *title;    //标题
//附加信息
@property (copy, nonatomic) NSString *extra1;
@property (copy, nonatomic) NSString *extra2;
@property (copy, nonatomic) NSString *extra3;
@property (copy, nonatomic) NSString *version;
@property (copy, nonatomic) NSString *wzdType;

//交流页面系统消息 字段
//@property (copy, nonatomic) NSString *orderNumber;  //订单号
//@property (copy, nonatomic) NSString *logisticNumber; //物流单号
@property (assign, nonatomic) IMMessageOpenType openType; //打开目标类型

//服务器返回消息id
@property (copy, nonatomic) NSString *serverMessageId;
//消息发送状态
@property (nonatomic) IMMessageStatus status;
//是否已读
@property (nonatomic) BOOL isLocalRead;             //收到的消息接收方是否读
@property (nonatomic) BOOL isRemoteRead;            //发送的消息对方是否已读
//消息代发
@property (copy, nonatomic) NSString *proxyId;      //代发消息方id
@property (copy, nonatomic) NSString *proxyName;    //代发消息方姓名
//附件地址
@property (copy, nonatomic) NSString *fileRemotePath;     //附件远程地址
@property (copy, nonatomic) NSString *fileLocalPath;        //附件本地地址
//附加信息
@property (copy, nonatomic) NSString *serverId;
@property (copy, nonatomic) NSString *serverIp;



@end
