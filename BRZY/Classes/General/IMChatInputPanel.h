//
//  IMChatInputPanel.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/11.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <UIKit/UIKit.h>

@class IMChatInputPanel;
NS_ASSUME_NONNULL_BEGIN

@protocol IMChatInputPanelDelegate <NSObject>

@required
- (void)inputPanel:(IMChatInputPanel *)inputPanel willChangeHeight:(CGFloat)height duration:(NSTimeInterval)duration animationCurve:(int)animationCurve;

@end

@interface IMChatInputPanel : UIView

@property (nullable, nonatomic, weak) id<IMChatInputPanelDelegate> delegate;

- (void)endInputting:(BOOL)animated;
- (void)adjustForSize:(CGSize)size keyboardHeight:(CGFloat)keyboardHeight duration:(NSTimeInterval)duration animationCurve:(int)animationCurve;
- (void)changeToSize:(CGSize)size keyboardHeight:(CGFloat)keyboardHeight duration:(NSTimeInterval)duration;

@end

NS_ASSUME_NONNULL_END
