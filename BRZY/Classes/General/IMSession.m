//
//  IMSession.m
//  BRZY
//
//  Created by  xujiangtao on 2017/9/4.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMSession.h"
#import "IMDataBaseManager.h"
#import "IMSDKHelper.h"
#import "IMMessage.h"
#import "SDImageCache.h"
#import "IMTableMessage.h"
#import "SocketManager.h"
#import "IMTableContact.h"

@interface IMSession ()

//如果chatSet为1 即打开消息免打扰时 必须有dialogId时才能发送消息  如果chatSet为0  即没有打开免打扰 则不用打开会话 281
@property (copy, nonatomic) NSString *dialogId;
@property (copy, nonatomic) NSString *chatSet;

@end

@implementation IMSession

//是否已经开启会话
- (BOOL)isOpenDialog {
    if ([_dialogId integerValue] > 0) {
        return YES;
    }else{
        return NO;
    }
}

- (NSArray *)loadMorePreMessageFromDate:(NSDate *)aDate limit:(int)aLimit {
    NSMutableArray *messages = [NSMutableArray arrayWithCapacity:0];
    
    NSArray *tableMessagesArray = [[IMDataBaseManager shareInstance] getPreIMTableMessagesWithSessionId:self.sessionId serverTime:aDate limit:aLimit];
    
    for (int i = 0; i < tableMessagesArray.count; i++) {
        IMTableMessage *tableMessage = [tableMessagesArray objectAtIndex:i];
        
        IMMessage *message = [IMSDKHelper convertIMTableMessageToIMMessage:tableMessage];
        
        //获取本地图片
        if (message.contentType == IMContentTypeImage) {
        }
        //文本
        else if (message.contentType == IMContentTypeText) {
            if (!message.text || [message.text isEqualToString:@""]) {
                continue;
            }
        }
//        //语音 设置本地path
//        else if (message.contentType == IMContentTypeAudio) {
//            message.filePath = tableMessage.fileLocalPath;
//        }
        
        [messages addObject:message];
    }
    
    return messages;
}

- (void)loadMorePreMessageRemoteFromId:(NSString *)aMessageId limit:(int)aLimit {
    NSMutableDictionary *dictionary = [NSMutableDictionary dictionaryWithCapacity:0];
    [dictionary setObject:_sessionId forKey:@"otherUserId"];
    [dictionary setObject:aMessageId forKey:@"messageid"];
    [dictionary setObject:@(aLimit) forKey:@"size"];
    [dictionary setObject:BRApiGetMessageHistoryList forKey:@"code"];
    
    [[SocketManager shareInstance] sendDataDict:dictionary];
}

- (NSInteger)unReadMessageCount {
    return [[IMDataBaseManager shareInstance] getLocalUnReadMessageCountWithSessionId:_sessionId];
}

- (void)updateSessionStatusCompletion:(void (^)(BOOL))completion {
    //调用 000282 接口 获取dialogId 和 chatSet(是否开启免打扰)
    NSDictionary *dict = @{
                           @"method_code" : @"000282",
                           @"patientId" : self.sessionId
                           };
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            _chatSet = [responseObject objectForKey:@"chatSet"];
            _dialogId = [responseObject objectForKey:@"dialogId"];
            
            if ([_chatSet isEqualToString:@"1"]) {
                //打开免打扰
                completion(YES);
            }else{
                completion(NO);
            }
        }else{
            NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
            NSLog(@"000282 error = %@",errorMsg);
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"000282 api 查询会话状态失败 = %@",error.description);
    }];
    
}

- (void)sessionStartCompletion:(void (^)(BOOL))completion {
    //000281 打开会话
    if ([_dialogId isEqualToString:@""] || !_dialogId) {
        //如果dialogId 则打开会话
        NSDictionary *dict = @{
                               @"method_code" : @"000281",
                               @"patientId" : self.sessionId,
                               @"fee" : @"0"
                               };
        [HTTPRequest POST:kServerDomain parameters:dict progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
            NSString *code = [responseObject objectForKey:@"code"];
            if ([code isEqualToString:@"0000"]) {
                _dialogId = [responseObject objectForKey:@"dialogId"];
                if ([_dialogId integerValue] > 0) {
                    completion(YES);
                }else{
                    completion(NO);
                }
            }else{
                NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
                NSLog(@"000281 error = %@",errorMsg);
            }
        } failure:^(NSURLSessionDataTask *task, NSError *error) {
            NSLog(@"000281 api reuqest failed = %@",error.description);
        }];
    }
}

- (void)sessionEndCompletion:(void (^)(BOOL, NSString *))completion {
    //000283 关闭会话
    NSDictionary *dict = @{
                           @"method_code" : @"000283",
                           @"dialogId" : self.dialogId
                           };
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            _dialogId = 0;
            NSLog(@"000283 close dialog successful");
            completion(YES,nil);
        }else{
            NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
            NSLog(@"000283 error = %@",errorMsg);
            completion(NO,errorMsg);
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"000283 api reuqest failed = %@",error.description);
        completion(NO,error.description);
    }];
}

- (void)sessionInfoCompletion:(void (^)(NSString *, NSString *, NSString *, BOOL))completion {
    //000293 获取会话信息
    NSDictionary *dict = @{
                           @"method_code" : @"000293",
                           @"dialogId" : self.dialogId
                           };
    [HTTPRequest POST:kServerDomain parameters:dict progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            NSString *whoOpen = [responseObject objectForKey:@"whoOpen"];
            NSString *openIds = [responseObject objectForKey:@"orderIds"];
            NSString *prePayMoney = [responseObject objectForKey:@"prePayMoney"];
            completion(whoOpen,openIds,prePayMoney,YES);
        }
        else {
            NSString *errorMeg = [responseObject objectForKey:@"errorMsg"];
            NSLog(@"000293 error msg = %@",errorMeg);
            completion(nil,nil,nil,NO);
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"000293 api request failed = %@",error.description);
        completion(nil,nil,nil,NO);
    }];
}

- (BOOL)updateMessageToLocalReaded:(NSString *)aMessageId {
    return [[IMDataBaseManager shareInstance] updateOneIMTableMessageLocalReadToReaded:aMessageId];
}

- (BOOL)updateAllMessageToLocalReaded {
    //重置该会话对应的所有消息id为已读
    BOOL rel = [[IMDataBaseManager shareInstance] updateSessionIMTableMessageLocalReadToReadedWithSessionId:_sessionId];
    if (rel) {
        NSLog(@"update session %@ all message to read successful",_sessionId);
    }else{
        NSLog(@"update session %@ all message to read failed",_sessionId);
    }
    return rel;
}

- (BOOL)deleteAllMessageFromDataBase {
    return [[IMDataBaseManager shareInstance] deleteAllIMTableMessageWithSessionId:_sessionId];
}

- (IMMessage *)getRetrySendMessageByMessageId:(NSString *)aMessageId {
    
    IMTableMessage *tableMessage = [[IMDataBaseManager shareInstance] getOneMessageByMessageId:aMessageId];
    //设置MessageId、fromId等为最新的
    tableMessage.messageId = [Utils createMessageId];
    tableMessage.fromId = tableMessage.messageId;
    //设置发送状态为发送中
    tableMessage.status = IMMessageStatusDelivering;
    //设置为当前时间
    tableMessage.serverTime = [Config currentServerDate];
    
    return [IMSDKHelper convertIMTableMessageToIMMessage:tableMessage];
}

- (BOOL)deleteMessageFromDataBaseWithMessageId:(NSString *)aMessageId {
    return [[IMDataBaseManager shareInstance] deleteIMTableMessageWithMessageId:aMessageId];
}

- (IMMessage *)getOneMessageWithMessageId:(NSString *)aMessagId {
    IMTableMessage *tableMessage = [[IMDataBaseManager shareInstance] getOneMessageByMessageId:aMessagId];
    return [IMSDKHelper convertIMTableMessageToIMMessage:tableMessage];
}

- (NSString *)headImgUrl {
    if (!_headImgUrl) {
        _headImgUrl = @"";
    }
    return _headImgUrl;
}
//草稿
- (NSString *)draftContent {
    NSString *draftContent = [[IMDataBaseManager shareInstance] getIMTableSessionDraftContentBySessionId:_sessionId];
    if (!draftContent) {
        draftContent = @"";
    }
    return draftContent;
}

- (void)setDraftContent:(NSString *)draftContent {
    NSDate *draftDate = [Config currentServerDate];
    if ([draftContent isEqualToString:@""]) {
        draftDate = nil;
    }
    BOOL rel = [[IMDataBaseManager shareInstance] updateIMTableSessionDraftContentBySessionId:_sessionId draftContent:draftContent draftDate:draftDate];
    if (rel) {
        NSLog(@"更新草稿成功");
    }
    else {
        NSLog(@"更新草稿失败");
    }
}
- (NSString *)remark
{
    NSString *remark = [[IMDataBaseManager shareInstance] getIMTableContactRemarkWithRosterUserId:_sessionId];
    if (!remark) {
        remark = @"";
    }
    return remark;
}

+ (IMSession *)createIMSessionWithUserId:(NSString *)aUserId name:(NSString *)aNickName headImageUrl:(NSString *)aHeadImageUrl {
    IMSession *session = [[IMSession alloc] init];
    
    session.sessionId = aUserId;
    session.name = aNickName;
    session.headImgUrl = aHeadImageUrl;
    
    return session;
}
@end
