//
//  IMTableMessage.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/5.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMTableMessage.h"
#import "IMTableMessage+WCTTableCoding.h"

@implementation IMTableMessage

WCDB_IMPLEMENTATION(IMTableMessage)

// 修改WCDB_SYNTHESIZE的用法，适配WCDB 2.1.10版本，移除类名作为参数
WCDB_SYNTHESIZE(loginUserId)

WCDB_SYNTHESIZE(localId)
WCDB_SYNTHESIZE(messageId)
WCDB_SYNTHESIZE(messageType)
WCDB_SYNTHESIZE(contentType)
WCDB_SYNTHESIZE(content)
//WCDB_SYNTHESIZE(createTime)
WCDB_SYNTHESIZE(serverTime)
WCDB_SYNTHESIZE(sessionId)
WCDB_SYNTHESIZE(sessionType)
WCDB_SYNTHESIZE(fromId)
WCDB_SYNTHESIZE_COLUMN(from, "fromUserId")
WCDB_SYNTHESIZE_COLUMN(to, "toUserId")
WCDB_SYNTHESIZE(serverId)
WCDB_SYNTHESIZE(serverIp)
WCDB_SYNTHESIZE(proxyId)
WCDB_SYNTHESIZE(isLocalRead)
WCDB_SYNTHESIZE(isRemoteRead)
WCDB_SYNTHESIZE(proxyName)
WCDB_SYNTHESIZE(fileRemotePath)
WCDB_SYNTHESIZE(fileLocalPath)
WCDB_SYNTHESIZE(status)
WCDB_SYNTHESIZE(serverMessageId)
WCDB_SYNTHESIZE(voiceLength)
WCDB_SYNTHESIZE(linkUrl)
//WCDB_SYNTHESIZE(orderNumber)
//WCDB_SYNTHESIZE(logisticNumber)
WCDB_SYNTHESIZE(openType)

WCDB_SYNTHESIZE(title)
WCDB_SYNTHESIZE(extra1)
WCDB_SYNTHESIZE(extra2)
WCDB_SYNTHESIZE(extra3)
WCDB_SYNTHESIZE(version)
WCDB_SYNTHESIZE(wzdType)

//localId 主键 自增
WCDB_PRIMARY_AUTO_INCREMENT(localId)

//message Id 唯一
WCDB_UNIQUE(messageId)
WCDB_UNIQUE(serverMessageId)

//非空
WCDB_NOT_NULL(localId)
WCDB_NOT_NULL(messageId)
WCDB_NOT_NULL(contentType)
WCDB_NOT_NULL(sessionId)


- (NSString *)loginUserId {
    return [UserManager shareInstance].getUserId;
}

//如果为null 则处理为@""
- (NSString *)linkUrl {
    if (!_linkUrl) {
        return @"";
    }
    else {
        return _linkUrl;
    }
}

- (NSString *)extra1 {
    if (!_extra1) {
        return @"";
    }
    else {
        return _extra1;
    }
}

- (NSString *)extra2 {
    if (!_extra2) {
        return @"";
    }
    else {
        return _extra2;
    }
}

- (NSString *)extra3 {
    if (!_extra3) {
        return @"";
    }
    else {
        return _extra3;
    }
}

- (NSString *)content {
    if (!_content) {
        return @"";
    }
    else {
        return _content;
    }
}

- (NSString *)serverMessageId {
    if (!_serverMessageId) {
        return @"";
    }
    else {
        return _serverMessageId;
    }
}
@end
