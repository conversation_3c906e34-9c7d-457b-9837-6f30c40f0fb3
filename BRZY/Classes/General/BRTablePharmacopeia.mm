//
//  BRTablePharmacopeia.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/12/26.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRTablePharmacopeia.h"
#import "BRTablePharmacopeia+WCTTableCoding.h"

@implementation BRTablePharmacopeia

WCDB_IMPLEMENTATION(BRTablePharmacopeia)

// 修改WCDB_SYNTHESIZE的用法，适配WCDB 2.1.10版本，移除类名作为参数
WCDB_SYNTHESIZE(detailId)
WCDB_SYNTHESIZE(drugName)
WCDB_SYNTHESIZE(secondName)
WCDB_SYNTHESIZE(fristLeter)
WCDB_SYNTHESIZE(spell)
WCDB_SYNTHESIZE(disableDrug)
WCDB_SYNTHESIZE(pregnantDisable)
WCDB_SYNTHESIZE(isHavePoison)
WCDB_SYNTHESIZE(decoctMin)
WCDB_SYNTHESIZE(decoctMax)
WCDB_SYNTHESIZE(decoctMethod)
WCDB_SYNTHESIZE(pillsMin)
WCDB_SYNTHESIZE(pillsMax)
WCDB_SYNTHESIZE(pillsNum)
WCDB_SYNTHESIZE(pillsMethod)
WCDB_SYNTHESIZE(drugStandard)
WCDB_SYNTHESIZE(charge)

// 主键定义也需要相应修改
WCDB_PRIMARY(detailId)

@end
