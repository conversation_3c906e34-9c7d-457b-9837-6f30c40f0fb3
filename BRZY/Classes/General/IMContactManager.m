//
//  IMContactManager.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/28.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMContactManager.h"
#import "IMContact.h"
#import "BRContactModel.h"
#import "IMSDKHelper.h"
#import "IMTableContact.h"
#import "IMDataBaseManager.h"
#import "BRContactModel.h"
#import "PatientInfoModel.h"

static IMContactManager *contactManager = nil;

@interface IMContactManager ()

@property (strong, nonatomic) NSMutableSet *delegateSet;

@end

@implementation IMContactManager

+ (instancetype)shareInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        contactManager = [[IMContactManager alloc] init];
    });
    return contactManager;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        //从服务器中更新联系人列表
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateContactListFromServer) name:kIMNotificationUpdateContactListFromRemoteServer object:nil];
        //从本地数据库中更新联系人信息
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateContactListFromDataBase) name:kIMNotificationUpdateContactListFromDataBase object:nil];
    }
    return self;
}

- (NSMutableSet *)delegateSet {
    if (!_delegateSet) {
        _delegateSet = [NSMutableSet set];
    }
    return _delegateSet;
}

- (void)addDelegate:(id<IMContactManagerDelegate>)delegate {
    [self.delegateSet addObject:delegate];
}

- (void)removeDelegate:(id<IMContactManagerDelegate>)delegate {
    [self.delegateSet removeObject:delegate];
}

//拉取更新联系人信息
- (void)updateContactListFromServer {
    NSDate *lastUpdate = [IMSDKHelper getLastUpdateContactListTimeDate];
    NSString *lastUpdateTimeString = [Config convertDateToFormatTime:lastUpdate];
    
    NSDictionary *dictionary = @{
                                 @"method_code" : @"000266",
                                 @"startTime" : lastUpdateTimeString
                                 };
    
    [HTTPRequest POST:kServerDomain parameters:dictionary progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            //拉取成功
            NSArray *contactList = [responseObject objectForKey:@"items"];
            NSArray *blacklistIds = [responseObject objectForKey:@"blacklistIds"];
            
            [Config storeBlackListArray:blacklistIds];
            
            long rosterCount = [[responseObject objectForKey:@"rosterCount"] longValue];
            [Config shareInstance].rosterCount = rosterCount;
            
            if (contactList.count > 0) {
                NSArray *contactModelArray = [BRContactModel mj_objectArrayWithKeyValuesArray:contactList];
                //循环转换为tableContact类型并设置首字母
                for (BRContactModel *contactModel in contactModelArray) {
                    IMTableContact *tableContact = [IMSDKHelper convertBRContactModelToIMTableContact:contactModel];
                    
                    //设置firstSpell 忽略服务器传回来的firstSpell
                    NSString *nickname = [tableContact.nickName stringByTrim];
                    
//                    if (nickname.length > 0) {
//                        NSString *firstLetter = [nickname substringToIndex:1];
//                        NSString *firstCharactor = [Utils firstCharactor:firstLetter];
//                        //匹配
//                        NSString *regex = @"^[a-zA-Z]";
//                        NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES %@",regex];
//                        //判断 正常字符
//                        if ([predicate evaluateWithObject:firstCharactor]) {
//                            tableContact.firstSpell = firstCharactor;
//                        }
//                        else {
//                            tableContact.firstSpell = @"|";
//                        }
//                    }
//                    else {
//                        tableContact.nickName = @"";
//                        tableContact.firstSpell = @"|";
//                    }
                    
                    BOOL isContain = [[IMDataBaseManager shareInstance] isContainContactWithRosterUserId:tableContact.rosterUserId];
                    
                    if (isContain) {
                        [[IMDataBaseManager shareInstance] updateIMTableContact:tableContact];
                    }
                    else {
                        [[IMDataBaseManager shareInstance] saveIMTableContact:tableContact];
                    }
                    
                }
                //更新联系人
                //保存成功 更新联系人列表ui
                [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateContactListFromDataBase object:nil];
                //更新会话列表 主要刷新头像
                [[NSNotificationCenter defaultCenter] postNotificationName:kIMNotificationUpdateSessionListFromDataBase object:nil];
                //保存到数据库成功并且数据大于1  才会保存时间戳
                if (contactList.count > 1) {
                    [IMSDKHelper storeUpdateContactListTime:[Config currentServerDate]];
                }
            }
            
            if (blacklistIds.count > 0) {
                //从联系人和会话表中清除黑名单用户
                [[IMDataBaseManager shareInstance] deleteIMContactBlackListByRosterUserIdArray:blacklistIds];
                [[IMDataBaseManager shareInstance] deleteIMSessionBlackListBySessionIdArray:blacklistIds];
            }
        }
        else{
            NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
            NSLog(@"update contact list server errormsg = %@",errorMsg);
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        //拉取失败
        NSLog(@"update contact list error = %@",error.description);
    }];
}

- (void)saveToDB:(IMTableContact *)tableContact {
    //获取并保存首字母
    if (!tableContact.firstSpell && tableContact.nickName.length > 0) {
        NSString *firstLetter = [tableContact.nickName substringToIndex:1];
        NSString *firstCharactor = [Utils firstCharactor:firstLetter];
        
        tableContact.firstSpell = firstCharactor;
    }
    
    //如果已经存在 则更新 不存在则保存
    IMDataBaseManager *dbManager = [IMDataBaseManager shareInstance];
    
    [dbManager saveIMTableContact:tableContact];
}

- (void)saveToDBWithArray:(NSArray *)aContacts {
    
}

- (void)getAllContactList:(void (^)(NSArray *, NSArray *))contactsInfo {
    
    IMDataBaseManager *dbManager = [IMDataBaseManager shareInstance];
    
    //获取除了小然客服以外的所有联系人distinct以后的首字母数组
    NSMutableArray *letterArray = [NSMutableArray arrayWithArray:[dbManager getIMContactFirstCharactors]];
    if (letterArray.count > 0) {
        
        NSMutableArray *contactsGroupArray = [NSMutableArray arrayWithCapacity:0];
        //根据联系人首字母分别获取每个首字母对应的联系人数组  与首字母数组顺序对应
        for (int i = 0; i < letterArray.count; i++) {
            NSString *letter = [letterArray objectAtIndex:i];
            NSArray *tableContactsArray = [dbManager getIMContactObjectByFirstCharactor:letter];
            NSMutableArray *groupArray = [NSMutableArray arrayWithCapacity:0];
            //将数组中包含的IMTableContact对象转为IMContact
            for (int j = 0; j < tableContactsArray.count; j++) {
                IMTableContact *tableContact = [tableContactsArray objectAtIndex:j];
                
                IMContact *contact = [IMSDKHelper convertIMTableContactToIMContact:tableContact];
                [groupArray addObject:contact];
            }
            [contactsGroupArray addObject:groupArray];
        }
        
        //更换 "|" 为 "#"
        if ([letterArray containsObject:@"|"]) {
            int index = (int)[letterArray indexOfObject:@"|"];
            
            if (index >= 0) {
                [letterArray replaceObjectAtIndex:index withObject:@"#"];
            }
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            contactsInfo(letterArray,contactsGroupArray);
        });
    }
    else {
        dispatch_async(dispatch_get_main_queue(), ^{
            contactsInfo(@[],@[]);
        });
    }
}

- (IMContact *)getXiaoRanInfo {
    IMTableContact *tableContact = [[IMDataBaseManager shareInstance] getIMContactByRosterUserId:kXiaoRanId];
    IMContact *contact = [IMSDKHelper convertIMTableContactToIMContact:tableContact];
    return contact;
}

- (IMContact *)getIMContactWithPatientId:(NSString *)aPatientId {
    IMTableContact *tableContact = [[IMDataBaseManager shareInstance] getIMContactByRosterUserId:aPatientId];
    IMContact *contact = [IMSDKHelper convertIMTableContactToIMContact:tableContact];
    return contact;
}

- (void)getIMContactWithPatientId:(NSString *)aPatientId completion:(void (^)(IMContact *))completion {
    
    IMTableContact *tableContact = [[IMDataBaseManager shareInstance] getIMContactByRosterUserId:aPatientId];
    
    if (tableContact) {
        IMContact *contact = [IMSDKHelper convertIMTableContactToIMContact:tableContact];
        completion(contact);
    }
    //从服务器获取数据
    else {
        NSDictionary *dict = @{
                               @"method_code" : @"000213",
                               @"patientId" : aPatientId
                               };
        
        [HTTPRequest POST:kServerDomain parameters:dict progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
            PatientInfoModel *infoModel = [PatientInfoModel mj_objectWithKeyValues:responseObject];
            
            if ([infoModel.code isEqualToString:@"0000"]) {
                IMContact *contact = [[IMContact alloc] init];
                contact.patientId = aPatientId;
                contact.age = infoModel.age;
                contact.gender = infoModel.sex;
                contact.nickname = infoModel.name;
                contact.headImgUrl = infoModel.handImgUrl;
                
                completion(contact);
            }
            else {
                completion(nil);
            }
        } failure:^(NSURLSessionDataTask *task, NSError *error) {
            completion(nil);
        }];
    }
}
#pragma mark - 更新联系人信息
- (void)updateContactListFromDataBase {
    [self getAllContactList:^(NSArray *letterArray, NSArray *contactsArray) {
        [self sendDelegate_didReceiveAllContactsLetterArray:letterArray contactsArray:contactsArray];
    }];
}

#pragma mark 从服务器搜索用户
- (void)searchPatientsByPatientName:(NSString *)keywords searchType:(NSString *)tag completion:(void (^)(NSString *, NSArray *))handle failed:(void (^)(NSString *))failed {
    NSDictionary *dictionary = @{
                                 @"method_code" : @"000265",
                                 @"keyword" : keywords,
                                 @"searchType":tag
                                 };
    
    [HTTPRequest POST:kServerDomain parameters:dictionary progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            NSArray *items = [responseObject objectForKey:@"items"];
            if (items.count > 0) {
                
                NSArray *contactModelsArray = [BRContactModel mj_objectArrayWithKeyValuesArray:items];
                
                NSMutableArray *contactArray = [NSMutableArray arrayWithCapacity:0];
                for (int i = 0; i < contactModelsArray.count; i++) {
                    BRContactModel *model = [contactModelsArray objectAtIndex:i];
                    IMContact *contact = [IMSDKHelper convertBRContactModelToIMContact:model];
                    [contactArray addObject:contact];
                }
                handle(nil,contactArray);
            }else{
                handle(nil,[NSArray array]);
            }
        }else{
            NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
            handle(errorMsg,nil);
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        failed(error.description);
    }];
}
#pragma mark - 黑名单
- (void)addContactToBlackListByPatientId:(NSString *)patientId completion:(void (^)(BOOL))handle {
    NSDictionary *dictionary = @{
                                 @"method_code" : @"000258",
                                 @"patientId" : patientId,
                                 @"optType" : @"1"
                                 };
    
    [HTTPRequest POST:kServerDomain parameters:dictionary progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            handle(YES);
        }else{
            NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
            handle(NO);
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"000258 failed = %@",error.description);
        handle(NO);
    }];
}

#pragma mark- 更新到UI界面
- (void)sendDelegate_didReceiveAllContactsLetterArray:(NSArray *)lettersArray contactsArray:(NSArray *)aContacts {
    dispatch_async(dispatch_get_main_queue(), ^{
        for (id<IMContactManagerDelegate> delegate in [self.delegateSet copy]) {
            if ([delegate respondsToSelector:@selector(didReceiveAllContact:letters:)]) {
                [delegate didReceiveAllContact:aContacts letters:lettersArray];
            }
        }
    });
}
@end
