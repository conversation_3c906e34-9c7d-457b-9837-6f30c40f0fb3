//
//  BRSessionRosterUserModel.h
//  BRZY
//
//  Created by  xujiangtao on 2017/12/19.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface BRSessionRosterUserModel : NSObject

@property (copy, nonatomic) NSString *patientId;    //替换 id

@property (copy, nonatomic) NSString *name;
@property (copy, nonatomic) NSString *plainPasssword;
@property (copy, nonatomic) NSString *telephone;
@property (copy, nonatomic) NSString *status;
@property (copy, nonatomic) NSString *thirdType;
@property (copy, nonatomic) NSString *userType;
@property (copy, nonatomic) NSString *username;

@property (strong, nonatomic) NSArray *uniqueValues;

@end
