//
//  UIFont+Util.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/27.
//  Copyright © 2017年 <PERSON> YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "UIFont+Util.h"

@implementation UIFont (Util)

+ (UIFont *)br_navigationFont {
    return [UIFont systemFontOfSize:19];
}

+ (UIFont *)br_textFont {
    return [UIFont systemFontOfSize:16];
}

+ (UIFont *)br_titleFont {
    return [UIFont systemFontOfSize:18];
}

+ (UIFont *)br_assistFont {
    return [UIFont systemFontOfSize:19];
}

+ (UIFont *)br_mainFont {
    return [UIFont systemFontOfSize:17];
}

+ (UIFont *)br_promptFont {
    return [UIFont systemFontOfSize:12];
}

+ (UIFont *)br_dialogFont {
    return [UIFont systemFontOfSize:15];
}

@end
