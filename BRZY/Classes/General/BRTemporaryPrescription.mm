//
//  BRTemporaryPrescription.m
//  BRZY
//
//  Created by <PERSON><PERSON><PERSON> on 2018/4/28.
//  Copyright © 2018年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRTemporaryPrescription.h"
#import "BRTemporaryPrescription+WCTTableCoding.h"

@implementation BRTemporaryPrescription

WCDB_IMPLEMENTATION(BRTemporaryPrescription)

// 修改WCDB_SYNTHESIZE的用法，适配WCDB 2.1.10版本，移除类名作为参数
WCDB_SYNTHESIZE(primaryKey)
WCDB_SYNTHESIZE(loginUserId)
WCDB_SYNTHESIZE(patientId)
WCDB_SYNTHESIZE(prescriptionStr)

// 主键定义也需要相应修改
WCDB_PRIMARY(primaryKey)

- (NSString *)loginUserId {
    return [UserManager shareInstance].getUserId;
}

@end
