//
//  BRTencentOCRRequest.m
//  BRZY
//
//  Created by 许江涛 on 2024/11/26.
//  Copyright © 2024 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRTencentOCRRequest.h"
#import <CommonCrypto/CommonHMAC.h>
#import "BRTencentCloudAPISignature.h"

@implementation BRTencentOCRRequest

// TencentOCRRequest.m 中实现
+ (void)generalHandwritingOCRWithImage:(UIImage *)image
                             secretId:(NSString *)secretId
                            secretKey:(NSString *)secretKey
                           completion:(void(^)(NSDictionary *response, NSError *error))completion {
    // 基础配置
    NSString *host = @"ocr.tencentcloudapi.com";
    NSString *service = @"ocr";
    NSString *version = @"2018-11-19";
    NSString *action = @"GeneralHandwritingOCR";
    NSTimeInterval timestamp = [[NSDate date] timeIntervalSince1970];
    
    // 将图片转换为Base64
    NSData *imageData = UIImageJPEGRepresentation(image, 0.8);
    NSString *base64Image = [imageData base64EncodedStringWithOptions:0];
    
    // 构建请求体
    NSDictionary *payloadDict = @{@"ImageBase64": base64Image};
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:payloadDict
                                                      options:0
                                                        error:&error];
    if (error) {
        if (completion) {
            completion(nil, error);
        }
        return;
    }
    
    NSString *payload = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    
    // 生成签名
    NSString *authorization = [BRTencentCloudAPISignature generateSignatureWithSecretId:secretId
                                                                       secretKey:secretKey
                                                                        service:service
                                                                        payload:payload
                                                                           host:host
                                                                         action:action
                                                                      timestamp:timestamp
                                                                       version:version];
    
    // 构建请求
    NSString *urlString = [NSString stringWithFormat:@"https://%@", host];
    NSURL *url = [NSURL URLWithString:urlString];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:url];
    
    // 设置请求头
    [request setHTTPMethod:@"POST"];
    [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    [request setValue:authorization forHTTPHeaderField:@"Authorization"];
    [request setValue:[NSString stringWithFormat:@"%ld", (long)timestamp]
        forHTTPHeaderField:@"X-TC-Timestamp"];
    [request setValue:version forHTTPHeaderField:@"X-TC-Version"];
    [request setValue:@"ap-guangzhou" forHTTPHeaderField:@"X-TC-Region"];
    [request setValue:action forHTTPHeaderField:@"X-TC-Action"];
    [request setValue:host forHTTPHeaderField:@"Host"];
    
    // 设置请求体
    [request setHTTPBody:[payload dataUsingEncoding:NSUTF8StringEncoding]];
    
    // 发送请求
    NSURLSession *session = [NSURLSession sharedSession];
    NSURLSessionDataTask *task = [session dataTaskWithRequest:request
                                          completionHandler:^(NSData *data,
                                                            NSURLResponse *response,
                                                            NSError *error) {
        if (error) {
            if (completion) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    completion(nil, error);
                });
            }
            return;
        }
        
        NSError *jsonError;
        NSDictionary *jsonResponse = [NSJSONSerialization JSONObjectWithData:data
                                                                   options:0
                                                                     error:&jsonError];
        
        if (completion) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(jsonResponse, jsonError);
            });
        }
    }];
    
    [task resume];
}


@end
