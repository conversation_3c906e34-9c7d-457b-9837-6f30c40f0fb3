//
//  UIColor+Util.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON>ta<PERSON> on 2017/8/22.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface UIColor (Util)

#pragma mark - 十六进制色值

+ (UIColor *)colorWithHex:(int)hexValue alpha:(CGFloat)alpha;
+ (UIColor *)colorWithHex:(int)hexValue;

#pragma mark - app 主要常用色值
/**
 #f6f6f6 背景色

 @return uicolor
 */
+ (UIColor *)br_backgroundColor;

/**
 #dcdcdc 分割线/无法点击

 @return uicolor
 */
+ (UIColor *)br_divisionLineColor;

/**
 #c4c4c4 顶部底部线

 @return uicolor
 */
+ (UIColor *)br_topOrBottomLineColor;

/**
 #eaeaea 内部分割线

 @return uicolor
 */
+ (UIColor *)br_insideDivisionLineColor;

/**
 #4185e6 主色调 蓝色

 @return uicolor
 */
+ (UIColor *)br_mainBlueColor;

/**
 #444f5e 主色调 深蓝色

 @return uicolor
 */
+ (UIColor *)br_mainDarkBlueColor;

/**
 #ffffff 按钮文字 白色

 @return uicolor
 */
+ (UIColor *)br_buttonTextWhiteColor;

/**
 #4185e6 按钮文字 蓝色

 @return uicolor
 */
+ (UIColor *)br_buttonTextBlueColor;

/**
 #c4dcff 对话框颜色

 @return uicolor
 */
+ (UIColor *)br_dialogColor;

/**
 #c5c5c5 提示性文字 灰色

 @return uicolor
 */
+ (UIColor *)br_promptTextGrayColor;

/**
 #d2dff1 阴影颜色

 @return uicolor
 */
+ (UIColor *)br_shadowColor;

/**
 #ef4d3d 提示性文字 红色
 
 @return uicolor
 */
+ (UIColor *)br_promptTextRedColor;

/**
 #dcdcdc 按钮无法点击颜色 灰色

 @return iucolor
 */
+ (UIColor *)br_disableBgColor;
#pragma mark - 文字规范

/**
 #1d2024 导航栏 黑色

 @return uicolor
 */
+ (UIColor *)br_navigationBarColor;

/**
 #1d2024 文本文字 黑色

 @return uicolor
 */
+ (UIColor *)br_textBlackColor;

/**
 #c5c5c5 文本文字 亮灰色

 @return uicolor
 */
+ (UIColor *)br_textLightGrayColor;

/**
 #888888 文本文字 灰色

 @return uicolor
 */
+ (UIColor *)br_textGrayColor;

/**
 #734343 文本文字 蓝色

 @return uicolor
 */
+ (UIColor *)br_textBlueColor;

/**
 #444f5e 文本文字 深蓝色

 @return uicolor
 */
+ (UIColor *)br_textDarkBlueColor;

/**
 #ef4d3d 文本文字 红色

 @return uicolor
 */
+ (UIColor *)br_textRedColor;

/**
 #ef4d3d 文本文字 中灰
 
 @return uicolor
 */
+ (UIColor *)br_textMediumGrayColor;

@end
