//
//  UIImage+BR.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/1/30.
//  Copyright © 2018年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "UIImage+BR.h"

@implementation UIImage (BR)

- (UIImage *)normalizedImage {
    if (self.imageOrientation == UIImageOrientationUp) return self;
    
    UIGraphicsBeginImageContextWithOptions(self.size, NO, self.scale);
    [self drawInRect:(CGRect){0, 0, self.size}];
    UIImage *normalizedImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return normalizedImage;
}

- (UIImage *)resizeImageWithMaximumPixels:(NSUInteger)maxPixels {
    CGSize imageSize = self.size;
    CGFloat width = imageSize.width;
    CGFloat height = imageSize.height;

    if (width * height <= maxPixels) {
        return self;
    }

    CGFloat scale = sqrt(maxPixels / (width * height));
    CGFloat newWidth = width * scale;
    CGFloat newHeight = height * scale;

    UIGraphicsBeginImageContextWithOptions(CGSizeMake(newWidth, newHeight), NO, self.scale);
    [self drawInRect:CGRectMake(0, 0, newWidth, newHeight)];
    UIImage *resizedImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();

    return resizedImage;
}

- (UIImage *)resizeImageWithMaxFileSize:(NSUInteger)maxFileSize {
    if (!self) return nil;
    
    // 先进行方向矫正
    UIImage *normalizedImage = [self normalizedImage];
    
    // 如果原图小于目标大小，直接返回
    NSData *imageData = UIImageJPEGRepresentation(normalizedImage, 1.0);
    if (imageData.length <= maxFileSize) {
        return normalizedImage;
    }
    
    // 二分法查找最合适的压缩质量
    CGFloat maxQuality = 1.0;
    CGFloat minQuality = 0.0;
    CGFloat quality = 0.5;
    int maxIterations = 15; // 防止死循环
    int currentIteration = 0;
    
    UIImage *resultImage = normalizedImage;
    
    while (currentIteration < maxIterations) {
        @autoreleasepool {
            NSData *imageData = UIImageJPEGRepresentation(normalizedImage, quality);
            
            // 压缩后的大小比目标大小大，需要继续压缩
            if (imageData.length > maxFileSize) {
                maxQuality = quality;
                quality = (minQuality + quality) / 2.0;
            }
            // 压缩后的大小比目标大小小，尝试提高质量
            else {
                resultImage = [UIImage imageWithData:imageData];
                minQuality = quality;
                quality = (maxQuality + quality) / 2.0;
            }
        }
        
        currentIteration++;
        
        // 如果质量已经很低，还是太大，就需要降低分辨率
        if (currentIteration == maxIterations && maxQuality <= 0.1) {
            // 计算需要的像素数
            CGFloat scale = sqrt((CGFloat)maxFileSize / (CGFloat)imageData.length);
            NSUInteger targetPixels = self.size.width * self.size.height * scale * scale;
            // 使用已有的像素压缩方法
            resultImage = [self resizeImageWithMaximumPixels:targetPixels];
            break;
        }
    }
    
    return resultImage;
}
@end
