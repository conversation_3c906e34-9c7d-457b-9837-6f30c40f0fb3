//
//  IMContactManager.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/28.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@class IMContact;

@protocol IMContactManagerDelegate <NSObject>

@optional
- (void)didReceiveAllContact:(NSArray *)aContacts letters:(NSArray *)aLetters;

@end

@interface IMContactManager : NSObject

+ (instancetype)shareInstance;

- (void)addDelegate:(id<IMContactManagerDelegate>)delegate;
- (void)removeDelegate:(id<IMContactManagerDelegate>)delegate;

//从服务器拉取更新联系人列表信息
- (void)updateContactListFromServer;

//从数据库获取所有的联系人列表
- (void)getAllContactList:(void(^)(NSArray *letterArray,NSArray *contactsArray))contactsInfo;

/**
 使用患者名称从服务器搜索患者列表
 
 @param keywords 名称关键字
 @param handle 搜索结果列表
 */
- (void)searchPatientsByPatientName:(NSString *)keywords searchType:(NSString *)tag completion:(void(^)(NSString *errorMsg,NSArray *patients))handle failed:(void(^)(NSString *msg))failed;

/**
 将联系人加入黑名单

 @param handle 回调
 */
- (void)addContactToBlackListByPatientId:(NSString *)patientId completion:(void(^)(BOOL isOk))handle;

/**
 获取小然客服信息

 @return 小然客服
 */
- (IMContact *)getXiaoRanInfo;

/**
 根据patientId获取患者

 @param aPatientId 患者id
 @return 联系人
 */
- (IMContact *)getIMContactWithPatientId:(NSString *)aPatientId;

/**
 根据patientId获取患者 如果本地数据库存在，则从数据库获取  如果没有，则从服务器请求获取

 @param aPatientId 患者id
 @param completion 完成后的block
 */
- (void)getIMContactWithPatientId:(NSString *)aPatientId completion:(void (^)(IMContact * aContact))completion;

@end
