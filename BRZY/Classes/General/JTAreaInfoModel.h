//
//  JTAreaInfoModel.h
//  BRZY
//
//  Created by 许江涛 on 2021/8/19.
//  Copyright © 2021 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JTAreaInfoModel : NSObject <NSCoding>

@property (nonatomic, copy) NSString *areaName;
@property (nonatomic, copy) NSString *areaCode;
@property (nonatomic, assign) int sort;
@property (nonatomic, copy) NSString *isHot;
@property (nonatomic, copy) NSString *administrationLvl;

@property (nonatomic, strong) NSArray *areaList;

@end

NS_ASSUME_NONNULL_END
