//
//  IMTableSession.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/5.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface IMTableSession : NSObject

@property (copy, nonatomic) NSString *loginUserId;  //当前登录的用户id

@property (copy, nonatomic) NSString *sessionId;    //对应用户的id 主键
@property (copy, nonatomic) NSString *name;
@property (copy, nonatomic) NSString *username;
@property (copy, nonatomic) NSString *telephone;
@property (copy, nonatomic) NSString *status;
@property (copy, nonatomic) NSString *userType;
@property (copy, nonatomic) NSString *thirdType;

//保存会话对应的最后一条消息
@property (copy, nonatomic) NSString *messageId;
@property (strong, nonatomic) NSDate *messageUpdateTime;
@property (nonatomic) IMContentType contentType;
@property (copy, nonatomic) NSString *content;
@property (nonatomic) IMMessageStatus messageStatus;
@property (nonatomic) ChatStatusType chatStatusType;

//保存草稿
@property (copy, nonatomic) NSString *draftContent;
@property (strong, nonatomic) NSDate *draftDate;

//不直接保存头像 通过连表查询从联系人表中获取头像
@property (copy, nonatomic) NSString *headImgUrl;

@end
