//
//  IMTableSession+WCTTableCoding.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/6.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMTableSession.h"
#import <WCDB/WCDB.h>

@interface IMTableSession (WCTTableCoding) <WCTTableCoding>

WCDB_PROPERTY(loginUserId)

WCDB_PROPERTY(sessionId)
WCDB_PROPERTY(name)
WCDB_PROPERTY(username)
WCDB_PROPERTY(telephone)
WCDB_PROPERTY(status)
WCDB_PROPERTY(userType)
WCDB_PROPERTY(thirdType)

//最后一条消息
WCDB_PROPERTY(messageId)
WCDB_PROPERTY(messageUpdateTime)
WCDB_PROPERTY(contentType)
WCDB_PROPERTY(content)
WCDB_PROPERTY(messageStatus)
WCDB_PROPERTY(chatStatusType)

//草稿
WCDB_PROPERTY(draftContent)
WCDB_PROPERTY(draftDate)

@end
