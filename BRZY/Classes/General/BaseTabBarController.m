//
//  BaseTabBarController.m
//  BRZY
//
//  Created by  <PERSON>ujiangta<PERSON> on 2017/8/22.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BaseTabBarController.h"
#import "MessageListViewController.h"
#import "PatientViewController.h"
#import "InviteViewController.h"
#import "UserCenterViewController.h"
#import "BaseNavigationController.h"
#import "ManagerViewController.h"
#import "IMClient.h"
#import "UITabBar+bagde.h"
#import "BRAlertView.h"
#import "JPUSHService.h"

@interface BaseTabBarController ()<UITabBarControllerDelegate>


@property (strong, nonatomic) PatientViewController *patientViewController;
@property (strong, nonatomic) InviteViewController *inviteViewController;
@property (strong, nonatomic) UserCenterViewController *userCenterViewController;
@property (strong, nonatomic) ManagerViewController *managerViewController;
@property (strong, nonatomic) NSArray *dataArray;

@end

@implementation BaseTabBarController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [[SocketManager shareInstance] addDelegate:self];
    
    [self setViewControllers:self.dataArray];
    
    self.delegate = self;
    
    for (UITabBarItem *item in self.tabBar.items) {
        [item setTitleTextAttributes:[NSDictionary dictionaryWithObjectsAndKeys:[UIColor colorWithHex:0x8ca5bd],NSForegroundColorAttributeName,[UIFont systemFontOfSize:13],NSFontAttributeName, nil] forState:UIControlStateNormal];
        [item setTitleTextAttributes:[NSDictionary dictionaryWithObjectsAndKeys:[UIColor br_mainBlueColor],NSForegroundColorAttributeName,[UIFont systemFontOfSize:13],NSFontAttributeName, nil] forState:UIControlStateSelected];
    }
    
    [[UITabBar appearance] setShadowImage:[UIImage new]];
    [self addShadowWithOffset:CGSizeMake(0, -1) radius:4 color:[UIColor colorWithHex:0xccdbed] opacity:0.3];
    
    //进入到主页面后 关闭需要完善资料功能
    [Config storeisNeedCompleteUserData:NO];
    //添加更新总消息未读数的通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateSumUnReadCount) name:kIMNotificationUpdateSumUnReadCount object:nil];
    
    if ([[[UserManager shareInstance] getUserInfoByKey:kUserDefaultbagde] isEqualToString:@"1"]) {
        [self.tabBar hideBadgeOnItemIndex:4];
    } else {
        [self.tabBar showBadgeOnItemIndex:4];
    }
    
    //如果为非自动登录进入  即从登录页面或者注册页面 则开始接收消息
    if ([Config getisAutoLogin] == NO) {
        [[IMClient shareInstance] startRequestReceiveMessage];
        //改为自动登录 当断开连接重新连接后 则开始更新数据
        [Config storeisAutoLogin:YES];
    }
    //自动登录
    else {
        [[Config shareInstance] fireShowDialog];
    }
    
    [self requestUpdateUserInfo];
    
    [SDWebImageDownloader.sharedDownloader setValue:@"text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
                                 forHTTPHeaderField:@"Accept"];
    
    if ([Config firstOpenByID:@"update_contact"]) {
//        [[YYCache cacheWithName:kIMSDKHelperCacheName] removeAllObjects];
        
        [[IMContactManager shareInstance] updateContactListFromServer];
        [Config changeFirstOpenStateById:@"update_contact"];
    }
    
}
#pragma mark - 更新用户信息
- (void)requestUpdateUserInfo {
    NSDictionary *dict = @{@"method_code":@"000017",
                           @"tel":[UserManager shareInstance].getTelephone
                           };
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        UserInfo *userInfo = [UserInfo mj_objectWithKeyValues:responseObject];
        [[UserManager shareInstance] updateUserInfo:userInfo];
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
    }];
    
}
#pragma mark - 底部阴影
- (void)addShadowWithOffset:(CGSize)offset radius:(CGFloat)radius color:(UIColor *)color opacity:(CGFloat)opacity {
    CGMutablePathRef path = CGPathCreateMutable();
    CGPathAddRect(path, NULL, self.tabBar.bounds);
    self.tabBar.layer.shadowPath = path;
    CGPathCloseSubpath(path);
    CGPathRelease(path);
    
    self.tabBar.layer.shadowColor = color.CGColor;
    self.tabBar.layer.shadowOffset = offset;
    self.tabBar.layer.shadowRadius = radius;
    self.tabBar.layer.shadowOpacity = opacity;
    
    self.tabBar.clipsToBounds = NO;
}
#pragma mark - 更新消息未读数
- (void)updateSumUnReadCount {
    NSInteger unReadCount = [[IMClient shareInstance].sessionManager getAllUnReadMessageCount];
    if (unReadCount > 0 && unReadCount <= kBadgeMessageMax) {
        _messageViewController.tabBarItem.badgeValue = [NSString stringWithFormat:@"%d",(int)unReadCount];
    }else if (unReadCount > kBadgeMessageMax) {
        _messageViewController.tabBarItem.badgeValue = [NSString stringWithFormat:@"%d+",kBadgeMessageMax];
    }else{
        _messageViewController.tabBarItem.badgeValue = nil;
    }
    
    [UIApplication sharedApplication].applicationIconBadgeNumber = unReadCount;
    [JPUSHService setBadge:unReadCount];
}

#pragma mark- lazy load
- (MessageListViewController *)messageViewController {
    if (!_messageViewController) {
        _messageViewController = [[MessageListViewController alloc] init];
        
        UIImage *messageNormalImage = [[UIImage imageNamed:@"tabbar_message_normal"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
        UIImage *messageSelectedImage = [[UIImage imageNamed:@"tabbar_message_selected"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
        
        _messageViewController.tabBarItem = [[UITabBarItem alloc] initWithTitle:NSLocalizedString(@"message", nil) image:messageNormalImage tag:0];
        _messageViewController.tabBarItem.selectedImage = messageSelectedImage;
    }
    return _messageViewController;
}

- (PatientViewController *)patientViewController {
    if (!_patientViewController) {
        _patientViewController = [[PatientViewController alloc] init];
        
        UIImage *patientNormalImage = [[UIImage imageNamed:@"tabbar_patient_normal"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
        UIImage *patientSelectedImage = [[UIImage imageNamed:@"tabbar_patient_selected"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
        
        _patientViewController.tabBarItem = [[UITabBarItem alloc] initWithTitle:NSLocalizedString(@"my patient", nil) image:patientNormalImage tag:1];
        _patientViewController.tabBarItem.selectedImage = patientSelectedImage;
    }
    return _patientViewController;
}

- (InviteViewController *)inviteViewController {
    if (!_inviteViewController) {
        _inviteViewController = [[InviteViewController alloc] init];
        
        UIImage *inviteNormalImage = [[UIImage imageNamed:@"tabbar_invite_normal"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
        UIImage *inviteSelectedImage = [[UIImage imageNamed:@"tabbar_invite_selected"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
        
        _inviteViewController.tabBarItem = [[UITabBarItem alloc] initWithTitle:NSLocalizedString(@"invite", nil) image:inviteNormalImage tag:3];
        _inviteViewController.tabBarItem.selectedImage = inviteSelectedImage;
    }
    return _inviteViewController;
}

- (UserCenterViewController *)userCenterViewController {
    if (!_userCenterViewController) {
        _userCenterViewController = [[UserCenterViewController alloc] init];
        
        UIImage *userCenterNormalImage = [[UIImage imageNamed:@"tabbar_usercenter_normal"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
        UIImage *userCenterSelectedImage = [[UIImage imageNamed:@"tabbar_usercenter_selected"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
        
        _userCenterViewController.tabBarItem = [[UITabBarItem alloc] initWithTitle:NSLocalizedString(@"personal center", nil) image:userCenterNormalImage tag:4];
        _userCenterViewController.tabBarItem.selectedImage = userCenterSelectedImage;
    }
    return _userCenterViewController;
}

- (ManagerViewController *)managerViewController {
    if (!_managerViewController) {
        _managerViewController = [[ManagerViewController alloc] init];
        
        
        UIImage *userCenterNormalImage = [[UIImage imageNamed:@"tabbar_manager_normal"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
        UIImage *userCenterSelectedImage = [[UIImage imageNamed:@"tabbar_manager_selected"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
        
        
        _managerViewController.tabBarItem = [[UITabBarItem alloc] initWithTitle:NSLocalizedString(@"manager", nil) image:userCenterNormalImage tag:2];
        _managerViewController.tabBarItem.selectedImage = userCenterSelectedImage;
    }
    return _managerViewController;
}
- (NSArray *)dataArray {
    if (!_dataArray) {
        
        BaseNavigationController *messageNavi = [[BaseNavigationController alloc] initWithRootViewController:self.messageViewController];
        
        BaseNavigationController *patientNavi = [[BaseNavigationController alloc] initWithRootViewController:self.patientViewController];
        
        BaseNavigationController *inviteNavi = [[BaseNavigationController alloc] initWithRootViewController:self.inviteViewController];
        
        BaseNavigationController *userCenterNavi = [[BaseNavigationController alloc] initWithRootViewController:self.userCenterViewController];
        
        BaseNavigationController *managerNavi = [[BaseNavigationController alloc] initWithRootViewController:self.managerViewController];
        
        _dataArray = [NSArray arrayWithObjects:messageNavi,patientNavi,managerNavi,inviteNavi,userCenterNavi, nil];
    }
    return _dataArray;
}

- (void)tabBarController:(UITabBarController *)tabBarController didSelectViewController:(UIViewController *)viewController {
    
    //点击管理
    if ([viewController isEqual:[self.viewControllers objectAtIndex:2]]) {
        [_managerViewController reloadCollectionView];
    }
    
}

#pragma mark-
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}

@end
