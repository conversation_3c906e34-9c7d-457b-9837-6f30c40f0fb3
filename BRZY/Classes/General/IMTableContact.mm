//
//  IMTableContact.m
//  
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/5.
//
//

#import "IMTableContact.h"
#import "IMTableContact+WCTTableCoding.h"

@implementation IMTableContact

WCDB_IMPLEMENTATION(IMTableContact)

// 修改WCDB_SYNTHESIZE的用法，适配WCDB 2.1.10版本，移除类名作为参数
WCDB_SYNTHESIZE(loginUserId)

WCDB_SYNTHESIZE(userId)
WCDB_SYNTHESIZE(rosterUserId)
WCDB_SYNTHESIZE(city)
WCDB_SYNTHESIZE(nickName)
WCDB_SYNTHESIZE(sex)
WCDB_SYNTHESIZE(firstSpell)
WCDB_SYNTHESIZE(mobile)
WCDB_SYNTHESIZE(updateTime)
WCDB_SYNTHESIZE(province)
WCDB_SYNTHESIZE(headImgUrl)
WCDB_SYNTHESIZE(createdTime)
WCDB_SYNTHESIZE_COLUMN(age, "ageString")
WCDB_SYNTHESIZE(remark)
WCDB_SYNTHESIZE(tags)
//WCDB_SYNTHESIZE(age, ageString)
WCDB_SYNTHESIZE(tag1)
WCDB_SYNTHESIZE(tag2)

WCDB_SYNTHESIZE(r_description)

WCDB_SYNTHESIZE(status)
WCDB_SYNTHESIZE(groupName)
WCDB_SYNTHESIZE(subType)

//不能设置主键  不同的账号对应相同的用户
WCDB_NOT_NULL(rosterUserId)

- (NSString *)loginUserId {
    return [UserManager shareInstance].getUserId;
}

@end
