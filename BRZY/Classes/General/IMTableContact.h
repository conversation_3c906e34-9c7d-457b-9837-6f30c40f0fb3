//
//  IMTableContact.h
//  
//
//  Created by  xujiangta<PERSON> on 2017/9/5.
//
//

#import <Foundation/Foundation.h>

@interface IMTableContact : NSObject

@property (copy, nonatomic) NSString *loginUserId;//当前登录的用户id

@property (copy, nonatomic) NSString *userId;//医生userId
@property (copy, nonatomic) NSString *rosterUserId; //
@property (copy, nonatomic) NSString *city;
@property (copy, nonatomic) NSString *nickName;
@property (nonatomic) IMContactGender sex;
@property (copy, nonatomic) NSString *firstSpell;   //首字母拼音
@property (copy, nonatomic) NSString *mobile;
@property (strong, nonatomic) NSDate *updateTime;
@property (copy, nonatomic) NSString *province;
@property (copy, nonatomic) NSString *headImgUrl;
@property (strong, nonatomic) NSDate *createdTime;
@property (nonatomic, copy) NSString *age;
@property (nonatomic, copy) NSString *remark;//备注名称
@property (copy, nonatomic) NSString *tag1;
@property (copy, nonatomic) NSString *tag2;
@property (strong, nonatomic) NSArray *tags;

//小然
@property (copy, nonatomic) NSString *r_description;//小然描述

//状态 组名等  不常用信息
@property (nonatomic) int status;
//对应BRContactModel中的group
@property (copy, nonatomic) NSString *groupName;
@property (copy, nonatomic) NSString *subType;
@end
