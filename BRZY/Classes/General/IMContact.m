//
//  IMContact.m
//  BRZY
//
//  Created by  <PERSON><PERSON>jiangta<PERSON> on 2017/8/28.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMContact.h"
#import "IMDataBaseManager.h"

@implementation IMContact

- (void)setProvince:(NSString *)province {
    _province = province;
    if (!_province) {
        _province = @"";
    }
}

- (void)setCity:(NSString *)city {
    _city = city;
    if (!_city) {
        _city = @"";
    }
}

- (void)setTagName:(NSString *)tagName {
    _tagName = tagName;
    if (!_tagName) {
        _tagName = @"";
    }
}

- (NSString *)headImgUrl {
    if (!_headImgUrl) {
        _headImgUrl = @"";
    }
    return _headImgUrl;
}

-(NSString *)remark
{
    if (!_remark) {
        _remark = @"";
    }
    return _remark;
}

- (NSString *)patientId {
    if (!_patientId) {
        _patientId = @"";
    }
    return _patientId;
}

#pragma mark - 联系人操作
- (BOOL)changeTagName:(NSString *)tagName {
    return [[IMDataBaseManager shareInstance] updateIMTableMessageTagWithRosterUserId:self.patientId tagname:tagName];
}
- (BOOL)changeRemark:(NSString *)remark
{
    return [[IMDataBaseManager shareInstance] updateIMTableContactRemarkWithRosterUserId:self.patientId remark:remark];
}
- (BOOL)changeTags:(NSArray *)tags
{
    return [[IMDataBaseManager shareInstance] updateIMTableContactTagsWithRosterUserId:self.patientId tags:tags];
}
@end
