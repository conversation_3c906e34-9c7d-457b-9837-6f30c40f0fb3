//
//  IMSession.h
//  BRZY
//
//  Created by  xujiangtao on 2017/9/4.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@class IMMessage;

@interface IMSession : NSObject

@property (copy, nonatomic) NSString *sessionId;
@property (copy, nonatomic) NSString *name;
@property (copy, nonatomic) NSString *username;

@property (copy, nonatomic) NSString *createTime;
@property (copy, nonatomic) NSString *headImgUrl;

//最后一条消息内容
@property (copy, nonatomic) NSString *content;
@property (strong, nonatomic) NSDate *messageDate;
@property (nonatomic) IMMessageStatus messageStatus;
@property (copy, nonatomic) NSString *chatStatusString;

//草稿
@property (copy, nonatomic) NSString *draftContent;
@property (strong, nonatomic) NSDate *draftDate;
@property (copy, nonatomic) NSString *remark;

//该会话对应的未读消息数目
@property (nonatomic) NSInteger unReadMessageCount;
//是否可以已经开启会话
@property (nonatomic) BOOL isOpenDialog;

/**
 从数据库中获取指定数量的消息

 @param aDate 时间
 @param aLimit 获取的条数
 @return 消息列表<IMMessage>
 */
- (NSArray *)loadMorePreMessageFromDate:(NSDate *)aDate limit:(int)aLimit;

/**
 从服务器中获取指定数量的消息

 @param aMessageId messageId
 @param aLimit 获取的条数
 */
- (void)loadMorePreMessageRemoteFromId:(NSString *)aMessageId limit:(int)aLimit;

/**
 开启会话后首先查询会话状态
 */
- (void)updateSessionStatusCompletion:(void (^)(BOOL isOpenNoDisturb))completion;

/**
 开启会话 告知服务器开启一个会话
 */
- (void)sessionStartCompletion:(void (^)(BOOL isOpenDialog))completion;

/**
 关闭会话 告知服务器结束一个会话
 isOk为是否关闭成功  errorMsg当isOk为NO时返回错误信息
 */
- (void)sessionEndCompletion:(void(^)(BOOL isOk,NSString *errorMsg))completion;

/**
 获取会话信息

 @param completion 获取完成回调
 */
- (void)sessionInfoCompletion:(void(^)(NSString *whoOpen,NSString *orderIds,NSString *prePayMoney,BOOL isOk))completion;

/**
 更新接收到的某一条消息为已读 在聊天界面接收到消息显示之前调用
 
 @param aMessageId 消息id
 @return 是否更新成功
 */
- (BOOL)updateMessageToLocalReaded:(NSString *)aMessageId;

/**
 更新该会话下所有的消息为已读

 @return 是否更新成功
 */
- (BOOL)updateAllMessageToLocalReaded;

/**
 删除当前会话下所有本地消息

 @return 是否删除成功
 */
- (BOOL)deleteAllMessageFromDataBase;

/**
 根据MessageId重新创建并获取新的Message

 @param aMessageId 需要重发的消息id
 @return 新的消息IMMessage
 */
- (IMMessage *)getRetrySendMessageByMessageId:(NSString *)aMessageId;

/**
 从数据库中删除指定消息id的消息

 @param aMessageId 消息id
 @return 是否删除成功
 */
- (BOOL)deleteMessageFromDataBaseWithMessageId:(NSString *)aMessageId;

/**
 根据消息id获取某条消息

 @param aMessagId 指定的消息id
 @return 获取到的消息
 */
- (IMMessage *)getOneMessageWithMessageId:(NSString *)aMessagId;

#pragma mark - 直接创建IMSession 匿名聊天等

/**
 使用用户id 名字 头像直接创建IMSession

 @param aUserId 用户id
 @param aNickName 昵称
 @param aHeadImageUrl 头像
 @return 会话
 */
+ (IMSession *)createIMSessionWithUserId:(NSString *)aUserId name:(NSString *)aNickName headImageUrl:(NSString *)aHeadImageUrl;

@end
