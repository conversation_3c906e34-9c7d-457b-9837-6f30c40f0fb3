//
//  BaseNavigationController.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/22.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BaseNavigationController.h"

@interface BaseNavigationController ()

@end

@implementation BaseNavigationController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [[UINavigationBar appearance] setBarTintColor:[UIColor whiteColor]];
    [[UINavigationBar appearance] setTranslucent:NO];
    
    [self.navigationBar setTitleTextAttributes:@{
                                                 NSFontAttributeName : [UIFont br_navigationFont],
                                                 NSForegroundColorAttributeName : [UIColor br_textBlackColor]
                                                 }];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    if (@available(iOS 15, *)) {
        UINavigationBarAppearance *appearance = [UINavigationBarAppearance new];
        appearance.backgroundEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleRegular];
        self.navigationBar.scrollEdgeAppearance = appearance;
    }
}


#pragma mark- 
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}


@end
