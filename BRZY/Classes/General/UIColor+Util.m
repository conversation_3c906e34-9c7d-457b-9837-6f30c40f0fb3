//
//  UIColor+Util.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/22.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "UIColor+Util.h"

@implementation UIColor (Util)

+ (UIColor *)colorWithHex:(int)hexValue alpha:(CGFloat)alpha {
    return [UIColor colorWithRed:((float)((hexValue & 0xFF0000) >> 16))/255.0
                           green:((float)((hexValue & 0xFF00) >> 8))/255.0
                            blue:((float)(hexValue & 0xFF))/255.0
                           alpha:alpha];
}

+ (UIColor *)colorWithHex:(int)hexValue {
    return [UIColor colorWithHex:hexValue alpha:1.0];
}

+ (UIColor *)br_backgroundColor {
    return [UIColor colorWithHex:0xf6f6f6];
}

+ (UIColor *)br_divisionLineColor {
    return [UIColor colorWithHex:0xdcdcdc];
}

+ (UIColor *)br_topOrBottomLineColor {
    return [UIColor colorWithHex:0xc4c4c4];
}

+ (UIColor *)br_insideDivisionLineColor {
    return [UIColor colorWithHex:0xeaeaea];
}

+ (UIColor *)br_shadowColor {
    return [UIColor colorWithHex:0xd2dff1];
}

+ (UIColor *)br_mainBlueColor {
    return [UIColor colorWithHex:0x4185e6];
}

+ (UIColor *)br_mainDarkBlueColor {
    return [UIColor colorWithHex:0x444f5e];
}

+ (UIColor *)br_buttonTextWhiteColor {
    return [UIColor colorWithHex:0xffffff];
}

+ (UIColor *)br_buttonTextBlueColor {
    return [UIColor colorWithHex:0x4185e6];
}

+ (UIColor *)abr_dialogColor {
    return [UIColor colorWithHex:0xc4dcff];
}

+ (UIColor *)br_promptTextGrayColor {
    return [UIColor colorWithHex:0xc5c5c5];
}

+ (UIColor *)br_promptTextRedColor {
    return [UIColor colorWithHex:0xef4d3d];
}

+ (UIColor *)br_navigationBarColor {
    return [UIColor colorWithHex:0x1d2024];
}

+ (UIColor *)br_textBlackColor {
    return [UIColor colorWithHex:0x1d2024];
}

+ (UIColor *)br_textLightGrayColor {
    return [UIColor colorWithHex:0xc5c5c5];
}

+ (UIColor *)br_textMediumGrayColor {
    return [UIColor colorWithHex:0xb1b1b1];
}

+ (UIColor *)br_textGrayColor {
    return [UIColor colorWithHex:0x888888];
}

+ (UIColor *)br_textBlueColor {
    return [UIColor colorWithHex:0x4185e6];
}

+ (UIColor *)br_textDarkBlueColor {
    return [UIColor colorWithHex:0x444f5e];
}

+ (UIColor *)br_textRedColor {
    return [UIColor colorWithHex:0xef4d3d];
}

+ (UIColor *)br_disableBgColor {
    return [UIColor colorWithHex:0xdcdcdc];
}

@end
