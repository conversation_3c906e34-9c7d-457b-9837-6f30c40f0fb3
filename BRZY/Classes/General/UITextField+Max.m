//
//  UITextField+Max.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/25.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "UITextField+Max.h"
#import <objc/runtime.h>

static NSString *const BRMaxLengthKey = @"BRMaxLengthKey";

@implementation UITextField (Max)

@dynamic maxLength;

+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        Class selfClass = [self class];
        
        SEL oriSel = @selector(setPlaceholder:);
        Method oriMethod = class_getInstanceMethod(selfClass, oriSel);
        
        SEL curSel = @selector(br_setPlaceholder:);
        Method curMethod = class_getInstanceMethod(selfClass, curSel);
        
        BOOL addSucc = class_addMethod(selfClass, oriSel, method_getImplementation(curMethod), method_getTypeEncoding(oriMethod));
        if (addSucc) {
            class_replaceMethod(selfClass, curSel, method_getImplementation(oriMethod), method_getTypeEncoding(oriMethod));
        }else{
            method_exchangeImplementations(oriMethod, curMethod);
        }
    });
}

- (void)br_setPlaceholder:(NSString *)placeholder {
    if (!placeholder) {
        placeholder = @"";
    }
    NSMutableAttributedString *placeholderAttributedString = [[NSMutableAttributedString alloc] initWithString:placeholder];
    [placeholderAttributedString addAttribute:NSForegroundColorAttributeName value:[UIColor colorWithHex:0xb1b1b1] range:NSMakeRange(0, placeholder.length)];
//    [placeholderAttributedString addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:16] range:NSMakeRange(0, placeholder.length)];
    self.attributedPlaceholder = placeholderAttributedString;
    
    [self br_setPlaceholder:placeholder];
}

- (NSInteger)maxLength {
    NSValue *maxLengthValue = objc_getAssociatedObject(self, &BRMaxLengthKey);
    if (maxLengthValue) {
        NSInteger maxLength;
        [maxLengthValue getValue:&maxLength];
        return maxLength;
    }
    return NSIntegerMax;
}

- (void)setMaxLength:(NSInteger)maxLength {
    if (maxLength < 1) {
        maxLength = NSIntegerMax;
    }
    
    NSValue *value = [NSValue value:&maxLength withObjCType:@encode(NSInteger)];
    
    objc_setAssociatedObject(self, &BRMaxLengthKey, value, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    [self br_textFieldTextChanged:self];
    
    [self removeTarget:self action:@selector(br_textFieldTextChanged:) forControlEvents:UIControlEventEditingChanged];
    
    [self addTarget:self action:@selector(br_textFieldTextChanged:) forControlEvents:UIControlEventEditingChanged];
}

- (void)br_textFieldTextChanged:(UITextField *)textField {
    if (textField.text.length <= textField.maxLength) {
        return;
    } else {
        NSInteger adaptedLength = MIN(textField.text.length, textField.maxLength);
        NSRange range = NSMakeRange(0, adaptedLength);
        textField.text = [textField.text substringWithRange:range];
    }
}

- (void)paste:(id)sender {
    UIPasteboard *pasteBoard = [UIPasteboard generalPasteboard];
    NSString *text = [[pasteBoard.string stringByReplacingOccurrencesOfString:@"-" withString:@""] stringByTrim];
    text = [text stringByReplacingOccurrencesOfString:@"\\p{Cf}" withString:@"" options:NSRegularExpressionSearch range:NSMakeRange(0, text.length)];
    text = [text stringByReplacingOccurrencesOfString:@" " withString:@""];;
    text = [text stringByReplacingOccurrencesOfString:@"_" withString:@""];
    self.text = text;
}
@end
