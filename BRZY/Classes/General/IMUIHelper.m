//
//  IMUIHelper.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/10/31.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMUIHelper.h"
#import "IMMessage.h"
#import "BRMessage.h"

@implementation IMUIHelper

//消息类型转换
+ (BRMessage *)convertIMMessageToBRMessage:(IMMessage *)aMessage {
    BRMessage *message = [[BRMessage alloc] init];
    
    message.messageId = aMessage.messageId;
    message.serverMessageId = aMessage.serverMessageId;
    message.date = aMessage.serverTime;
    message.senderId = aMessage.from;
    message.localId = aMessage.localId;
    message.deliveryStatus = aMessage.status;
    message.message = aMessage;
    
    //发送或者接收
    if ([message.senderId isEqualToString:[UserManager shareInstance].getUserId]) {
        message.outgoing = YES;
    }
    else{
        message.outgoing = NO;
    }
    
    //文字类型消息
    if (aMessage.contentType == IMContentTypeText) {
        message.type = IMMessageCellTypeText;
        message.text = aMessage.text;
    }
    //图片类型消息
    else if (aMessage.contentType == IMContentTypeImage) {
        message.type = IMMessageCellTypeImage;
        message.imageUrl = aMessage.filePath;
    }
    //语音类型
    else if (aMessage.contentType == IMContentTypeAudio) {
        message.type = IMMessageCellTypeAudio;
        message.voiceDataPath = aMessage.filePath;
        message.voiceLength = aMessage.length;
    }
    //问诊单问题类型
    else if (aMessage.contentType == IMContentTypeWZDQuestion) {
        message.type = IMMessageCellTypeWzdQuestion;
        
        message.linkUrl = aMessage.finalLinkUrl;
        message.text = aMessage.text;
        
        message.outgoing = YES;
    }
    //问诊单答案类型
    else if (aMessage.contentType == IMContentTypeWZDAnswer){
        message.type = IMMessageCellTypeWzdAnswer;
        
        message.linkUrl = aMessage.finalLinkUrl;
        message.text = aMessage.text;
    }
    //复诊单问题类型
    else if (aMessage.contentType == IMContentTypeFZDQuestion) {
        message.type = IMMessageCellTypeWzdFzQuestion;
        
        message.linkUrl = aMessage.finalLinkUrl;
        message.text = aMessage.text;
        
        message.outgoing = YES;
    }
    //复诊单答案类型
    else if (aMessage.contentType == IMContentTypeFZDAnswer) {
        message.type = IMMessageCellTypeWzdFzAnswer;
        
        message.linkUrl = aMessage.finalLinkUrl;
        message.text = aMessage.text;
    }
    //补充问题问题类型
    else if (aMessage.contentType == IMContentTypeSupplementQuestion) {
        message.type = IMMessageCellTypeSupplementQuestion;
        
        message.linkUrl = aMessage.finalLinkUrl;
        message.text = aMessage.text;
        
        message.outgoing = YES;
    }
    //补充问题答案类型
    else if (aMessage.contentType == IMContentTypeSupplementAnswer) {
        message.type = IMMessageCellTypeSupplementAnswer;
        
        message.linkUrl = aMessage.finalLinkUrl;
        message.text = aMessage.text;
    }
    //物流消息
    else if (aMessage.contentType == IMContentTypeLogistics) {
        message.type = IMMessageCellTypeLogistics;
        message.text = aMessage.text;
    }
    //交流页面系统消息
    else if (aMessage.contentType == IMContentTypeSystem) {
        message.type = IMMessageCellTypeCustomSystem;
        message.text = aMessage.text;
        
    }
    //消息撤回
    else if (aMessage.contentType == IMContentTypeMsgRevoke) {
        message.type = IMMessageCellTypeMsgRevoke;
        message.text = aMessage.text;
    }
    //医生开启会话
    else if (aMessage.contentType == IMContentTypeStartChatDoctor) {
        message.type = IMMessageCellTypeStartChatDoctor;
        message.text = aMessage.text;
    }
    //医生结束会话
    else if (aMessage.contentType == IMContentTypeFinishChatDoctor) {
        message.type = IMMessageCellTypeFinishChatDoctor;
        message.text = aMessage.text;
    }
    //付费患者开启会话
    else if (aMessage.contentType == IMContentTypeStartChatPatient) {
        message.type = IMMessageCellTypeStartChatPatient;
        message.text = aMessage.text;
    }
    
    return message;
}

@end
