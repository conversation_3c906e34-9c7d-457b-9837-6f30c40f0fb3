//
//  UIView+BR.h
//  BRZY
//
//  Created by  <PERSON><PERSON>jiangta<PERSON> on 2017/9/4.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface UIView (BR)

@property (nonatomic) CGFloat br_left;

/**
 * Shortcut for frame.origin.y
 *
 * Sets frame.origin.y = top
 */
@property (nonatomic) CGFloat br_top;

/**
 * Shortcut for frame.origin.x + frame.size.width
 *
 * Sets frame.origin.x = right - frame.size.width
 */
@property (nonatomic) CGFloat br_right;

/**
 * Shortcut for frame.origin.y + frame.size.height
 *
 * Sets frame.origin.y = bottom - frame.size.height
 */
@property (nonatomic) CGFloat br_bottom;

/**
 * Shortcut for frame.size.width
 *
 * Sets frame.size.width = width
 */
@property (nonatomic) CGFloat br_width;

/**
 * Shortcut for frame.size.height
 *
 * Sets frame.size.height = height
 */
@property (nonatomic) CGFloat br_height;

/**
 * Shortcut for center.x
 *
 * Sets center.x = centerX
 */
@property (nonatomic) CGFloat br_centerX;

/**
 * Shortcut for center.y
 *
 * Sets center.y = centerY
 */
@property (nonatomic) CGFloat br_centerY;
/**
 * Shortcut for frame.origin
 */
@property (nonatomic) CGPoint br_origin;

/**
 * Shortcut for frame.size
 */
@property (nonatomic) CGSize br_size;

//找到自己的vc
- (UIViewController *)br_viewController;

- (void)topBorderWidth:(CGFloat)width color:(UIColor *)color;

- (void)bottomBorderWidth:(CGFloat)width color:(UIColor *)color;

@end
