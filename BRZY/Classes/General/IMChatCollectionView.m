//
//  IMChatCollectionView.m
//  BRZY
//
//  Created by  xujiangtao on 2017/9/11.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMChatCollectionView.h"

@interface IMChatCollectionView ()

@end

@implementation IMChatCollectionView

- (instancetype)initWithFrame:(CGRect)frame collectionViewLayout:(UICollectionViewLayout *)layout
{
    self = [super initWithFrame:frame collectionViewLayout:layout];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        self.showsVerticalScrollIndicator = YES;
        self.showsHorizontalScrollIndicator = NO;
        self.scrollsToTop = NO;
        self.alwaysBounceVertical = YES;
        self.exclusiveTouch = YES;
        self.delaysContentTouches = NO;
    }
    return self;
}

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [super touchesEnded:touches withEvent:event];
    if (self.tapAction) {
        self.tapAction();
    }
}

- (void)setContentInset:(UIEdgeInsets)contentInset
{
    [super setContentInset:contentInset];
    
    UIEdgeInsets inset = self.scrollIndicatorInsets;
    inset.top = contentInset.top;
    inset.bottom = contentInset.bottom;
    self.scrollIndicatorInsets = inset;
}

@end
