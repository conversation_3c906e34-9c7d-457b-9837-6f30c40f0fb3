//
//  IMChatItemCellLayout.h
//  BRZY
//
//  Created by  xujiangtao on 2017/9/11.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "IMChatItem.h"

NS_ASSUME_NONNULL_BEGIN

@protocol IMChatItemCellLayout <NSObject>

@property (nonatomic, strong) NSString *reuseIdentifier;
@property (nonatomic, strong) id<IMChatItem> chatItem;
@property (nonatomic, assign) CGFloat width;
@property (nonatomic, assign) CGFloat height;

- (instancetype)initWithChatItem:(id<IMChatItem>)chatItem cellWidth:(CGFloat)width;
- (void)calculateLayout;

@end

NS_ASSUME_NONNULL_END
