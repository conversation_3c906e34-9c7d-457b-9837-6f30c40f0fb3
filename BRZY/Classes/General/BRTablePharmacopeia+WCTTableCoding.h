//
//  BRTablePharmacopeia+WCTTableCoding.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/12/26.
//Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRTablePharmacopeia.h"
#import <WCDB/WCDB.h>

@interface BRTablePharmacopeia (WCTTableCoding) <WCTTableCoding>

WCDB_PROPERTY(detailId)
WCDB_PROPERTY(drugName)
WCDB_PROPERTY(secondName)
WCDB_PROPERTY(fristLeter)
WCDB_PROPERTY(spell)
WCDB_PROPERTY(disableDrug)
WCDB_PROPERTY(pregnantDisable)
WCDB_PROPERTY(isHavePoison)
WCDB_PROPERTY(decoctMin)
WCDB_PROPERTY(decoctMax)
WCDB_PROPERTY(decoctMethod)
WCDB_PROPERTY(pillsMin)
WCDB_PROPERTY(pillsMax)
WCDB_PROPERTY(pillsNum)
WCDB_PROPERTY(pillsMethod)
WCDB_PROPERTY(drugStandard)
WCDB_PROPERTY(charge)


@end
