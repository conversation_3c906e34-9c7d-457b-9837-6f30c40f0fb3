//
//  IMChatInputMoreView.m
//  BRZY
//
//  Created by  xujiangtao on 2017/9/15.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMChatInputMoreView.h"
#import "ButtonInfoModel.h"

@implementation IMChatInputMoreView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self configDataWithFrame:frame];
        [self configUI];
    }
    return self;
}

    
- (void)configDataWithFrame:(CGRect)frame {
    _width = CGRectGetWidth(frame);
    margin = 20.f;
}

- (void)configUI {
    
    UIView *lineView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, _width, 1/kScreenScale)];
    lineView.backgroundColor = [UIColor br_topOrBottomLineColor];
    [self addSubview:lineView];
    
    CGFloat btnWidth = 47;
    CGFloat btnHeight = 47;
    NSInteger count = self.btnInfoArray.count;
    CGFloat space = (_width - margin * 2 - btnWidth * count) / (count - 1);
    
    for (int i = 0; i < count; i++) {
        CGFloat x = margin + i * (space + btnWidth);
        CGFloat y = 19;
        
        ButtonInfoModel *model = [self.btnInfoArray objectAtIndex:i];
        UIImage *image = [UIImage imageNamed:model.imageName];
        
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.frame = CGRectMake(x, y, btnWidth, btnHeight);
        [button setImage:image forState:UIControlStateNormal];
        button.tag = i;
        [self addSubview:button];
        
        UILabel *label = [[UILabel alloc] init];
        label.text = model.title;
        label.textColor = [UIColor colorWithHex:0x444f5e];
        label.font = [UIFont systemFontOfSize:14];
        
        [self addSubview:label];
        
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(button.mas_bottom).with.offset(6);
            make.centerX.equalTo(button.mas_centerX);
        }];
        
        [button addTarget:self action:@selector(clickButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    }
}

- (void)clickButtonEvent:(UIButton *)sender {
    
}

- (NSMutableArray *)btnInfoArray {
    if (!_btnInfoArray) {
        _btnInfoArray = [NSMutableArray arrayWithCapacity:0];
        for (int i = 0; i < _titlesArray.count; i++) {
            ButtonInfoModel *model = [[ButtonInfoModel alloc] init];
            model.title = [_titlesArray objectAtIndex:i];
            model.imageName = [_imagesArray objectAtIndex:i];
            
            [_btnInfoArray addObject:model];
        }
    }
    return _btnInfoArray;
}


@end
