//
//  BRError.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/29.
//  Copyright © 2017年 Yi Yi<PERSON>ang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NS_ENUM(NSUInteger, BRErrorCode) {
    BRErrorCodeTimeOut = 300,
    BRErrorCodeAuthFailed = 401,
    BRErrorCodeNetworkError = 503,
};


@interface BRError : NSObject

@property (nonatomic) NSInteger code;
@property (copy, nonatomic) NSString *errorDescription;

+ (BRError *)errorWithCode:(NSInteger)code description:(NSString *)description;

@end
