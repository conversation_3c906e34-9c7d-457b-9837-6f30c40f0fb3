//
//  HTTPRequest.h
//  BRZY
//
//  Created by  <PERSON><PERSON>jiangta<PERSON> on 2017/8/22.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef void (^requestSuccessBlock)(NSURLSessionDataTask *task,id responseObject);
typedef void (^requestErrorBlock)(NSURLSessionDataTask *task, NSError *error);
typedef void (^requestProgressBlock)(NSProgress *progress);
typedef void (^constructingBodyBlock)(id<AFMultipartFormData> formData);
typedef NSURL *(^destinationBlock)(NSURL *targetPath,NSURLResponse *response);
typedef void (^completionHandleBlock)(NSURLResponse *defaultManager,NSURL *filePath, NSError *error);

typedef void (^HttpRequestError)(NSError *errMsg, NSURLSessionDataTask *task);
typedef void (^HttpRequestSucess)(id responsedObject, NSURLSessionDataTask *task);
typedef void (^HttpRequestProgress)(NSProgress *progress);

@interface HTTPRequest : NSObject



/**
 GET 请求
 @prram URLString  请求服务器地址
 @param parameters 请求时所需要的具体参数
 @param progress 请求过程
 @param success 请求成功调用的block
 @param failure 请求失败调用的block
 */

+ (void)GET:(NSString *)URLString
 parameters:(NSDictionary *)parameters
   progress:(requestProgressBlock)progress
        success:(requestSuccessBlock)success
       failure:(requestErrorBlock)failure;



/**
 POST 请求

 @param URLString 请求服务器地址
 @param parameters 请求时所需要的具体参数
 @param progress 请求过程
 @param success 请求成功调用的block
 @param failure 请求失败调用的block
 */
+ (NSURLSessionTask *)POST:(NSString *)URLString
  parameters:(NSDictionary *)parameters
    progress:(requestProgressBlock)progress
     success:(requestSuccessBlock)success
     failure:(requestErrorBlock)failure;


/**
 POST 请求 文件上传

 @param URLString 请求服务器地址
 @param parameters 请求时所需的具体参数
 @param formDataBlock 上传文件处理
 @param progress 请求过程
 @param success 请求成功调用的block
 @param failure 请求失败调用的block
 @return 返回 NSURLSessionTask
 */
+ (NSURLSessionTask *)POST:(NSString *)URLString
                parameters:(NSDictionary *)parameters
 constructingBodyWithBlock:(constructingBodyBlock)formDataBlock
                  progress:(requestProgressBlock)progress
                   success:(requestSuccessBlock)success
                   failure:(requestErrorBlock)failure;



/**
 文件下载

 @param URLString 下载文件所在地址
 @param progress 下载过程
 @param destination 文件保存位置
 @param completion 请求完成
 @return 返回 NSURLSessionDownloadTask
 */
+ (NSURLSessionDownloadTask *)download:(NSString *)URLString
                              progress:(requestProgressBlock)progress
                           destination:(destinationBlock)destination
                            completion:(completionHandleBlock)completion;

+(NSURLSessionDataTask*)HttpPostRequsetWithUrl:(NSString*)urlAdress
                                        params:(NSDictionary*)parmasDic
                                          file:(NSData*)file
                                      progress:(HttpRequestProgress)progressBlock
                                        sucess:(HttpRequestSucess)sucessBlock
                                         error:(HttpRequestError)errorBlock;
@end
