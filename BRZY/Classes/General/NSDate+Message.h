//
//  NSDate+Message.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/23.
//  Copyright © 2017年 <PERSON> Yi<PERSON>ang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "DateTools.h"

typedef NS_ENUM(NSUInteger, TimeValueType) {
    ValueTypeOfSecondAgo = 0,
    ValueTypeOfMinuteAgo,
    ValueTypeOfHourAgo,
    ValueTypeOfDayAgo,
    ValueTypeOfWeekAgo,
    ValueTypeOfMonthAgo,
    ValueTypeOfYearAgo
};

@interface NSDate (Message)

+ (instancetype)dateFromString:(NSString *)string;
- (NSString *)weekdayString;

- (NSString* )timeAgoSince;

@end
