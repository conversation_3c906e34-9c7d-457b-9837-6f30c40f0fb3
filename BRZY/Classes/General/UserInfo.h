//
//  UserInfo.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/25.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface UserInfo : NSObject

@property (copy, nonatomic) NSString *userId;
@property (copy, nonatomic) NSString *name;
@property (copy, nonatomic) NSString *username;
@property (nonatomic) BRUserType userType;
@property (copy, nonatomic) NSString *plainPasssword;
@property (copy, nonatomic) NSString *telephone;
@property (copy, nonatomic) NSString *app_isHaveTakePictureAuth;

///待明确
@property (copy, nonatomic) NSString *status;
@property (nonatomic) NSInteger thirdType;
@property (copy, nonatomic) NSArray *uniqueValues;

//个人资料详细信息
@property (copy, nonatomic) NSString *address;
@property (copy, nonatomic) NSString *areaCode;
@property (copy, nonatomic) NSString *desc;
@property (copy, nonatomic) NSString *goodAt;
@property (copy, nonatomic) NSString *handleUrl;
@property (copy, nonatomic) NSString *hospitalName;
@property (copy, nonatomic) NSString *isAuthentication;
@property (copy, nonatomic) NSString *isDiTui;
@property (copy, nonatomic) NSString *isPerfected;
@property (copy, nonatomic) NSString *lvlName;
@property (copy, nonatomic) NSString *notPayOrderNum;
@property (copy, nonatomic) NSString *paperNo;
@property (copy, nonatomic) NSString *privatePassword;
@property (copy, nonatomic) NSString *sex;
@property (copy, nonatomic) NSString *stockDay;
@property (copy, nonatomic) NSString *title;
@property (copy, nonatomic) NSString *type;
@property (copy, nonatomic) NSString *userState;
@property (copy, nonatomic) NSString *currentAuthenticationState;
@property (copy, nonatomic) NSString *bagde;
@property (copy, nonatomic) NSString *paperNoNew;
@property (copy, nonatomic) NSString *companyVideoUrl;
@property (copy, nonatomic) NSString *firstOpenAgreement;

@property (copy, nonatomic) NSString *keyBoardNumber;

//微信提现用到的绑定
@property (nonatomic, copy) NSString *wxOpenId;

@end
