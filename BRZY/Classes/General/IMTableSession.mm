//
//  IMTableSession.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/5.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMTableSession.h"
#import "IMTableSession+WCTTableCoding.h"


@implementation IMTableSession

WCDB_IMPLEMENTATION(IMTableSession)

// 修改WCDB_SYNTHESIZE的用法，适配WCDB 2.1.10版本，移除类名作为参数
WCDB_SYNTHESIZE(loginUserId)

WCDB_SYNTHESIZE(sessionId)
WCDB_SYNTHESIZE(name)
WCDB_SYNTHESIZE(username)
WCDB_SYNTHESIZE(telephone)
WCDB_SYNTHESIZE(status)
WCDB_SYNTHESIZE(userType)
WCDB_SYNTHESIZE(thirdType)

//最后一条消息记录
WCDB_SYNTHESIZE(messageId)
WCDB_SYNTHESIZE(messageUpdateTime)
WCDB_SYNTHESIZE(contentType)
WCDB_SYNTHESIZE(content)
WCDB_SYNTHESIZE(messageStatus)
WCDB_SYNTHESIZE(chatStatusType)

//sessionId 非空 不能设置主键 不同的账号可能对应有相同的用户
WCDB_NOT_NULL(sessionId)

//草稿
WCDB_SYNTHESIZE(draftContent)
WCDB_SYNTHESIZE(draftDate)


- (NSString *)loginUserId {
    return [UserManager shareInstance].getUserId;
}

@end
