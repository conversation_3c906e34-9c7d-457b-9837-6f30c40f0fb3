//
//  IMChatItemCell.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/11.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMChatItemCell.h"

@implementation IMChatItemCell

+ (NSString *)reuseIdentifier
{
    return @"IMChatItemCell";
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        _itemView = [[UIView alloc] initWithFrame:self.bounds];
        [self.contentView addSubview:_itemView];
    }
    return self;
}

- (void)setLayout:(id<IMChatItemCellLayout>)layout
{
    _layout = layout;
    
    self.itemView.frame = CGRectMake(0, 0, layout.width, layout.height);
}

- (UIView *)snapshotViewAfterScreenUpdates:(BOOL)afterUpdates
{
    UIGraphicsBeginImageContext(self.bounds.size);
    
    [self drawRect:self.bounds];
    
    UIImage *snapshotImage = UIGraphicsGetImageFromCurrentImageContext();
    
    UIGraphicsEndImageContext();
    
    UIImageView *view = [[UIImageView alloc] initWithFrame:self.bounds];
    view.image = snapshotImage;
    
    return view;
}


@end
