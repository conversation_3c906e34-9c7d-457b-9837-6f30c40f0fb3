//
//  IMSessionManager.h
//  BRZY
//
//  Created by  xujiangta<PERSON> on 2017/9/4.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "SocketManager.h"

@class IMSession;
@class IMMessage;
@class BRError;
@class BRMessageModel;

@protocol IMSessionManagerDelegate <NSObject>

@optional

//接收到消息
- (void)didReceiveMessage:(IMMessage *)aMessage;

//消息状态改变
- (void)didMessageUpdate:(IMMessage *)aMessage;

//一次接受多条消息  主要用到获取历史消息
- (void)didReceiveHistoryMessages:(NSArray *)aMessages;

//更新会话列表
- (void)didSessionsUpdate:(NSArray *)aArray;

//通知通告消息
- (void)didReceiveNoticeMessage:(IMMessage *)aMessage;

@end


@interface IMSessionManager : NSObject <SocketManagerDelegate>

+ (instancetype)shareInstance;

- (void)addDelegate:(id<IMSessionManagerDelegate>)delegate;

- (void)removeDelegate:(id<IMSessionManagerDelegate>)delegate;

//发送消息
- (void)sendMessage:(IMMessage *)aMessage;

/**
 撤回指定消息
 
 @param aMessageId 消息id
 */
- (void)withdrawMessage:(NSString *)aMessageId completion:(void(^)(IMMessage *message))handle;

/**
 从本地数据库中获取指定的某条消息

 @param aMessageId 消息id
 @return 要获取的消息
 */
- (IMMessage *)getOneMessageWithMessageId:(NSString *)aMessageId;

/// 从本地数据库中获取指定会话
/// @param aSessionId 会话id
- (IMSession *)getOneSessionWithSessionId:(NSString *)aSessionId;

/**
 从服务器更新最近会话列表
 */
- (void)updateSessionListFromServer;

/**
 从服务器中获取所有会话列表

 @return 包含所有会话的数组
 */
- (NSArray *)loadAllSessionListFromDataBase;

/**
 获取当前登录的用户所有的未读消息条数

 @return 未读消息数目
 */
- (NSInteger)getAllUnReadMessageCount;

/**
 使用患者名来搜索相应消息列表

 @param keywords 关键字
 @return 搜索结果列表
 */
- (NSArray *)searchSessionsByPatientName:(NSString *)keywords;

/**
 根据会话列表chatStatusType来搜索会话

 @param chatStatusType chatStatusType
 @return 搜索结果列表
 */
- (NSArray *)searchSessionsByChatStatusType:(ChatStatusType)chatStatusType;

/**
 根据患者名和chatStatusType来搜索会话

 @param keywords 关键字
 @param chatStatusType chatStatusType
 @return 搜索结果列表
 */
- (NSArray *)searchSessionsByPatientName:(NSString *)keywords chatStatusType:(ChatStatusType)chatStatusType;


/// 根据消息列表内容来查找对应的会话列表
/// @param keywords 消息中包含的关键字
- (NSArray *)searchSessionByMessageListFuzzyMatchingWithKeyworkds:(NSString *)keywords;


/**
 从服务器上更新未读消息列表
 */
- (void)updateUnReadMessagesFromServer;

/**
 删除指定会话id的本地会话

 @param aSessionId 会话id
 @return 是否删除成功
 */
- (BOOL)deleteSessionWithSessionId:(NSString *)aSessionId;


/**
 从服务器更新所有的发送中消息为发送失败

 @return 是否更新成功
 */
- (BOOL)updateAllDeliveringMessageToDeliveringFailed;


/**
 更新本地所有会话中未读消息为已读
 
 @return 是否更新成功
 */
- (BOOL)updateAllUnReadMessagesToLocalRead;


/*********************** private **************************/
- (void)dealReceivedChatMessage:(BRMessageModel *)messageModel;

@end
