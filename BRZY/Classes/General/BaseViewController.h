//
//  BaseViewController.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/1.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <UIKit/UIKit.h>

typedef void (^RequestFinished) (id responsedObject);

@interface BaseViewController : UIViewController

//navigation界面
- (void)showNavBackItem;
- (void)showNavQRItem;
- (void)showNavAddItem;
- (void)showNavSearchItem;
- (void)hideNavSearchItem;

//click Event
- (void)clickQRCodeButton:(UIButton *)sender;
- (void)clickAddButton:(UIButton *)sender;
- (void)clickZoomButton:(UIButton *)sender;
- (void)clickBackButton:(UIButton *)sender;

//打开相机
- (void)presentCameraVC;
- (void)judgeIfHaveAuthorityWithSourceType:(UIImagePickerControllerSourceType)sourceType;
- (void)selectImageFinished:(NSData*)image;

- (void)removeNotification;
- (void)addNotification;

- (BOOL)requestForUploadFile:(NSData *)file superImageView:(UIImageView *)imageView requestFinished:(RequestFinished)finished;
@end
