//
//  IMSDKHelper.h
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/8/28.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@class IMChat;
@class IMMessage;
@class IMContact;
@class IMSession;

@class IMTableMessage;
@class IMTableSession;
@class IMTableContact;

@class IMTableSession;
@class IMTableMessage;
@class IMTableContacts;

@class BRMessageModel;
@class BRContactModel;

@class BRSessionListItemModel;

@interface IMSDKHelper : NSObject

+ (BOOL)createFolderWithFolderPath:(NSString *)path;

//保存更新联系人的时间
+ (void)storeUpdateContactListTime:(NSDate *)date;
//获取上次更新联系人的时间 如果未更新过则返回 半年前时间
+ (NSDate *)getLastUpdateContactListTimeDate;

//保存更新会话列表的时间
+ (void)storeUpdateSessionListTime:(NSDate *)date;
//获取上次更新会话列表的时间  如果没有更新时间  180天
+ (NSDate *)getLastUpdateSessionListTimeDate;

/*************** 类型转换 *****************/
+ (IMTableMessage *)convertIMMessageToIMTableMessage:(IMMessage *)aMessage;
+ (IMMessage *)convertIMTableMessageToIMMessage:(IMTableMessage *)aTableMessage;
+ (IMTableMessage *)convertBRMessageModelToIMTableMessage:(BRMessageModel *)aBRMessageModel;
+ (IMMessage *)convertBRMessageModelToIMMessage:(BRMessageModel *)aBRMessageModel;
+ (IMTableContact *)convertBRContactModelToIMTableContact:(BRContactModel *)aBRContactModel;
+ (IMContact *)convertIMTableContactToIMContact:(IMTableContact *)aTableContact;
+ (IMTableSession *)convertBRSessionListItemModelToIMTableSession:(BRSessionListItemModel *)aBRSessionListItemModel;
+ (IMSession *)convertIMTableSessionToIMSession:(IMTableSession *)aTableSession;
+ (IMSession *)convertIMContactToIMSession:(IMContact *)aContact;
+ (IMContact *)convertIMSessionToIMContact:(IMSession *)aSession;
+ (IMContact *)convertBRContactModelToIMContact:(BRContactModel *)aBRContactModel;
//消息转发 Message 创建
+ (IMMessage *)createTransmitMessageFromOriginalMessage:(IMMessage *)aMessage toPatientId:(NSString *)aPatientId;



@end
