//
//  IMSDKHelper.m
//  BRZY
//
//  Created by  xujiangta<PERSON> on 2017/8/28.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "IMSDKHelper.h"
#import "IMTableMessage.h"
#import "IMTableContact.h"
#import "IMMessage.h"
#import "BRMessageModel.h"
#import "BRContactModel.h"
#import "IMContact.h"
#import "IMSession.h"
#import "IMTableSession.h"
#import "BRSessionListItemModel.h"

static YYCache *cache = nil;

@implementation IMSDKHelper

+ (YYCache *)cache {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        cache = [YYCache cacheWithName:kIMSDKHelperCacheName];
    });
    return cache;
}

+ (BOOL)createFolderWithFolderPath:(NSString *)path {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if ([fileManager fileExistsAtPath:path]) {
        return YES;
    }else{
        return [fileManager createDirectoryAtPath:path withIntermediateDirectories:YES attributes:nil error:nil];
    }
}

+ (void)storeUpdateContactListTime:(NSDate *)date {
    YYCache *cache = [self cache];
    [cache setObject:date forKey:[kIMSDKLastContactListUpdateTime stringByAppendingFormat:@"_%@",[UserManager shareInstance].getUserId]];
}

+ (NSDate *)getLastUpdateContactListTimeDate {
    YYCache *cache = [self cache];
    id lastUpdateTime = [cache objectForKey:[kIMSDKLastContactListUpdateTime stringByAppendingFormat:@"_%@",[UserManager shareInstance].getUserId]];
    
    if (lastUpdateTime) {
        NSDate *date = (NSDate *)lastUpdateTime;
        return date;
    }else{
        return [[Config currentServerDate] dateBySubtractingMonths:6];
    }
}

+ (void)storeUpdateSessionListTime:(NSDate *)date {
    YYCache *cache = [self cache];
    [cache setObject:date forKey:[kIMSDKLastSessionListUpdateTime stringByAppendingFormat:@"_%@",[UserManager shareInstance].getUserId]];
}

+ (NSDate *)getLastUpdateSessionListTimeDate {
    YYCache *cache = [self cache];
    id lastSessionUpdateTime = [cache objectForKey:[kIMSDKLastSessionListUpdateTime stringByAppendingFormat:@"_%@",[UserManager shareInstance].getUserId]];
    
    if (lastSessionUpdateTime) {
        NSDate *date = (NSDate *)lastSessionUpdateTime;
        return date;
    }else{
//        return [[Config currentServerDate] dateBySubtractingMonths:1];
        return [[Config currentServerDate] dateBySubtractingDays:15];
    }
}

+ (IMTableMessage *)convertIMMessageToIMTableMessage:(IMMessage *)aMessage {
    IMTableMessage *tableMessage = [[IMTableMessage alloc] init];
    
    tableMessage.messageId = aMessage.messageId;
    tableMessage.messageType = aMessage.messageType;
    tableMessage.contentType = aMessage.contentType;
    tableMessage.content = aMessage.text;
    tableMessage.serverTime = aMessage.serverTime;
    tableMessage.sessionId = aMessage.sessionId;
    tableMessage.sessionType = aMessage.sessionType;
    tableMessage.fromId = aMessage.fromId;
    tableMessage.from = aMessage.from;
    tableMessage.to = aMessage.to;
    tableMessage.status = aMessage.status;
    
    //
    tableMessage.serverMessageId = aMessage.messageId;
    
    //语音消息
    if (aMessage.contentType == IMContentTypeAudio) {
        tableMessage.fileLocalPath = aMessage.filePath;
        tableMessage.voiceLength = aMessage.length;
    }
    //图片消息
    else if (aMessage.contentType == IMContentTypeImage) {
        tableMessage.fileLocalPath = aMessage.filePath;
    }
    //补充问题
    else if (aMessage.contentType == IMContentTypeSupplementQuestion) {
        //wzdId即groupId 存在在数据表content中
        tableMessage.content = aMessage.wzdId;
        tableMessage.linkUrl = aMessage.linkUrl;
        //extra1 中再次添加  等同groupId
        tableMessage.extra1 = aMessage.wzdId;
    }
    
    return tableMessage;
}

+ (IMMessage *)convertIMTableMessageToIMMessage:(IMTableMessage *)aTableMessage {
    IMMessage *message = [[IMMessage alloc] init];
    
    message.localId = aTableMessage.localId;
    message.messageId = aTableMessage.messageId;
    message.serverMessageId = aTableMessage.serverMessageId;
    message.from = aTableMessage.from;
    message.to = aTableMessage.to;
    message.fromId = aTableMessage.fromId;
    message.sessionType = aTableMessage.sessionType;
    message.contentType = aTableMessage.contentType;
    message.messageType = aTableMessage.messageType;
    message.sessionId = aTableMessage.sessionId;
    message.status = aTableMessage.status;
    
    //Chat
    if (message.messageType == IMMessageTypeChat) {
        //文字消息
        if (aTableMessage.contentType == IMContentTypeText) {
            message.text = aTableMessage.content;
        }
        //语音消息
        else if (aTableMessage.contentType == IMContentTypeAudio) {
            message.length = aTableMessage.voiceLength;
            //以服务端文件地址为准显示  如果没有远程地址则使用本地路径  兼容消息重发时 有TableMessage转IMMessage
            if (aTableMessage.fileLocalPath && ![aTableMessage.fileLocalPath isEqualToString:@""]) {
                message.filePath = aTableMessage.fileLocalPath;
            }
            else {
                message.filePath = aTableMessage.fileRemotePath;
            }
        }
        //图片消息
        else if (aTableMessage.contentType == IMContentTypeImage) {
            //如果是发送的图片 则显示本地路径  如果是接收的图片 则显示远程路径
            //发送
            if ([aTableMessage.from isEqualToString:[UserManager shareInstance].getUserId]) {
                message.filePath = aTableMessage.fileLocalPath;
                //如果没有本地路径 并且有远程路径
                if (!message.filePath && aTableMessage.fileRemotePath) {
                    message.filePath = aTableMessage.fileRemotePath;
                }
            }
            //接收
            else {
                message.filePath = aTableMessage.fileRemotePath;
            }
            
//            if (aTableMessage.fileRemotePath && ![aTableMessage.fileRemotePath isEqualToString:@""]) {
//                message.filePath = aTableMessage.fileRemotePath;
//            }
//            else {
//                message.filePath = aTableMessage.fileLocalPath;
//            }
        }
        //问诊单问题
        //问诊单答案
        //复诊单问题
        //复诊单答案
        //补充问题问题
        //补充问题答案
        else if (aTableMessage.contentType == IMContentTypeWZDQuestion
                 || aTableMessage.contentType == IMContentTypeWZDAnswer
                 || aTableMessage.contentType == IMContentTypeFZDQuestion
                 || aTableMessage.contentType == IMContentTypeFZDAnswer
                 || aTableMessage.contentType == IMContentTypeSupplementQuestion
                 || aTableMessage.contentType == IMContentTypeSupplementAnswer
                 ) {
            message.wzdId = aTableMessage.content;
            message.linkUrl = aTableMessage.linkUrl;
            message.wzdVersion = aTableMessage.version;
            message.wzdType = aTableMessage.wzdType;
            message.extra1 = aTableMessage.extra1;
            message.extra2 = aTableMessage.extra2;
            message.extra3 = aTableMessage.extra3;
            message.text = [NSString stringWithFormat:@"%@\n%@",aTableMessage.extra1,aTableMessage.extra2];
        }
        //交流页面系统消息
        else if (aTableMessage.contentType == IMContentTypeSystem) {
            message.text = aTableMessage.content;
            message.openType = aTableMessage.openType;
            message.extra1 = aTableMessage.extra1;
            message.linkUrl = aTableMessage.linkUrl;
        }
        //消息撤回
        else if (aTableMessage.contentType == IMContentTypeMsgRevoke) {
            message.text = aTableMessage.content;
        }
    }
    //Notice
    else if (message.messageType == IMMessageTypeNotice) {
        message.text = aTableMessage.content;
        message.title = aTableMessage.title;
        message.extra1 = aTableMessage.extra1;
        message.extra2 = aTableMessage.extra2;
        message.extra3 = aTableMessage.extra3;
    }
    //System
    else if (message.messageType == IMMessageTypeSystem) {
        message.text = aTableMessage.content;
    }
    
    
    message.serverTime = aTableMessage.serverTime;
    
    return message;
}

//问诊单等链接等还未添加
+ (IMMessage *)convertBRMessageModelToIMMessage:(BRMessageModel *)aBRMessageModel {
    
    BRMessageContentModel *contentModel = aBRMessageModel.content;
    
    IMMessage *message = [[IMMessage alloc] init];
    
    message.messageId = aBRMessageModel.fromId;
    message.serverMessageId = aBRMessageModel.serverMessageId;
    message.from = aBRMessageModel.from;
    message.to = aBRMessageModel.to;
    message.fromId = aBRMessageModel.fromId;
    message.serverTime = [NSDate dateWithString:aBRMessageModel.createdTime format:@"yyyy-MM-dd HH:mm:ss" timeZone:[NSTimeZone timeZoneWithName:@"GMT+0800"] locale:nil];
    //messageType
    message.messageType = [kIMMessageTypeArray indexOfObject:aBRMessageModel.messageType];
    //contentType
    message.contentType = [kIMContentTypeArray indexOfObject:aBRMessageModel.content.type];
    
    if ([message.from isEqualToString:[[UserManager shareInstance] getUserId]]) {
        //自己发的
        message.sessionId = aBRMessageModel.to;
    }else if ([message.to isEqualToString:[[UserManager shareInstance] getUserId]]){
        //发给自己的
        message.sessionId = aBRMessageModel.from;
    }

    //文字
    if (message.contentType == IMContentTypeText) {
        message.text = contentModel.content;
    }
    //图片
    else if (message.contentType == IMContentTypeImage){
        message.filePath = contentModel.content;
    }
    //语音
    else if (message.contentType == IMContentTypeAudio) {
        message.length = [contentModel.extra1 integerValue];
        message.filePath = contentModel.content;
    }
    //问诊单问题
    //问诊单答案
    //复诊单问题
    //复诊单答案
    //补充问题问题
    //补充问题答案
    else if (message.contentType == IMContentTypeWZDQuestion
             || message.contentType == IMContentTypeWZDAnswer
             || message.contentType == IMContentTypeFZDQuestion
             || message.contentType == IMContentTypeFZDAnswer
             || message.contentType == IMContentTypeSupplementQuestion
             || message.contentType == IMContentTypeSupplementAnswer
             ) {
        message.wzdId = contentModel.content;
        message.linkUrl = contentModel.url;
        message.wzdVersion = contentModel.version;
        message.wzdType = contentModel.wzdType;
        message.extra1 = contentModel.extra1;
        message.extra2 = contentModel.extra2;
        message.extra3 = contentModel.extra3;
        message.text = [NSString stringWithFormat:@"%@\n%@",contentModel.extra1,contentModel.extra2];
    }
    //交流页面系统消息
    else if (message.contentType == IMContentTypeSystem) {
        message.text = contentModel.content;
        message.openType = [kIMMessageOpenTypeArray indexOfObject:contentModel.openType];
        message.extra1 = contentModel.extra1;
        message.linkUrl = contentModel.url;
    }
    //消息撤回
    else if (message.contentType == IMContentTypeMsgRevoke) {
        message.text = contentModel.content;
    }
    //付费患者开启会话
    else if (message.contentType == IMContentTypeStartChatPatient) {
        
    }
    
    return message;
}

+ (IMTableMessage *)convertBRMessageModelToIMTableMessage:(BRMessageModel *)aBRMessageModel {
    
    BRMessageContentModel *contentModel = aBRMessageModel.content;
    
    IMTableMessage *tableMessage = [[IMTableMessage alloc] init];
    
    //contentType 以消息内容content中为主
    tableMessage.contentType = [kIMContentTypeArray indexOfObject:contentModel.type];
    tableMessage.messageType = [kIMMessageTypeArray indexOfObject:aBRMessageModel.messageType];
    tableMessage.content = contentModel.content;
    tableMessage.serverTime = [NSDate dateWithString:aBRMessageModel.createdTime format:@"yyyy-MM-dd HH:mm:ss" timeZone:[NSTimeZone timeZoneWithName:@"GMT+0800"] locale:nil];
    //接收到的消息  接收到的消息不一定是发给自己的 也有可能是自己发出去的  sessionId 为 对方id
    if ([aBRMessageModel.from isEqualToString:[UserManager shareInstance].getUserId]) {
        tableMessage.sessionId = aBRMessageModel.to;
    }else if ([aBRMessageModel.to isEqualToString:[UserManager shareInstance].getUserId]) {
        tableMessage.sessionId = aBRMessageModel.from;
    }
    
    tableMessage.from = aBRMessageModel.from;
    tableMessage.sessionType = IMSessionTypeOneToOne;
    tableMessage.to = aBRMessageModel.to;
    //服务器返回的消息id
    tableMessage.serverMessageId = aBRMessageModel.serverMessageId;
    tableMessage.fromId = aBRMessageModel.fromId;
    //如果为小然消息
    if ([tableMessage.from isEqualToString:@"1000"] && tableMessage.messageType != IMMessageTypeNotice) {
        tableMessage.fromId = tableMessage.serverMessageId;
    }
    
    tableMessage.messageId = tableMessage.fromId;
    //接收到消息设置为未读
    tableMessage.isLocalRead = NO;
    
    //Chat
    if (tableMessage.messageType == IMMessageTypeChat) {
        if (tableMessage.contentType == IMContentTypeImage) {
            //图片消息 url中地址为附件所在远程服务器地址
            tableMessage.fileRemotePath = contentModel.content;
        }
        
        else if(tableMessage.contentType == IMContentTypeAudio){
            //语音消息 url中地址为附加所在远程服务器地址
            tableMessage.fileRemotePath = contentModel.content;
            //语音消息时 extra1 中为语音长度
            tableMessage.voiceLength = [contentModel.extra1 integerValue];
        }
        
        //问诊单相关
        else if (tableMessage.contentType == IMContentTypeWZDQuestion
                 || tableMessage.contentType == IMContentTypeWZDAnswer
                 || tableMessage.contentType == IMContentTypeFZDQuestion
                 || tableMessage.contentType == IMContentTypeFZDAnswer
                 || tableMessage.contentType == IMContentTypeSupplementQuestion
                 || tableMessage.contentType == IMContentTypeSupplementAnswer
                 ) {
            //链接
            tableMessage.linkUrl = contentModel.url;
            //extra1 中 患者信息
            tableMessage.extra1 = contentModel.extra1;
            //extra2 中 病症信息
            tableMessage.extra2 = contentModel.extra2;
            //extra3 中
            tableMessage.extra3 = contentModel.extra3;
            //版本号
            tableMessage.version = contentModel.version;
            //问诊单类型
            tableMessage.wzdType = contentModel.wzdType;
        }
        
        //交流页面系统消息
        else if (tableMessage.contentType == IMContentTypeSystem) {
            tableMessage.openType = [kIMMessageOpenTypeArray indexOfObject:contentModel.openType];
            if (tableMessage.openType == IMMessageOpenTypeOrder) {
                //订单
                tableMessage.extra1 = contentModel.extra1;
                tableMessage.linkUrl = contentModel.url;
            }else if (tableMessage.openType == IMMessageOpenTypeLogistics) {
                //物流
                tableMessage.extra1 = contentModel.extra1;
                tableMessage.linkUrl = contentModel.url;
            }else if (tableMessage.openType == IMMessageOpenTypeOther) {
                //其他
                tableMessage.linkUrl = contentModel.url;
            }
        }
    }
    //通知通告
    else if (tableMessage.messageType == IMMessageTypeNotice) {
        tableMessage.title = contentModel.title;
        tableMessage.extra1 = contentModel.extra1;
        tableMessage.extra2 = contentModel.extra2;
        tableMessage.extra3 = contentModel.extra3;
        
    }
    //系统消息
    else if (tableMessage.messageType == IMMessageTypeSystem) {
        tableMessage.messageId = aBRMessageModel.serverMessageId;
        tableMessage.title = contentModel.title;
        
        //如果为小然客服发送的消息
        tableMessage.sessionId = kSystemId;
        tableMessage.from = kSystemId;
        
    }
    
    return tableMessage;
}

+ (IMTableContact *)convertBRContactModelToIMTableContact:(BRContactModel *)aBRContactModel {
    IMTableContact *tableContact = [[IMTableContact alloc] init];
    
    tableContact.userId = aBRContactModel.userId;
    tableContact.rosterUserId = aBRContactModel.rosterUserId;
    tableContact.city = aBRContactModel.city;
    tableContact.nickName = aBRContactModel.nickName;
    tableContact.sex = [aBRContactModel.sex integerValue];
    tableContact.firstSpell = aBRContactModel.firstSpell;
    tableContact.updateTime = [Config convertFormatedTimeToDate:aBRContactModel.updateTime];
    tableContact.province = aBRContactModel.province;
    tableContact.headImgUrl = aBRContactModel.headImgUrl;
    tableContact.createdTime = [Config convertFormatedTimeToDate:aBRContactModel.createdTime];
//    tableContact.age = [aBRContactModel.age integerValue];
    tableContact.age = aBRContactModel.age;
    tableContact.status = [aBRContactModel.status integerValue];
    tableContact.groupName = aBRContactModel.group;
    tableContact.subType = aBRContactModel.subType;
    tableContact.mobile = aBRContactModel.mobile;
    tableContact.tag1 = aBRContactModel.tag1;
    tableContact.tag2 = aBRContactModel.tag2;
    tableContact.remark = aBRContactModel.remark;
    tableContact.tags = aBRContactModel.tags;
    
    tableContact.r_description = aBRContactModel.r_description;
    //如果为小然  则将小然id 赋值为rosterUesrId中  小然id为 1000
    if (aBRContactModel.r_id && (!aBRContactModel.rosterUserId)) {
        tableContact.rosterUserId = aBRContactModel.r_id;
    }
    
    return tableContact;
}

+ (IMContact *)convertIMTableContactToIMContact:(IMTableContact *)aTableContact {
    IMContact *contact = [[IMContact alloc] init];
    
    contact.patientId = aTableContact.rosterUserId;
    contact.city = aTableContact.city;
    contact.province = aTableContact.province;
    contact.nickname = aTableContact.nickName;
    contact.headImgUrl = aTableContact.headImgUrl;
    contact.gender = aTableContact.sex;
    contact.age = aTableContact.age;
    contact.mobile = aTableContact.mobile;
    contact.tagName = aTableContact.tag1;
    contact.remark = aTableContact.remark;
    contact.tags = aTableContact.tags;
    
    //如果为小然客服
    contact.r_description = aTableContact.r_description;
    
    return contact;
}

+ (IMTableSession *)convertBRSessionListItemModelToIMTableSession:(BRSessionListItemModel *)aBRSessionListItemModel {
    IMTableSession *tableSession = [[IMTableSession alloc] init];
    
    BRSessionRosterUserModel *rosterUserModel = aBRSessionListItemModel.rosterUser;
    
    tableSession.sessionId = rosterUserModel.patientId;
    tableSession.name = rosterUserModel.name;
    tableSession.username = rosterUserModel.username;
    tableSession.telephone = rosterUserModel.telephone;
    tableSession.status = rosterUserModel.status;
    tableSession.userType = rosterUserModel.userType;
    tableSession.thirdType = rosterUserModel.thirdType;
    
    return tableSession;
}

+ (IMMessage *)createTransmitMessageFromOriginalMessage:(IMMessage *)aMessage toPatientId:(NSString *)aPatientId {
    IMMessage *message = aMessage;
    
    message.to = aPatientId;
    message.from = [UserManager shareInstance].getUserId;
    message.status = IMMessageStatusDelivering;
    message.messageId = [Utils createMessageId];
    message.fromId = message.messageId;
    message.localId = 0;
    message.serverTime = [Config currentServerDate];
    message.sessionId = message.to;
    
    
    return message;
}

+ (IMSession *)convertIMTableSessionToIMSession:(IMTableSession *)aTableSession {
    
    IMSession *session = [[IMSession alloc] init];
    
    session.sessionId = aTableSession.sessionId;
    session.name = aTableSession.name;
    session.username = aTableSession.username;
    session.headImgUrl = aTableSession.headImgUrl;
    session.draftDate = aTableSession.draftDate;
    
    //最后一条消息内容
    session.messageStatus = aTableSession.messageStatus;
    if (aTableSession.messageUpdateTime) {
        session.messageDate = aTableSession.messageUpdateTime;
    }
    
    if (aTableSession.contentType == IMContentTypeText || aTableSession.contentType == IMContentTypeSM0003) {
        session.content = aTableSession.content;
    }
    else if (aTableSession.contentType == IMContentTypeImage) {
        session.content = @"图片";
    }
    else if (aTableSession.contentType == IMContentTypeAudio) {
        session.content = @"语音";
    }
    else if (aTableSession.contentType == IMContentTypeWZDQuestion) {
        session.content = @"问诊单已发送";
    }
    else if (aTableSession.contentType == IMContentTypeWZDAnswer) {
        session.content = @"问诊单已填写";
    }
    else if (aTableSession.contentType == IMContentTypeFZDQuestion) {
        session.content = @"复诊单已发送";
    }
    else if (aTableSession.contentType == IMContentTypeFZDAnswer) {
        session.content = @"复诊单已填写";
    }
    else if (aTableSession.contentType == IMContentTypeSupplementQuestion) {
        session.content = @"定制问诊单已发送";
    }
    else if (aTableSession.contentType == IMContentTypeSupplementAnswer) {
        session.content = @"定制问诊单已填写";
    }
    else if (aTableSession.contentType == IMContentTypeMsgRevoke) {
        session.content = @"你撤回了一条消息";
    }
    else if (aTableSession.contentType == IMContentTypeSystem) {
        session.content = aTableSession.content;
    }
    else if (aTableSession.contentType == IMContentTypeStartChatDoctor) {
        session.content = aTableSession.content;
    }
    else if (aTableSession.contentType == IMContentTypeFinishChatDoctor) {
        session.content = aTableSession.content;
    }
    
    //会话问诊单状态等
    
    //问诊单已发送
    if (aTableSession.chatStatusType == ChatStatusTypeWzdSended) {
        session.chatStatusString = @"问诊单已发送";
    }
    //问诊单已填写
    else if (aTableSession.chatStatusType == ChatStatusTypeWzdFilled) {
        session.chatStatusString = @"问诊单已填写";
    }
    //复诊单已发送
    else if (aTableSession.chatStatusType == ChatStatusTypeFzdSended) {
        session.chatStatusString = @"复诊单已发送";
    }
    //复诊单已填写
    else if (aTableSession.chatStatusType == ChatStatusTypeFzdFilled) {
        session.chatStatusString = @"复诊单已填写";
    }
    //未支付
    else if (aTableSession.chatStatusType == ChatStatusTypeOrderNoPay) {
        session.chatStatusString = @"未支付";
    }
    //已支付
    else if (aTableSession.chatStatusType == ChatStatusTypeOrderPayed) {
        session.chatStatusString = @"已支付";
    }
    //已发药
    else if (aTableSession.chatStatusType == ChatStatusTypeDeliverGoods) {
        session.chatStatusString = @"已发药";
    }
    
    return session;
}

+ (IMSession *)convertIMContactToIMSession:(IMContact *)aContact {
    IMSession *session = [[IMSession alloc] init];
    
    session.sessionId = aContact.patientId;
    session.name = aContact.nickname;
    session.headImgUrl = aContact.headImgUrl;
    
    return session;
}

+ (IMContact *)convertIMSessionToIMContact:(IMSession *)aSession {
    IMContact *contact = [[IMContact alloc] init];
    
    contact.patientId = aSession.sessionId;
    contact.nickname = aSession.name;
    contact.headImgUrl = aSession.headImgUrl;
    
    return contact;
}

+ (IMContact *)convertBRContactModelToIMContact:(BRContactModel *)aBRContactModel {
    IMContact *contact = [[IMContact alloc] init];
    
    contact.patientId = aBRContactModel.rosterUserId;
    contact.city = aBRContactModel.city;
    contact.province = aBRContactModel.province;
    contact.nickname = aBRContactModel.nickName;
    contact.headImgUrl = aBRContactModel.headImgUrl;
    contact.gender = (IMContactGender)[aBRContactModel.sex integerValue];
//    contact.age = [aBRContactModel.age integerValue];
    contact.age = aBRContactModel.age;
    contact.tags = aBRContactModel.tags;
    
    contact.remark = aBRContactModel.remark;
    
    return contact;
}

@end
