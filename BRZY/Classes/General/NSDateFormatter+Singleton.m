//
//  NSDateFormatter+Singleton.m
//  BRZY
//
//  Created by  xujiangtao on 2017/8/23.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "NSDateFormatter+Singleton.h"

@implementation NSDateFormatter (Singleton)

+ (instancetype)shareInstance {
    static NSDateFormatter *formatter;
    static dispatch_once_t onceToken;
    
    dispatch_once(&onceToken, ^{
        formatter = [NSDateFormatter new];
        formatter.dateFormat = @"yyyy-MM-dd HH:mm:ss";
        NSTimeZone *zone = [[NSTimeZone alloc] initWithName:@"Asia/Shanghai"];
        formatter.timeZone = zone;
    });
    
    return formatter;
}

@end
