//
//  IMChatViewController.h
//  BRZY
//
//  Created by  <PERSON>ujiang<PERSON><PERSON> on 2017/9/11.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "IMChatCollectionViewLayout.h"
#import "IMChatItemCell.h"
#import "IMChatInputPanel.h"
#import "IMChatInputMoreView.h"

@class IMChatContainerView;
@class IMChatCollectionView;

typedef NS_ENUM(NSUInteger, IMChatInputStatus) {
    IMChatInputStatusText,
    IMChatInputStatusAudio,
    IMChatInputStatusEmoticon,
    IMChatInputStatusMore
};

NS_ASSUME_NONNULL_BEGIN

@interface IMChatViewController : UIViewController <UICollectionViewDataSource, UICollectionViewDelegate, IMChatCollectionViewLayoutDelegate, IMChatInputPanelDelegate, IMChatItemCellDelegate>

@property (nullable, nonatomic, strong) IMChatContainerView *containerView;
@property (nullable, nonatomic, strong) UIImageView *backgroundView;

@property (nullable, nonatomic, strong) UIButton *endChatButton;
@property (nullable, nonatomic, strong) IMChatCollectionView *collectionView;
@property (nullable, nonatomic, strong) IMChatCollectionViewLayout *collectionLayout;
@property (nullable, nonatomic, strong) UIScrollView *collectionViewScrollToTopProxy;

// Supporting you have only one input panel. If you had custom input panels,
// this property would be a pointer which point to current panel.
@property (nullable, nonatomic, strong) IMChatInputPanel *inputPanel;
@property (nullable, nonatomic, strong) IMChatInputMoreView *inputPanelMoreView;
//@property (nonatomic, assign) CGFloat 

@property (nonatomic, assign) CGFloat containerHeight;
@property (nonatomic, assign) CGFloat halfTransitionKeyboardHeight;
@property (nonatomic, assign) CGFloat keyboardHeight;
@property (nonatomic, assign) BOOL isRotating;

@property (nonatomic, assign) BOOL isInControllerTransition;

//
// Pay more attention to inverted mode with layouts,
// as you see on collectionView:
//
// +------------+                +------------+
// |    ...     |                |  layout 0  |
// |  layout 2  |                |  layout 1  |
// |  layout 1  |       VS.      |  layout 2  |
// |  layout 0  |                |    ...     |
// +------------+                +------------+
//
// inverted is YES               inverted is NO
//
@property (nonatomic, strong) NSMutableArray<id<IMChatItemCellLayout>> *layouts;

// Default is YES.
@property (nonatomic, assign, getter=isInverted) BOOL inverted;

@property (nonatomic, assign) CGFloat chatInputContainerViewDefaultHeight;
@property (nonatomic, assign) CGFloat chatInputMoreViewContainerViewDefaultHeight;
@property (nonatomic, assign) CGFloat scrollFractionalThreshold; // in [0, 1]

@property (nonatomic, assign, readonly) CGFloat cellWidth;

+ (nullable Class)cellLayoutClassForItemType:(NSString *)type;
+ (nullable Class)inputPanelClass;
+ (nullable Class)inputPanelMoreViewClass;
- (void)registerChatItemCells;

- (void)didTapStatusBar;

- (nullable id<IMChatItemCellLayout>)createLayoutWithItem:(id<IMChatItem>)item;

- (void)performSizeChangesWithDuration:(NSTimeInterval)duration size:(CGSize)size;

- (void)setupContainerView;
- (void)setupBackgroundView;
- (void)setupCollectionViewScrollToTopProxy;
- (void)setupCollectionView;
- (void)setupInputPanel;
- (void)setupInputPanelMoreView;
- (void)setupEndChatButtonView;

- (void)adjustCollectionViewForSize:(CGSize)size keyboardHeight:(CGFloat)keyboardHeight inputContainerHeight:(CGFloat)inputContainerHeight scrollToBottom:(BOOL)scrollToBottom duration:(NSTimeInterval)duration animationCurve:(int)animationCurve;
@end

//
// Pay more attention to use these methods with inverted mode.
// These methods won't check or even map the relationship
// between layouts and their indexes for you.
//
@interface IMChatViewController (IMChanges)

- (void)insertLayouts:(NSArray<id<IMChatItemCellLayout>> *)layouts atIndexes:(NSIndexSet *)indexes animated:(BOOL)animated;
- (void)deleteLayoutsAtIndexes:(NSIndexSet *)indexes animated:(BOOL)animated;
- (void)updateLayoutAtIndex:(NSUInteger)index toLayout:(id<IMChatItemCellLayout>)layout animated:(BOOL)animated;

@end


//
// These scrolling methods already deal with inverted mode for you.
// For example `isScrolledAtBottom`, the `bottom` is the bottom you see on screen,
// maybe not real bottom of collectionView.
//
@interface IMChatViewController (IMScrolling)

- (BOOL)isCloseToTop;
- (BOOL)isScrolledAtTop;
- (void)scrollToTopAnimated:(BOOL)animated;

- (BOOL)isCloseToBottom;
- (BOOL)isScrolledAtBottom;
- (void)scrollToBottomAnimated:(BOOL)animated;

- (void)stopScrollIfNeeded;

#pragma mark - 结束会话
- (void)clickEndChatButton;
@end

NS_ASSUME_NONNULL_END
