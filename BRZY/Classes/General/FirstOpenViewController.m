//
//  FirstOpenViewController.m
//  BRZY
//
//  Created by 许江涛 on 2022/1/23.
//  Copyright © 2022 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "FirstOpenViewController.h"

@interface FirstOpenViewController ()

@property (nonatomic, strong) UIImageView *bgImageView;

@end

@implementation FirstOpenViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    
    UIImage *launchIamge = [UIImage imageNamed:@"LaunchImage"];
    _bgImageView = [[UIImageView alloc] init];
    _bgImageView.image = launchIamge;
    _bgImageView.contentMode = UIViewContentModeScaleAspectFill;
    [self.view addSubview:_bgImageView];
    
    [_bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    self.navigationController.navigationBarHidden = YES;
}


@end
