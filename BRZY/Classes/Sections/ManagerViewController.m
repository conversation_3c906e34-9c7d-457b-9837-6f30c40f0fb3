//
//  ManagerViewController.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/26.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "ManagerViewController.h"
#import "PanelIconModel.h"
#import "UserCenterPanelCollectionViewCell.h"
#import "BRVisitsArrangementViewController.h"
#import "BRMyPurseViewController.h"

#import "OrderViewController.h"
#import "CommonlyUsedPartyViewController.h"
#import "ServiceSettingViewController.h"
#import "MyAnnouncementViewController.h"
#import "BlacklistViewController.h"
#import "PharmacopoeiaViewController.h"
#import "UINavigationBar+Addition.h"
#import "MyPurseViewController.h"
#import "PhotoPresViewController.h"
#import "DrugStoreViewController.h"
#import "PhotoPrescAgreementViewController.h"
#import "EbookListViewController.h"
#import "BRQuickPrescribeViewController.h"
#import "InviteScanPrescribeViewController.h"
#import "BRMessagePrescribeViewController.h"
#import "AuthCheckHelper.h"
#import "UserManager.h"

static NSString *kcellIdentifier = @"userCenterPanelCellIdentifier";
static NSString *kCellOrderIdentifier = @"kCellOrderIdentifier";
static NSString *kCellTopIdentifier = @"kCellTopIdentifier";


@interface ManagerViewController ()<UICollectionViewDataSource,UICollectionViewDelegate,UICollectionViewDelegateFlowLayout>
{
    NSString *isShowHud;
}
@property (strong, nonatomic) UICollectionView *collectionView;
@property (strong, nonatomic) NSMutableArray *panelModelArray;

@property (nonatomic, strong) UILabel *HDLabel;

@property (nonatomic, strong) NSString *showAndHidden;
@property (nonatomic, strong) NSString *photoShowAndHidden;

@property (nonatomic, strong) PanelIconModel *photoPrescriptionPanelIconModel;
@property (nonatomic, strong) PanelIconModel *drugStorePanelIconModel;

@property (nonatomic, strong) DoctorAuthentiationModel *authStateResult; // 认证状态

@end

@implementation ManagerViewController

-(instancetype)init {

    self = [super init];
    if (self) {
        [[[NSNotificationCenter defaultCenter] rac_addObserverForName:kUserDefaultApp_isHaveTakePictureAuth object:nil]subscribeNext:^(NSNotification * _Nullable x) {
            [self.collectionView reloadData];
        }];
    }
    return self;

}

- (void)viewDidLoad {
    [super viewDidLoad];

    self.view.backgroundColor = [UIColor br_backgroundColor];

    UIView *orderView = [[UIView alloc] initWithFrame:CGRectMake(0, 10, kScreenWidth, 146)];
    orderView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:orderView];

    UILabel *myOrderLabel = [[UILabel alloc] initWithFrame:CGRectMake(15, 0, 100, 45)];
    myOrderLabel.text = @"我的处方订单";
    myOrderLabel.font = kFontLight(16);
    myOrderLabel.textColor = [UIColor br_textBlackColor];
    [orderView addSubview:myOrderLabel];

    UILabel *allOrderLabel = [[UILabel alloc] initWithFrame:CGRectMake(kScreenWidth-130, 0, 100, 45)];
    allOrderLabel.text = @"全部订单";
    allOrderLabel.font = kFontLight(16);
    allOrderLabel.textColor = [UIColor br_textBlackColor];
    allOrderLabel.textAlignment = NSTextAlignmentRight;
    allOrderLabel.backgroundColor = [UIColor clearColor];
    [orderView addSubview:allOrderLabel];

    UIImageView *rightImageView = [[UIImageView alloc] initWithFrame:CGRectMake(kScreenWidth-35, 15/2, 30, 30)];
    rightImageView.image = [UIImage imageNamed:@"my_right"];
    [orderView addSubview:rightImageView];

    UIButton *allButton = [[UIButton alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, 46)];
    allButton.tag = 103;
    [allButton addTarget:self action:@selector(buttonClick:) forControlEvents:UIControlEventTouchUpInside];
    [orderView addSubview:allButton];

    UIImageView *lineImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 45, kScreenWidth, 0.7)];
    lineImageView.backgroundColor = [UIColor br_insideDivisionLineColor];
    [orderView addSubview:lineImageView];

    //待付款
    UIImageView *waitingPaymentImageView = [[UIImageView alloc]initWithFrame:CGRectMake((kScreenWidth/3-30)/2, 66, 30, 30)];
    waitingPaymentImageView.image = [UIImage imageNamed:@"manager_daifukuan"];
    [orderView addSubview:waitingPaymentImageView];

    _HDLabel = [[UILabel alloc]initWithFrame:CGRectMake((kScreenWidth/3-30)/2+17, 62, 16, 16)];
    _HDLabel.font = kFontRegular(12);
    _HDLabel.textColor = [UIColor whiteColor];
    _HDLabel.textAlignment = NSTextAlignmentCenter;
    _HDLabel.backgroundColor = [UIColor redColor];
    _HDLabel.layer.cornerRadius = 8;
    _HDLabel.clipsToBounds = YES;
    _HDLabel.hidden = YES;
    [orderView addSubview:_HDLabel];

    UILabel *waitingPaymentLabel = [[UILabel alloc]initWithFrame:CGRectMake(0, 106, kScreenWidth/3, 20)];
    waitingPaymentLabel.text = @"待付款";
    waitingPaymentLabel.font = kFontLight(16);
    waitingPaymentLabel.textAlignment = NSTextAlignmentCenter;
    waitingPaymentLabel.textColor = [UIColor br_textBlackColor];
    [orderView addSubview:waitingPaymentLabel];

    UIButton *waitingPaymentButton = [[UIButton alloc]initWithFrame:CGRectMake(0, 46, kScreenWidth/3, 100)];
    waitingPaymentButton.tag = 102;
    [waitingPaymentButton addTarget:self action:@selector(buttonClick:) forControlEvents:UIControlEventTouchUpInside];
    [orderView addSubview:waitingPaymentButton];

    //已完成
    UIImageView *finishedImageView = [[UIImageView alloc]initWithFrame:CGRectMake(kScreenWidth/3+(kScreenWidth/3-30)/2, 66, 30, 30)];
    finishedImageView.image = [UIImage imageNamed:@"manager_yiwancheng"];
    [orderView addSubview:finishedImageView];

    UILabel *finishedLabel = [[UILabel alloc]initWithFrame:CGRectMake(kScreenWidth/3, 106, kScreenWidth/3, 20)];
    finishedLabel.text = @"已完成";
    finishedLabel.font = kFontLight(16);
    finishedLabel.textAlignment = NSTextAlignmentCenter;
    finishedLabel.textColor = [UIColor br_textBlackColor];
    [orderView addSubview:finishedLabel];

    UIButton *finishedButton = [[UIButton alloc]initWithFrame:CGRectMake(kScreenWidth/3, 46, kScreenWidth/3, 100)];
    finishedButton.tag = 101;
    [finishedButton addTarget:self action:@selector(buttonClick:) forControlEvents:UIControlEventTouchUpInside];
    [orderView addSubview:finishedButton];

    //未完成
    UIImageView *unfinishedImageView = [[UIImageView alloc]initWithFrame:CGRectMake(kScreenWidth/3*2+(kScreenWidth/3-30)/2, 66, 30, 30)];
    unfinishedImageView.image = [UIImage imageNamed:@"manager_weiwancheng"];
    [orderView addSubview:unfinishedImageView];

    UILabel *unfinishedLabel = [[UILabel alloc]initWithFrame:CGRectMake(kScreenWidth/3*2, 106, kScreenWidth/3, 20)];
    unfinishedLabel.text = @"已过期";
    unfinishedLabel.font = kFontLight(16);
    unfinishedLabel.textAlignment = NSTextAlignmentCenter;
    unfinishedLabel.textColor = [UIColor br_textBlackColor];
    [orderView addSubview:unfinishedLabel];

    UIButton *unfinishedButton = [[UIButton alloc]initWithFrame:CGRectMake(kScreenWidth/3*2, 46, kScreenWidth/3, 100)];
    unfinishedButton.tag = 107;
    [unfinishedButton addTarget:self action:@selector(buttonClick:) forControlEvents:UIControlEventTouchUpInside];
    [orderView addSubview:unfinishedButton];


    UIView *managerView = [[UIView alloc] initWithFrame:CGRectMake(0, 166, kScreenWidth, 400)];
    managerView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:managerView];

    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
    layout.minimumInteritemSpacing = 0;
    layout.minimumLineSpacing = 0;
    [layout setScrollDirection:UICollectionViewScrollDirectionVertical];

    self.collectionView = [[UICollectionView alloc] initWithFrame:managerView.bounds collectionViewLayout:layout];
    [managerView addSubview:self.collectionView];
    self.collectionView.backgroundColor = [UIColor clearColor];

    //注册 UICollectionViewCell
    [self.collectionView registerClass:[UserCenterPanelCollectionViewCell class] forCellWithReuseIdentifier:kcellIdentifier];

    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;

}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];

    [self.navigationController setNavigationBarHidden:NO animated:YES];
    self.title = @"管理";
    //去掉navigationbar底部线条
//    self.navigationController.navigationBar.clipsToBounds = NO;

    isShowHud = @"";

    UINavigationBar *navigationBar = self.navigationController.navigationBar;
    [navigationBar showBottomHairline];

    [self waitingForPaymentCount];
    [self accessToDisplayStatus];
    [self requestAuthState]; // 请求认证状态
}

// 请求认证状态
- (void)requestAuthState {
    // 检查是否为邀请用户
    NSString *userId = [UserManager shareInstance].getUserId;
    NSString *inviteUserKey = [NSString stringWithFormat:@"is_invite_user_%@", userId];
    BOOL isInviteUser = [[NSUserDefaults standardUserDefaults] boolForKey:inviteUserKey];
    if (isInviteUser) {
        // 如果是邀请用户，创建一个默认的认证状态对象
        if (self.authStateResult == nil) {
            self.authStateResult = [[DoctorAuthentiationModel alloc] init];
        }
        self.authStateResult.inviteUser = YES;
        NSLog(@"ManagerViewController: 用户是被邀请的医生，无需请求认证状态");
        return;
    }

    // 如果不是邀请用户，则请求认证状态
    NSDictionary *dict = @{@"method_code":@"000045"};

    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {

    } success:^(NSURLSessionDataTask *task, id responseObject) {
        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {
            // 保存认证状态
            DoctorAuthentiationModel *model = [DoctorAuthentiationModel mj_objectWithKeyValues:responseObject];
            self.authStateResult = model;

            // 保存邀请用户状态
            NSString *userId = [UserManager shareInstance].getUserId;
            NSString *inviteUserKey = [NSString stringWithFormat:@"is_invite_user_%@", userId];
            [[NSUserDefaults standardUserDefaults] setBool:model.inviteUser forKey:inviteUserKey];

            // 保存认证状态
//            NSString *authState = [NSString stringWithFormat:@"%ld", (long)model.currentAuthenticationState];
//            [[NSUserDefaults standardUserDefaults] setObject:authState forKey:@"auth_state"];
//            [[NSUserDefaults standardUserDefaults] synchronize];

            NSLog(@"ManagerViewController: 认证状态获取成功, isInviteUser: %d, currentAuthenticationState: %ld",
                  model.inviteUser, (long)model.currentAuthenticationState);
        } else {
            NSLog(@"ManagerViewController: 认证状态获取失败");
            // 创建默认对象
            if (self.authStateResult == nil) {
                self.authStateResult = [[DoctorAuthentiationModel alloc] init];
                self.authStateResult.inviteUser = NO;
                self.authStateResult.currentAuthenticationState = DoctorAuthentiationStateNotAuth;
            }
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"ManagerViewController: 认证状态获取失败: %@", error);
        // 创建默认对象
        if (self.authStateResult == nil) {
            self.authStateResult = [[DoctorAuthentiationModel alloc] init];
            self.authStateResult.inviteUser = NO;
            self.authStateResult.currentAuthenticationState = DoctorAuthentiationStateNotAuth;
        }
    }];
}

- (void)reloadCollectionView {

    _panelModelArray = nil;

    [_collectionView reloadData];
}
#pragma mark - UICollectionViewDataSource
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {

    NSInteger result = 0;


    result = self.panelModelArray.count;

    return result;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {

    UserCenterPanelCollectionViewCell *panelCell = (UserCenterPanelCollectionViewCell *)[collectionView dequeueReusableCellWithReuseIdentifier:kcellIdentifier forIndexPath:indexPath];

    PanelIconModel *model = [self.panelModelArray objectAtIndex:indexPath.row];

    panelCell.title = model.title;
    panelCell.iconImage = [UIImage imageNamed:model.iconName];

    //weakSelf
    __weak typeof(self) weakSelf = self;

    if ([model.title isEqualToString:@"扫码开方"]) {
        panelCell.issueButtonHidden = NO;
        panelCell.clickIussueButtonBlock = ^{
            [Utils br_showInviteScanPrescribeWithBlock:^{
                InviteScanPrescribeViewController *inviteScanVC = [[InviteScanPrescribeViewController alloc] init];
                inviteScanVC.hidesBottomBarWhenPushed = YES;
                [weakSelf.navigationController pushViewController:inviteScanVC animated:YES];
            }];
        };
    }else if([model.title isEqualToString:@"微信开方"]){
        panelCell.issueButtonHidden = NO;
        panelCell.clickIussueButtonBlock = ^{
//            [Utils br_showQuickPrescribeHintWithBlock:^{
//                BRQuickPrescribeViewController *quickPrescribeVC = [[BRQuickPrescribeViewController alloc] init];
//                quickPrescribeVC.hidesBottomBarWhenPushed = YES;
//                [weakSelf.navigationController pushViewController:quickPrescribeVC animated:YES];
//            }];

            [Utils br_showQuickPrescribeHintWithIsMessage:NO Block:^{
                BRQuickPrescribeViewController *quickPrescribeVC = [[BRQuickPrescribeViewController alloc] init];
                quickPrescribeVC.hidesBottomBarWhenPushed = YES;
                [weakSelf.navigationController pushViewController:quickPrescribeVC animated:YES];
            }];
        };
    }else if ([model.title isEqualToString:@"短信开方"]){
        panelCell.issueButtonHidden = NO;
        panelCell.clickIussueButtonBlock = ^{
//            [Utils br_showQuickPrescribeHintWithBlock:^{
//                BRMessagePrescribeViewController *messagePrescribeVC = [[BRMessagePrescribeViewController alloc] init];
//                messagePrescribeVC.hidesBottomBarWhenPushed = YES;
//                [weakSelf.navigationController pushViewController:messagePrescribeVC animated:YES];
//            }];

            [Utils br_showQuickPrescribeHintWithIsMessage:YES Block:^{
                BRMessagePrescribeViewController *messagePrescribeVC = [[BRMessagePrescribeViewController alloc] init];
                messagePrescribeVC.hidesBottomBarWhenPushed = YES;
                [weakSelf.navigationController pushViewController:messagePrescribeVC animated:YES];
            }];
        };
    }else{
        panelCell.issueButtonHidden = YES;
    }

    return panelCell;
}

#pragma mark - UICollectionViewDelegate
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {

    //网络监测 如果断网 则停止下载 直到网络连接成功后重新进行
    YYReachability *reachability = [YYReachability reachabilityWithHostname:@"www.baidu.com"];
    if (reachability.status == YYReachabilityStatusNone) {
        //无网络
        [self.view makeToast:@"联网失败，请检查网络设置！" duration:2 position:CSToastPositionCenter];
        return;
    }
    if ([isShowHud isEqualToString:@"1"]) {
        [self waitingForPaymentCount];
        [self accessToDisplayStatus];
        return;
    }

    if (indexPath.row == 0) {

        //常用方管理
        CommonlyUsedPartyViewController *commonlyUsedParty = [[CommonlyUsedPartyViewController alloc] init];
        commonlyUsedParty.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:commonlyUsedParty animated:YES];

    }else if (indexPath.row == 1){

        //出诊设置
        BRVisitsArrangementViewController *visitsVC = [[BRVisitsArrangementViewController alloc] init];
        visitsVC.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:visitsVC animated:YES];

    }else if (indexPath.row == 2){

        //服务设置
        ServiceSettingViewController *serviceSetting = [[ServiceSettingViewController alloc] init];
        serviceSetting.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:serviceSetting animated:YES];

    }else if (indexPath.row == 3){

        //我的钱包
//        BRMyPurseViewController *purse = [[BRMyPurseViewController alloc] init];
//        purse.hidesBottomBarWhenPushed = YES;
//        [self.navigationController pushViewController:purse animated:YES];
        MyPurseViewController *purseVC = [[MyPurseViewController alloc] init];
        purseVC.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:purseVC animated:YES];

    }else if (indexPath.row == 4){

        //我的公告
        MyAnnouncementViewController *myAnnouncement = [[MyAnnouncementViewController alloc] init];
        myAnnouncement.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:myAnnouncement animated:YES];

    }else if (indexPath.row == 5){
        //黑名单
        BlacklistViewController *blackList = [[BlacklistViewController alloc] init];
        blackList.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:blackList animated:YES];

    }else if (indexPath.row == 6){
        //药典查询
        PharmacopoeiaViewController *pharmacopoeia = [[PharmacopoeiaViewController alloc] init];
        pharmacopoeia.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:pharmacopoeia animated:YES];

    }else if (indexPath.row == 7){
        //扫码开方
        // 检查是否有权限使用扫码开方
        NSLog(@"ManagerViewController: 扫码开方, 使用最新认证状态判断");
        [AuthCheckHelper canUseAuthFeatureWithLatestState:self completion:^(BOOL canUse) {
            if (canUse) {
                InviteScanPrescribeViewController *inviteScanVC = [[InviteScanPrescribeViewController alloc] init];
                inviteScanVC.hidesBottomBarWhenPushed = YES;
                [self.navigationController pushViewController:inviteScanVC animated:YES];
            }
        }];
    }else if (indexPath.row == 8) {
        //微信开方
        NSLog(@"ManagerViewController: 微信开方, 使用最新认证状态判断");
        [AuthCheckHelper canUseAuthFeatureWithLatestState:self completion:^(BOOL canUse) {
            if (canUse) {
                BRQuickPrescribeViewController *quickPrescribeVC = [[BRQuickPrescribeViewController alloc] init];
                quickPrescribeVC.hidesBottomBarWhenPushed = YES;
                [self.navigationController pushViewController:quickPrescribeVC animated:YES];
            }
        }];
    }else if (indexPath.row == 9) {
        //短信开方
        NSLog(@"ManagerViewController: 短信开方, 使用最新认证状态判断");
        [AuthCheckHelper canUseAuthFeatureWithLatestState:self completion:^(BOOL canUse) {
            if (canUse) {
                BRMessagePrescribeViewController *messagePrescribeVC = [[BRMessagePrescribeViewController alloc] init];
                messagePrescribeVC.hidesBottomBarWhenPushed = YES;
                [self.navigationController pushViewController:messagePrescribeVC animated:YES];
            }
        }];
    }

//        //包含拍照开方
//        if ([_panelModelArray containsObject:_photoPrescriptionPanelIconModel]) {
//            //如果photoShowAndHidden为空那么不跳转拍照开方页面。
//            if (!_photoShowAndHidden) {
//                return;
//            }
//
//            [self jumpToPhotoPrescription];
//        }
//        //不包含拍照开方  则为养生铺
//        else {
//            [self jumpToDrugStoreList];
//        }
//    } else if (indexPath.row == 8) {
//
//        [self jumpToDrugStoreList];
//    }






}

//跳转至拍照开方订单
- (void)jumpToPhotoPrescription {
    PhotoPresViewController *photoPres = [[PhotoPresViewController alloc]init];
    photoPres.hidesBottomBarWhenPushed = YES;
    photoPres.showAndHidden = _photoShowAndHidden;
    [self.navigationController pushViewController:photoPres animated:YES];
}

//跳转至养生铺
- (void)jumpToDrugStoreList {

    BOOL rel = [Config firstOpenByID:[NSString stringWithFormat:@"managerToDrugStoreAgree_%@",[UserManager shareInstance].getUserId]];

    if (rel) {
        PhotoPrescAgreementViewController *agreementVC = [[PhotoPrescAgreementViewController alloc] init];
        agreementVC.controllerStr = @"ManagerViewController";
        agreementVC.hidesBottomBarWhenPushed = YES;
        agreementVC.isYspFlag = YES;
        [self.navigationController pushViewController:agreementVC animated:YES];
    }
    else {
        DrugStoreViewController *drugStoreVC = [[DrugStoreViewController alloc] init];
        drugStoreVC.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:drugStoreVC animated:YES];

    }
}

#pragma mark - UICollectionViewDelegateFlowLayout
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {

    CGSize size = CGSizeZero;

    size = CGSizeMake(SCREEN_WIDTH / 3, 300 / 3);

    return size;
}

#pragma mark- lazy load
- (NSMutableArray *)panelModelArray {
    if (!_panelModelArray) {
        _panelModelArray = [NSMutableArray new];

        NSArray *titleArray = @[NSLocalizedString(@"常用方管理", nil),
                                NSLocalizedString(@"出诊管理", nil),
                                NSLocalizedString(@"服务设置", nil),
                                NSLocalizedString(@"我的钱包", nil),
                                NSLocalizedString(@"我的公告", nil),
                                NSLocalizedString(@"黑名单", nil),
                                NSLocalizedString(@"药典查询", nil),
                                NSLocalizedString(@"扫码开方", nil),
                                NSLocalizedString(@"微信开方", nil),
//                                NSLocalizedString(@"短信开方", nil)
//                                NSLocalizedString(@"电子书", nil),
//                                NSLocalizedString(@"拍照药方订单", nil),
//                                @"养生建议"
        ];
        NSArray *iconArray = @[@"manager_changyongfang",
                               @"manager_chuzhen",
                               @"manager_fuwushezhi",
                               @"manager_wodeqianbao",
//                               @"manager_gonggao",
                               @"book.pages",
                               @"manager_heimingdan",
                               @"manager_yaodian",
//                               @"manager_yaodian",
//                               @"navi_barcode_btn",
                               @"qrcode.viewfinder",
                               @"share_WeChat",
//                               @"message-icon"
//                               @"manager_photoPrescription",
//                               @"manager_yangshengpu"
        ];

//        _photoPrescriptionPanelIconModel = [PanelIconModel new];
//        _photoPrescriptionPanelIconModel.iconName = @"manager_photoPrescription";
//        _photoPrescriptionPanelIconModel.title = @"拍照药方订单";
//

        for (int i = 0; i < titleArray.count; i++) {
            PanelIconModel *model = [PanelIconModel new];
            model.title = [titleArray objectAtIndex:i];
            model.iconName = [iconArray objectAtIndex:i];

            if ([model.iconName isEqualToString:@"manager_photoPrescription"]) {
                _photoPrescriptionPanelIconModel = model;
            }

            if ([model.iconName isEqualToString:@"manager_yangshengpu"]) {
                _drugStorePanelIconModel = model;
            }

            [_panelModelArray addObject:model];
        }

    }
    return _panelModelArray;
}

#pragma mark - buttonClick:
- (void)buttonClick:(UIButton *)sender
{
    //网络监测 如果断网 则停止下载 直到网络连接成功后重新进行
    YYReachability *reachability = [YYReachability reachabilityWithHostname:@"www.baidu.com"];
    if (reachability.status == YYReachabilityStatusNone) {
        //无网络
        [self.view makeToast:@"联网失败，请检查网络设置！" duration:2 position:CSToastPositionCenter];
        return;
    }
    if ([isShowHud isEqualToString:@"1"]) {
        [self waitingForPaymentCount];
        [self accessToDisplayStatus];
        return;
    }
    OrderViewController *order = [[OrderViewController alloc] init];
    order.payState = sender.tag;
    order.showAndHidden = _showAndHidden;
    order.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:order animated:YES];
}
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}
#pragma mark - 获取待付款角标
- (void)waitingForPaymentCount
{
    //网络监测 如果断网 则停止下载 直到网络连接成功后重新进行
    YYReachability *reachability = [YYReachability reachabilityWithHostname:@"www.baidu.com"];
    if (reachability.status == YYReachabilityStatusNone) {
        //无网络
        [self.view makeToast:@"联网失败，请检查网络设置！" duration:2 position:CSToastPositionCenter];
        return;
    }
    NSDictionary *dict = @{@"method_code":@"000209"
                           };

    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {

    } success:^(NSURLSessionDataTask *task, id responseObject) {

        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {

            if ([[responseObject objectForKey:@"data"] integerValue] > 0) {

                _HDLabel.hidden = NO;
                _HDLabel.text = [NSString stringWithFormat:@"%@",[responseObject objectForKey:@"data"]];
            } else {
                _HDLabel.hidden = YES;
            }
            isShowHud = @"2";

        } else {
            isShowHud = @"1";
        }

    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        isShowHud = @"1";
    }];
}

#pragma mark = 显示隐藏
- (void)accessToDisplayStatus
{
    NSDictionary *dict = @{@"method_code"   :@"000207",
                           @"userId"        :[UserManager shareInstance].getUserId
                           };
    MBProgressHUD *hud;

    if ([isShowHud isEqualToString:@"1"]) {
        hud = [Utils createLoadingHUD];
    }

    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {

    } success:^(NSURLSessionDataTask *task, id responseObject) {

        if ([[responseObject objectForKey:@"code"] isEqualToString:@"0000"]) {

            _showAndHidden = [[responseObject objectForKey:@"data"] objectForKey:@"order_list_display"];
            _photoShowAndHidden = [[responseObject objectForKey:@"data"] objectForKey:@"photo_order_list_display"];
            isShowHud = @"2";

        } else {
            isShowHud = @"1";
        }
        [hud hideAnimated:YES];
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [hud hideAnimated:YES];
        isShowHud = @"1";
    }];
}

@end
