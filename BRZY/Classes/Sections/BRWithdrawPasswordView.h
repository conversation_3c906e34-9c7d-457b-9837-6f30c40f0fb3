//
//  BRWithdrawPasswordView.h
//  BRZY
//
//  Created by AI Assistant on 2024/12/19.
//  Copyright © 2024 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

// 提现密码验证弹窗回调类型定义
typedef void(^WithdrawPasswordBlock)(NSString *password);
typedef void(^WithdrawPasswordCancelBlock)(void);

@interface BRWithdrawPasswordContent : UIView

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *yes;
@property (nonatomic, strong) UIButton *cancel;
@property (nonatomic, strong) UITextField *passwordTextField;

- (instancetype)initWithFrame:(CGRect)frame 
                    withBlock:(WithdrawPasswordBlock)passwordBack 
                 cancelBlock:(WithdrawPasswordCancelBlock)cancelBtnClick;

@end

@interface BRWithdrawPasswordView : UIView

@property (nonatomic, strong) BRWithdrawPasswordContent *selectViewList;
@property (nonatomic, assign) BOOL isHideWhenTapBackground;

- (instancetype)initWithPasswordBlock:(WithdrawPasswordBlock)backPassword 
                          cancelBlock:(WithdrawPasswordCancelBlock)cancelClick;
- (void)removeView;

@end

NS_ASSUME_NONNULL_END 