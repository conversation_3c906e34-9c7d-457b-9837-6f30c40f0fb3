//
//  LoginViewController.m
//  BRZY
//
//  Created by  xujiangta<PERSON> on 2017/8/24.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "LoginViewController.h"
#import "LoginInputView.h"
#import "RegisterViewController.h"
#import "FindPasswordViewController.h"
#import "PersonalDataViewController.h"
#import "BRDebugInfoViewController.h"
#import "DataInitLoadingViewController.h"
#import "JTPrivacyWebViewController.h"
// 已移除BROneClickLoginViewController，一键登录逻辑现在在当前类中处理
#import "BRAuthConfig.h"

// 导入阿里云号码认证SDK
#import <ATAuthSDK/ATAuthSDK.h>

@interface LoginViewController ()<LoginInputViewDelegate, UITextViewDelegate>

//@property (strong, nonatomic) UIButton *passwordButton;//密码选项按钮
//@property (strong, nonatomic) UIButton *authcodeButton;//验证码选项按钮
//@property (strong, nonatomic) UIView *segmentLiveView;//选项卡下条

@property (strong, nonatomic) UISegmentedControl *segment;

@property (strong, nonatomic) UIButton *loginButton;
@property (strong, nonatomic) UIButton *registerButton;
@property (strong, nonatomic) UIButton *oneClickLoginButton;

@property (strong, nonatomic) LoginInputView *loginInputView;

@property (nonatomic, assign)NSInteger clickNum;   //点击次数

@property (nonatomic, assign) BOOL isLogined;

@property (nonatomic, assign) BOOL isOneClickLogining;

@end

@implementation LoginViewController
{
    CGFloat segmentHeight;
}
- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self configUI];
    
//    [self.passwordButton setSelected:YES];
    //如果已经登录并且需要完善资料 则进入完善资料页面
    if ([UserManager shareInstance].isLogin && [Config getUserisNeedCompleteUserData]) {
        //更新区域信息资料
        ConfigInfo *info = [[ConfigInfo alloc] init];
        info.area_addressVersion = [Config getArea_addressVersion];
        [[Config shareInstance] updateAreaInfo:info];
        //设置为自动登录
        [Config storeisAutoLogin:YES];
        //直接进入完善资料页面
        PersonalDataViewController *dataVC = [[PersonalDataViewController alloc] init];
        [self.navigationController pushViewController:dataVC animated:NO];
    }
    
//    if ([Config getisNeedAgreePrivacy]) {
//        __weak __typeof(self)weakSelf = self;
//        [[Utils sharedInstance] showPrivacyPopViewTapPrivacy:^{
//            NSLog(@"隐私政策====");
//            JTPrivacyWebViewController *privacyVC = [[JTPrivacyWebViewController alloc] init];
//            privacyVC.url = kPrivacyUrl;
//            privacyVC.title = @"隐私政策";
//            [weakSelf.navigationController pushViewController:privacyVC animated:YES];
//        } tapUserAgreement:^{
//            NSLog(@"用户协议====");
//            JTPrivacyWebViewController *privacyVC = [[JTPrivacyWebViewController alloc] init];
//            privacyVC.url = kUserAgreementUrl;
//            privacyVC.title = @"用户服务协议";
//            [weakSelf.navigationController pushViewController:privacyVC animated:YES];
//        }];
//    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.clickNum = 0;
    self.navigationController.navigationBar.hidden = YES;
    
    if (@available(iOS 15, *)) {
        UINavigationBarAppearance *appearance = [UINavigationBarAppearance new];
        appearance.backgroundEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleRegular];
        self.navigationController.navigationBar.scrollEdgeAppearance = appearance;
    }
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleDefault;
}
#pragma mark- ConfigUI
- (void)configUI {
    
    segmentHeight = 30;
    
    [self.view addSubview:self.segment];
    
    [self.view addSubview:self.loginInputView];
    
    [self.view addSubview:self.loginButton];
    [self.view addSubview:self.registerButton];
    [self.view addSubview:self.oneClickLoginButton];
    
    self.loginInputView.delegate = self;
    
    if (self.segment) {
        [self.segment mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(@(58));
            make.centerX.equalTo(self.view);
            make.size.mas_equalTo(CGSizeMake(202, segmentHeight));
        }];
    }
    
    if (self.loginInputView) {
        [self.loginInputView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(SCREEN_WIDTH, 120));
            make.left.and.right.equalTo(self.view);
            make.top.equalTo(self.segment.mas_bottom).with.offset(60);
        }];
    }
    
    if (self.loginButton) {
        [self.loginButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(@(20));
            make.right.mas_equalTo(@(-20));
            make.top.equalTo(self.loginInputView.mas_bottom).with.offset(45);
            make.height.mas_equalTo(@(kButtonHeight));
        }];
    }
    
    if (self.oneClickLoginButton) {
        [self.oneClickLoginButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(@(20));
            make.right.mas_equalTo(@(-20));
            make.top.equalTo(self.loginButton.mas_bottom).with.offset(20);
            make.height.mas_equalTo(@(kButtonHeight));
        }];
    }
    
    if (self.registerButton) {
        [self.registerButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.oneClickLoginButton.mas_bottom).with.offset(10);
//            make.left.and.right.and.height.equalTo(self.loginButton);
            make.size.mas_equalTo(CGSizeMake(100, 16));
            make.right.equalTo(self.oneClickLoginButton.mas_right).with.offset(10);
        }];
    }
    
    [self.view addSubview:[self clickNNumBtn:CGRectMake(0, self.view.frame.size.height-FONTSIZE(60), FONTSIZE(60), FONTSIZE(60))]];
    
    NSString *privacyText = @"为了保障您的个人权益，请在点击 登录 按钮前认真阅读以下协议：\n 《隐私政策》和《用户服务协议》";
    
    //添加隐私协议
    UITextView *textView = [UITextView new];
    textView.delegate = self;
    textView.editable = NO;
    textView.textAlignment = NSTextAlignmentCenter;
    [self.view addSubview:textView];
    
    [textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(@20);
        make.right.mas_equalTo(@-20);
        make.bottom.mas_equalTo(@-50);
        make.height.mas_equalTo(@50);
    }];
    
    
    NSRange userRanage = [privacyText rangeOfString:@"《隐私政策》"];
    NSRange serverRange = [privacyText rangeOfString:@"《用户服务协议》"];
    
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:privacyText attributes:@{
        NSForegroundColorAttributeName : [UIColor grayColor],
        NSFontAttributeName : [UIFont systemFontOfSize:12 weight:UIFontWeightRegular]
    }];
    
    [attributedString addAttribute:NSLinkAttributeName value:@"http://privacy" range:userRanage];
    
    [attributedString addAttribute:NSLinkAttributeName value:@"http://userAgreement" range:serverRange];
    
    //居中
    NSMutableParagraphStyle *paragraph = [[NSMutableParagraphStyle alloc] init];
    paragraph.alignment = NSTextAlignmentCenter;
    
    [attributedString addAttribute:NSParagraphStyleAttributeName value:paragraph range:NSMakeRange(0, privacyText.length)];
    
    textView.attributedText = attributedString;
    
    
    
}
#pragma mark - UITextViewDelegate
- (BOOL)textView:(UITextView *)textView shouldInteractWithURL:(NSURL *)URL inRange:(NSRange)characterRange interaction:(UITextItemInteraction)interaction {
    
    //隐私政策
    if ([URL.absoluteString isEqualToString:@"http://privacy"]) {
        NSLog(@"隐私政策 === ");
        
        JTPrivacyWebViewController *privacyVC = [[JTPrivacyWebViewController alloc] init];
        privacyVC.url = kPrivacyUrl;
        privacyVC.title = @"隐私政策";
        [self.navigationController pushViewController:privacyVC animated:YES];
        
    }
    //用户服务协议
    else if ([URL.absoluteString isEqualToString:@"http://userAgreement"]) {
        NSLog(@"用户服务协议");
        
        JTPrivacyWebViewController *privacyVC = [[JTPrivacyWebViewController alloc] init];
        privacyVC.url = kUserAgreementUrl;
        privacyVC.title = @"用户服务协议";
        [self.navigationController pushViewController:privacyVC animated:YES];
    }
    
    return NO;
}

#pragma mark - Click Event
- (void)clickLoginButton:(UIButton *)sender {
    
    NSString *phone = nil;
    NSString *password = nil;
    NSString *authcode = nil;
    //开始登录
    if (self.loginInputView.loginTabType == BRLoginTabTypePassword) {
        
        [self.view endEditing:YES];
        //使用密码登录
        phone = self.loginInputView.phoneTextField.text;
        password = self.loginInputView.passwordTextField.text;
        
        if ([phone isEqualToString:@""] || (phone.length > 0 && phone.length < 11)) {
            [self.view makeToast:@"请输入11位手机号" duration:kToastDuration position:CSToastPositionCenter];
            return;
        }
        
        if ([password isEqualToString:@""] || password.length < 6) {
            [self.view makeToast:@"请输入6位以上密码" duration:kToastDuration position:CSToastPositionCenter];
            return;
        }
    }
    //使用验证码登录
    else if (self.loginInputView.loginTabType == BRLoginTabTypeAuthcode){
        phone = self.loginInputView.phoneAuthTextField.text;
        authcode = self.loginInputView.authCodeTextField.text;
        
        if ([phone isEqualToString:@""] || (phone.length > 0 && phone.length < 11)) {
            [self.view makeToast:@"请输入11位手机号" duration:kToastDuration position:CSToastPositionCenter];
            return;
        }
        
        //未点击获取验证码 则提示需要获取验证码
        if (!self.loginInputView.isGetAuthcode) {
            [self.view makeToast:@"请获取验证码" duration:kToastDuration position:CSToastPositionCenter];
            return;
        }
        
        if ([authcode isEqualToString:@""]) {
            [self.view makeToast:@"请输入验证码" duration:kToastDuration position:CSToastPositionCenter];
            return;
        }
    }
    
    YYReachability *reachability = [YYReachability reachabilityWithHostname:@"www.baidu.com"];
    if (reachability.status == YYReachabilityStatusNone) {
        
        [self.view makeToast:@"联网失败，请检查手机网络状态。" duration:kToastDuration position:CSToastPositionCenter];
        return;
    }
    
    MBProgressHUD *HUD = [Utils createLoadingHUDWithTitle:NSLocalizedString(@"on login", nil)];
    
    _isLogined = NO;
    
    [[UserManager shareInstance] loginUsername:phone password:password sms:authcode success:^(ConfigInfo *info) {
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            HUD.mode = MBProgressHUDModeText;
            HUD.label.text = NSLocalizedString(@"login successful", nil);
            HUD.label.textColor = [UIColor whiteColor];
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [HUD hideAnimated:YES];
                
                if (self.isLogined) {
                    return;
                }
                
                if ([Config getUserisNeedCompleteUserData]) {
                    //需要完善资料
                    PersonalDataViewController *dataVC = [[PersonalDataViewController alloc] init];
                    [self.navigationController pushViewController:dataVC animated:YES];
                }else{
                    //进入初始化数据页面
                    DataInitLoadingViewController *dataInitLoadingVC = [[DataInitLoadingViewController alloc] init];
                    dataInitLoadingVC.configInfo = info;
                    [self.navigationController pushViewController:dataInitLoadingVC animated:YES];
                }
                
                self.isLogined = YES;
            });
        });
    } failed:^(BRError *error) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            HUD.mode = MBProgressHUDModeText;
            HUD.label.textColor = [UIColor whiteColor];
            HUD.label.text = error.errorDescription;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [HUD hideAnimated:YES];
            });
        });
    }];
}

- (void)clickRegisterButton:(UIButton *)sender {
    RegisterViewController *registerViewController = [[RegisterViewController alloc] init];
    [self.navigationController pushViewController:registerViewController animated:YES];
}

- (void)clickOneClickLoginButton:(UIButton *)sender {
//    BRLog(@"一键登录按钮被点击");
    
    // 立即显示点击反馈
    [self.view makeToast:@"按钮点击成功，正在处理..." duration:0.5 position:CSToastPositionCenter];
    
    if (self.isOneClickLogining) {
        BRLog(@"一键登录正在进行中，忽略重复点击");
        return;
    }
    
    // 检查是否已配置SDK
    if (![BRAuthConfig isATAuthSDKConfigured]) {
        BRLog(@"SDK未配置，一键登录功能暂时不可用");
        [self.view makeToast:@"一键登录功能暂时不可用，请使用密码或验证码登录" duration:kToastDuration position:CSToastPositionCenter];
        return;
    }
    
    // 检查网络
    YYReachability *reachability = [YYReachability reachabilityWithHostname:@"www.baidu.com"];
    if (reachability.status == YYReachabilityStatusNone) {
        BRLog(@"网络不可用");
        [self.view makeToast:@"联网失败，请检查手机网络状态。" duration:kToastDuration position:CSToastPositionCenter];
        return;
    }
    
    BRLog(@"开始一键登录流程");
    self.isOneClickLogining = YES;
    [sender setEnabled:NO];
    
    // 设置超时处理
    __weak __typeof(self) weakSelf = self;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if (weakSelf.isOneClickLogining) {
            BRLog(@"一键登录超时，使用备用方案");
            weakSelf.isOneClickLogining = NO;
            [sender setEnabled:YES];
            [weakSelf.view makeToast:@"一键登录超时，请使用密码或验证码登录" duration:kToastDuration position:CSToastPositionCenter];
        }
    });
    
    // 初始化SDK
    NSString *appKey = [BRAuthConfig getATAuthSDKAppKey];
    BRLog(@"使用App Key初始化SDK: %@", [appKey substringToIndex:MIN(20, appKey.length)]);
    
    [[TXCommonHandler sharedInstance] setAuthSDKInfo:appKey complete:^(NSDictionary *resultDic) {
        dispatch_async(dispatch_get_main_queue(), ^{
            BRLog(@"SDK初始化结果: %@", resultDic);
            NSString *resultCode = resultDic[@"resultCode"];
            if ([resultCode isEqualToString:PNSCodeSuccess]) {
                BRLog(@"阿里云号码认证SDK初始化成功");
                // 检查环境支持
                [self checkEnvironmentAndStartOneClickLogin:sender];
            } else {
                BRLog(@"阿里云号码认证SDK初始化失败: %@", resultDic[@"msg"]);
                self.isOneClickLogining = NO;
                [sender setEnabled:YES];
                NSString *errorMsg = [NSString stringWithFormat:@"一键登录服务初始化失败: %@", resultDic[@"msg"] ?: @"未知错误"];
                [self.view makeToast:errorMsg duration:kToastDuration position:CSToastPositionCenter];
            }
        });
    }];
}

- (void)checkEnvironmentAndStartOneClickLogin:(UIButton *)sender {
    BRLog(@"开始检查一键登录环境");
    [[TXCommonHandler sharedInstance] checkEnvAvailableWithAuthType:PNSAuthTypeLoginToken complete:^(NSDictionary *resultDic) {
        dispatch_async(dispatch_get_main_queue(), ^{
            BRLog(@"环境检查结果: %@", resultDic);
            NSString *resultCode = resultDic[@"resultCode"];
            if ([resultCode isEqualToString:PNSCodeSuccess]) {
                BRLog(@"当前环境支持一键登录");
                // 预加载授权页面，提升用户体验
                [[TXCommonHandler sharedInstance] accelerateLoginPageWithTimeout:3.0 complete:^(NSDictionary *accelerateResult) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        BRLog(@"预加载结果: %@", accelerateResult);
                        // 启动一键登录
                        [self startOneClickLogin:sender];
                    });
                }];
            } else {
                BRLog(@"当前环境不支持一键登录: %@", resultDic[@"msg"]);
                self.isOneClickLogining = NO;
                [sender setEnabled:YES];
                NSString *errorMsg = [NSString stringWithFormat:@"当前环境不支持一键登录: %@", resultDic[@"msg"] ?: @"请使用密码或验证码登录"];
                [self.view makeToast:errorMsg duration:kToastDuration position:CSToastPositionCenter];
            }
        });
    }];
}

- (void)startOneClickLogin:(UIButton *)sender {
    // 创建自定义UI模型，与安卓端保持一致的样式
    TXCustomModel *customModel = [self createOneClickLoginCustomModel];
    
    __weak __typeof(self) weakSelf = self;
    [[TXCommonHandler sharedInstance] getLoginTokenWithTimeout:5.0 controller:self model:customModel complete:^(NSDictionary *resultDic) {
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [sender setEnabled:YES];
            weakSelf.isOneClickLogining = NO;
            
            NSString *resultCode = resultDic[@"resultCode"];
            BRLog(@"一键登录回调结果: %@", resultDic);
            
            if ([resultCode isEqualToString:PNSCodeSuccess]) {
                // 获取token成功
                NSString *token = resultDic[@"token"];
                // 先关闭一键登录授权页，再继续后续流程
                [[TXCommonHandler sharedInstance] cancelLoginVCAnimated:YES complete:^{
                    [weakSelf handleOneClickLoginSuccessWithToken:token];
                }];
                
            } else if ([resultCode isEqualToString:PNSCodeLoginControllerClickCancel]) {
                // 用户点击取消按钮
                BRLog(@"用户点击取消按钮");
                // 直接返回，不做任何操作
                
            } else if ([resultCode isEqualToString:PNSCodeLoginControllerClickChangeBtn]) {
                // 用户点击切换登录方式按钮
                BRLog(@"用户点击切换登录方式");
                // 关闭号码认证页面，返回到普通登录界面
                [[TXCommonHandler sharedInstance] cancelLoginVCAnimated:YES complete:^{
                    BRLog(@"一键登录授权页已关闭");
                }];
                // 用户主动切换登录方式，不需要显示错误提示
                return;
                
            } else if ([resultCode isEqualToString:PNSCodeLoginControllerClickLoginBtn]) {
                // 用户点击登录按钮，但可能协议未勾选
                BOOL isChecked = [resultDic[@"isChecked"] boolValue];
                if (!isChecked) {
                    [weakSelf.view makeToast:@"请先同意隐私协议" duration:kToastDuration position:CSToastPositionCenter];
                } else {
                    // 协议已勾选：SDK会返回“点击了登录按钮”等提示，这里不展示toast，避免干扰用户
                    BRLog(@"已勾选协议，忽略点击事件提示: %@", resultDic[@"msg"]);
                    // 不做提示，等待后续回调处理
                }
                
            } else {
                // 其他错误
                NSString *msg = resultDic[@"msg"];
                BRLog(@"一键登录失败: %@", resultDic);
                
                // 过滤“点击了登录按钮”等纯点击事件提示，不弹toast
                if ([msg isKindOfClass:[NSString class]] && msg.length > 0 && ![msg containsString:@"点击了登录按钮"]) {
                    [weakSelf.view makeToast:msg duration:kToastDuration position:CSToastPositionCenter];
                    
                    // 如果是超时或网络问题，给出更具体的提示
                    if ([resultCode containsString:@"timeout"] || [resultCode containsString:@"network"]) {
                        [weakSelf.view makeToast:@"网络超时，请检查网络连接后重试" duration:kToastDuration position:CSToastPositionCenter];
                    }
                }
            }
        });
    }];
}

- (void)handleOneClickLoginSuccessWithToken:(NSString *)token {
    if (!token || token.length == 0) {
        [self.view makeToast:@"获取登录凭证失败" duration:kToastDuration position:CSToastPositionCenter];
        return;
    }
    
    BRLog(@"=== iOS号码认证开始验证token ===");
    BRLog(@"Token: %@", token);
    BRLog(@"Token长度: %lu", (unsigned long)token.length);
    
    MBProgressHUD *HUD = [Utils createLoadingHUDWithTitle:@"验证中..."];
    
    // 第一步：调用HTTP API验证token（与安卓保持一致）
    [self callPhoneAuthAPIWithToken:token success:^(NSString *mobile) {
        BRLog(@"号码认证验证成功，获取到手机号: %@", mobile);
        // 第二步：验证成功后使用手机号登录
        [self performPhoneAuthLoginWithMobile:mobile HUD:HUD];
    } failed:^(BRError *error) {
        BRLog(@"号码认证验证失败: %@", error.errorDescription);
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            HUD.mode = MBProgressHUDModeText;
            HUD.label.text = error.errorDescription ?: @"号码认证失败，请使用密码或验证码登录";
            HUD.label.textColor = [UIColor whiteColor];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [HUD hideAnimated:YES];
            });
        });
    }];
}

#pragma mark - 号码认证API调用（与安卓保持一致）

/**
 * 调用HTTP API验证token（000452接口，与安卓保持一致）
 * @param token 从阿里云SDK获取的token
 * @param success 成功回调，返回手机号
 * @param failed 失败回调
 */
- (void)callPhoneAuthAPIWithToken:(NSString *)token success:(void(^)(NSString *mobile))success failed:(void(^)(BRError *error))failed {
    
    BRLog(@"=== 调用000452号码认证接口 ===");
    BRLog(@"accessToken: %@", token);
    BRLog(@"deviceKey: %@", [Config uuid]);
    
    // 构建请求参数（与安卓保持完全一致的参数格式）
    NSDictionary *params = @{
        @"method_code": @"000452",           // 号码认证接口
        @"accessToken": token,               // 从阿里云SDK获取的token
        @"deviceKey": [Config uuid]          // 设备标识，与安卓使用相同的uuid
    };
    
    BRLog(@"请求参数: %@", params);
    
    // 调用HTTP API（使用与安卓相同的接口）
    [HTTPRequest POST:kServerDomain parameters:params progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
        
        BRLog(@"=== 000452接口返回 ===");
        BRLog(@"响应: %@", responseObject);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            NSString *code = [responseObject objectForKey:@"code"];
            if ([code isEqualToString:@"0000"]) {
                // 解析返回结果（与安卓PhoneAuthResult保持一致）
                NSString *mobile = [responseObject objectForKey:@"mobile"];
                BOOL available = [[responseObject objectForKey:@"available"] boolValue];
                
                BRLog(@"mobile: %@", mobile);
                BRLog(@"available: %@", available ? @"true" : @"false");
                
                if (available && mobile.length > 0) {
                    BRLog(@"号码认证可用，手机号: %@", mobile);
                    if (success) {
                        success(mobile);
                    }
                } else {
                    BRLog(@"号码认证不可用或手机号为空");
                    NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
                    if (!errorMsg || errorMsg.length == 0) {
                        errorMsg = @"号码认证失败，请使用密码或验证码登录";
                    }
                    BRError *error = [BRError errorWithCode:BRErrorCodeAuthFailed description:errorMsg];
                    if (failed) {
                        failed(error);
                    }
                }
            } else {
                BRLog(@"接口返回错误，code: %@", code);
                NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
                if (!errorMsg || errorMsg.length == 0) {
                    errorMsg = @"号码认证失败，请使用密码或验证码登录";
                }
                BRError *error = [BRError errorWithCode:BRErrorCodeAuthFailed description:errorMsg];
                if (failed) {
                    failed(error);
                }
            }
        });
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        BRLog(@"000452接口请求失败: %@", error.localizedDescription);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            BRError *brError = [BRError errorWithCode:BRErrorCodeNetworkError 
                                          description:@"网络请求失败，请检查网络连接"];
            if (failed) {
                failed(brError);
            }
        });
    }];
}

/**
 * 使用手机号执行登录（与安卓performPhoneAuthLogin保持一致）
 * @param mobile 认证成功返回的手机号
 * @param HUD 加载提示框
 */
- (void)performPhoneAuthLoginWithMobile:(NSString *)mobile HUD:(MBProgressHUD *)HUD {
    if (!mobile || mobile.length == 0) {
        BRLog(@"手机号为空，无法进行自动登录");
        HUD.mode = MBProgressHUDModeText;
        HUD.label.text = @"获取手机号失败";
        HUD.label.textColor = [UIColor whiteColor];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [HUD hideAnimated:YES];
        });
        return;
    }
    
    BRLog(@"=== 开始执行号码认证自动登录 ===");
    BRLog(@"手机号: %@", mobile);
    
    HUD.label.text = @"登录中...";
    
    // 使用手机号进行登录（与安卓相同：使用空密码和验证码）
    [[UserManager shareInstance] loginUsername:mobile 
                                      password:@""     // 空密码 
                                           sms:@""     // 空验证码
                                       success:^(ConfigInfo *info) {
        
        BRLog(@"号码认证登录成功");
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            HUD.mode = MBProgressHUDModeText;
            HUD.label.text = @"登录成功";
            HUD.label.textColor = [UIColor whiteColor];
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [HUD hideAnimated:YES];
                
                if (self.isLogined) {
                    return;
                }
                
                if ([Config getUserisNeedCompleteUserData]) {
                    // 需要完善资料
                    PersonalDataViewController *dataVC = [[PersonalDataViewController alloc] init];
                    [self.navigationController pushViewController:dataVC animated:YES];
                } else {
                    // 进入初始化数据页面
                    DataInitLoadingViewController *dataInitLoadingVC = [[DataInitLoadingViewController alloc] init];
                    dataInitLoadingVC.configInfo = info;
                    [self.navigationController pushViewController:dataInitLoadingVC animated:YES];
                }
                
                self.isLogined = YES;
            });
        });
        
    } failed:^(BRError *error) {
        
        BRLog(@"号码认证登录失败: %@", error.errorDescription);
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            HUD.mode = MBProgressHUDModeText;
            HUD.label.text = error.errorDescription ?: @"登录失败，请使用密码或验证码登录";
            HUD.label.textColor = [UIColor whiteColor];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [HUD hideAnimated:YES];
            });
        });
    }];
}

- (TXCustomModel *)createOneClickLoginCustomModel {
    TXCustomModel *model = [[TXCustomModel alloc] init];
    
    // 设置状态栏
    model.prefersStatusBarHidden = NO;
    model.preferredStatusBarStyle = UIStatusBarStyleDefault;
    
    // 设置背景
    model.backgroundColor = [UIColor whiteColor];
    
    // 设置导航栏 - 与安卓端保持一致
    model.navIsHidden = NO;
    model.navColor = [UIColor whiteColor];
    model.navTitle = [[NSAttributedString alloc] initWithString:@"快速登录" attributes:@{
        NSForegroundColorAttributeName: [UIColor blackColor],
        NSFontAttributeName: [UIFont boldSystemFontOfSize:18]
    }];
//    model.navBackImage = [[UIImage imageNamed:@"navi_back_btn"] imageWithTintColor:[UIColor whiteColor]];
    
//    // 设置Logo - 与安卓端保持一致的尺寸和位置
//    model.logoImage = [UIImage imageNamed:@"logo_icon"];
    model.logoIsHidden = YES;
    model.logoFrameBlock = ^CGRect(CGSize screenSize, CGSize superViewSize, CGRect frame) {
        // 与安卓端一致：70x70尺寸，Y偏移100
        return CGRectMake((superViewSize.width - 70) / 2, 100, 70, 70);
    };
    
    // 设置Slogan - 在手机号上方显示提示文字
    model.sloganIsHidden = NO;
    model.sloganText = [[NSAttributedString alloc] initWithString:@"为了您的账号安全，请先验证手机号" attributes:@{
        NSForegroundColorAttributeName: [UIColor grayColor],
        NSFontAttributeName: [UIFont systemFontOfSize:14]
    }];
    model.sloganFrameBlock = ^CGRect(CGSize screenSize, CGSize superViewSize, CGRect frame) {
        // 在手机号上方显示，Y偏移160
        return CGRectMake(20, 150, superViewSize.width - 40, 20);
    };
    
    // 设置号码显示 - 横向居中显示
    model.numberColor = [UIColor blackColor];
    model.numberFont = [UIFont boldSystemFontOfSize:18];
//    __weak typeof(LoginViewController) *weakSelf = self;
    model.numberFrameBlock = ^CGRect(CGSize screenSize, CGSize superViewSize, CGRect frame) {
        NSLog(@"numberframe frame width = %f",frame.size.width);
        // 号码横向居中显示，在提示文字下方，Y偏移190
        return CGRectMake((kScreenWidth - frame.size.width) / 2.0, 190, superViewSize.width, 30);
    };
    
    // 设置登录按钮 - 与安卓端保持一致的文本和样式
    model.loginBtnText = [[NSAttributedString alloc] initWithString:@"本机号码一键登录" attributes:@{
        NSForegroundColorAttributeName: [UIColor whiteColor],
        NSFontAttributeName: [UIFont boldSystemFontOfSize:16]
    }];
    
//    model.loginBtnBgImgs = @[[UIImage imageWithColor:[UIColor br_mainBlueColor]]];
    
    // 设置登录按钮背景图片 - 创建带边框的图片
    UIImage *normalImage = [self createBorderedButtonImageWithBackgroundColor:[UIColor br_mainBlueColor]
                                                                  borderColor:[UIColor br_mainBlueColor]
                                                                  borderWidth:1.0 
                                                                 cornerRadius:kButtonHeight/2.0 
                                                                         size:CGSizeMake(280, 45)];
    UIImage *highlightImage = [self createBorderedButtonImageWithBackgroundColor:[[UIColor br_mainBlueColor] colorWithAlphaComponent:0.8]
                                                                     borderColor:[UIColor br_mainBlueColor]
                                                                     borderWidth:1.0 
                                                                    cornerRadius:kButtonHeight/2.0 
                                                                            size:CGSizeMake(280, 45)];
    model.loginBtnBgImgs = @[normalImage, normalImage, highlightImage];
    
    model.loginBtnFrameBlock = ^CGRect(CGSize screenSize, CGSize superViewSize, CGRect frame) {
        // 与安卓端一致：280x45尺寸，Y偏移240，居中
        return CGRectMake((superViewSize.width - 280) / 2, 240, 280, 45);
    };
    
    // 设置切换登录方式按钮 - 放在本机号码一键登录正下方
    model.changeBtnTitle = [[NSAttributedString alloc] initWithString:@"切换到其他方式" attributes:@{
        NSForegroundColorAttributeName: [UIColor grayColor],
        NSFontAttributeName: [UIFont systemFontOfSize:16]
    }];
    
    model.changeBtnIsHidden = NO;
    model.changeBtnFrameBlock = ^CGRect(CGSize screenSize, CGSize superViewSize, CGRect frame) {
        // 放在本机号码一键登录按钮正下方
        return CGRectMake((superViewSize.width - 120) / 2, 300, 120, 20);
    };
    
    // 设置隐私协议
    model.checkBoxIsChecked = YES;  // 默认勾选协议
    model.checkBoxIsHidden = NO;
    model.checkBoxWH = 15;
    
    // 设置协议内容
    model.privacyOne = @[@"《用户服务协议》、", kUserAgreementUrl];
    model.privacyTwo = @[@"《隐私政策》", kPrivacyUrl];
    model.privacyColors = @[[UIColor grayColor], [UIColor br_mainBlueColor]];
    model.privacyFont = [UIFont systemFontOfSize:12];
    model.privacyPreText = @"登录即表示同意";
    model.privacySufText = @"";
    model.privacyConectTexts = @[@"和", @""];
    model.privacyAlignment = NSTextAlignmentCenter;
    
    model.privacyFrameBlock = ^CGRect(CGSize screenSize, CGSize superViewSize, CGRect frame) {
        // 调整隐私协议位置，在切换按钮上方
        CGFloat privacyY = superViewSize.height - 160; // 距离底部160点
        return CGRectMake(20, privacyY, superViewSize.width - 40, 30);
    };
    
    // 设置协议页面导航栏样式
    model.privacyNavColor = [UIColor whiteColor];
    model.privacyNavTitleFont = [UIFont boldSystemFontOfSize:18];
    model.privacyNavTitleColor = [UIColor blackColor];
    model.privacyNavBackImage = [[UIImage imageNamed:@"navi_back_btn"] imageWithTintColor:[UIColor blackColor]];
    
    // 动画设置
    model.animationDuration = 0.3;
    model.presentDirection = PNSPresentationDirectionBottom;
    
    return model;
}

#pragma mark - 辅助方法
- (UIImage *)createBorderedButtonImageWithBackgroundColor:(UIColor *)backgroundColor 
                                               borderColor:(UIColor *)borderColor 
                                               borderWidth:(CGFloat)borderWidth 
                                              cornerRadius:(CGFloat)cornerRadius 
                                                      size:(CGSize)size {
    UIGraphicsBeginImageContextWithOptions(size, NO, [UIScreen mainScreen].scale);
    CGContextRef context = UIGraphicsGetCurrentContext();
    
    // 创建圆角矩形路径
    CGRect rect = CGRectMake(0, 0, size.width, size.height);
    UIBezierPath *roundedRectPath = [UIBezierPath bezierPathWithRoundedRect:rect cornerRadius:cornerRadius];
    
    // 设置裁剪区域为圆角矩形
    [roundedRectPath addClip];
    
    // 填充背景色
    CGContextSetFillColorWithColor(context, backgroundColor.CGColor);
    CGContextFillRect(context, rect);
    
    // 绘制边框
    if (borderWidth > 0) {
        CGContextSetStrokeColorWithColor(context, borderColor.CGColor);
        CGContextSetLineWidth(context, borderWidth);
        
        // 边框路径需要稍微内缩，避免被裁剪
        CGRect borderRect = CGRectInset(rect, borderWidth/2, borderWidth/2);
        UIBezierPath *borderPath = [UIBezierPath bezierPathWithRoundedRect:borderRect cornerRadius:cornerRadius - borderWidth/2];
        [borderPath stroke];
    }
    
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return image;
}

- (void)clickSegmentControl:(UISegmentedControl *)segmentControl {
    NSInteger index = segmentControl.selectedSegmentIndex;
    if (index == 0) {
        //密码登录
        self.loginInputView.loginTabType = BRLoginTabTypePassword;
    }else if (index == 1){
        //验证码登录
        self.loginInputView.loginTabType = BRLoginTabTypeAuthcode;
    }
}
#pragma mark - loginInputViewDelegate
- (void)LoginInputViewDelegateClickForgetPassword {
    BRLog(@"忘记密码");
    FindPasswordViewController *findPWDController = [[FindPasswordViewController alloc] init];
    [self.navigationController pushViewController:findPWDController animated:YES];
}

- (void)loginInputViewDelegateClickNoReachability {
    [self.view makeToast:@"联网失败，请检查手机网络状态" duration:kToastDuration position:CSToastPositionCenter];
}
#pragma mark - lazy load
- (UISegmentedControl *)segment {
    if (!_segment) {
        _segment = [[UISegmentedControl alloc] initWithItems:@[@"密码登录",@"验证码登录"]];
        _segment.selectedSegmentIndex = 0;
        _segment.layer.cornerRadius = segmentHeight / 2;
        _segment.layer.borderWidth = 1;
        _segment.layer.borderColor = [UIColor br_mainBlueColor].CGColor;
        _segment.layer.masksToBounds = YES;
        
        [_segment addTarget:self action:@selector(clickSegmentControl:) forControlEvents:UIControlEventValueChanged];
    }
    return _segment;
}
- (LoginInputView *)loginInputView {
    if (!_loginInputView) {
        _loginInputView = [LoginInputView loginInputView];
    }
    return _loginInputView;
}

- (UIButton *)loginButton {
    if (!_loginButton) {
        _loginButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_loginButton setBackgroundImage:[Utils createImageWithColor:[UIColor br_mainBlueColor]] forState:UIControlStateNormal];
        [_loginButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_loginButton setTitle:@"登  录" forState:UIControlStateNormal];
        [[_loginButton layer] setCornerRadius:kButtonHeight/2.0 ];
        [[_loginButton layer] setMasksToBounds:YES];
        
        [_loginButton addTarget:self action:@selector(clickLoginButton:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _loginButton;
}

- (UIButton *)registerButton {
    if (!_registerButton) {
        _registerButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_registerButton setTitle:@"快速注册" forState:UIControlStateNormal];
        [_registerButton setTitleColor:[UIColor br_mainBlueColor] forState:UIControlStateNormal];
        [[_registerButton titleLabel] setFont:kFontRegular(16)];
        [_registerButton addTarget:self action:@selector(clickRegisterButton:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _registerButton;
}

- (UIButton *)oneClickLoginButton {
    if (!_oneClickLoginButton) {
        _oneClickLoginButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_oneClickLoginButton setBackgroundImage:[Utils createImageWithColor:[UIColor whiteColor]] forState:UIControlStateNormal];
        [_oneClickLoginButton setBackgroundImage:[Utils createImageWithColor:[[UIColor br_mainBlueColor] colorWithAlphaComponent:0.1]] forState:UIControlStateHighlighted];
        [_oneClickLoginButton setTitleColor:[UIColor br_mainBlueColor] forState:UIControlStateNormal];
        [_oneClickLoginButton setTitle:@"手机号码一键登录" forState:UIControlStateNormal];
        [_oneClickLoginButton.titleLabel setFont:[UIFont systemFontOfSize:18]];
        
        // 设置圆角
        _oneClickLoginButton.layer.cornerRadius = kButtonHeight/2.0;
        _oneClickLoginButton.layer.masksToBounds = YES;
        
        [_oneClickLoginButton addTarget:self action:@selector(clickOneClickLoginButton:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _oneClickLoginButton;
}

#pragma mark - 点击N次按钮
- (UIButton *)clickNNumBtn:(CGRect)frame {
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeRoundedRect];
    btn.frame = frame;
    [btn addTarget:self action:@selector(nNumBtnClick) forControlEvents:UIControlEventTouchUpInside];
    
    return btn;
}

#pragma mark - 连续点5次后跳出调试界面
- (void)nNumBtnClick {
    self.clickNum ++;
    if (self.clickNum >= 5) {
        self.clickNum = 0;
        
        BRDebugInfoViewController *debugInfoViewController = [[BRDebugInfoViewController alloc] init];
        UINavigationController *navigation = [[UINavigationController alloc] initWithRootViewController:debugInfoViewController];
        [self presentViewController:navigation animated:YES completion:^{
            
        }];
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.clickNum = 0;
    });
}

#pragma mark-
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}

@end
