//
//  ChatViewController.m
//  BRZY
//
//  Created by  <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/8.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "ChatViewController.h"
#import "TitleBarView.h"
#import "PatientDocumentViewController.h"
#import "SessionViewController.h"
#import "PrescriptionViewController.h"
#import "UINavigationBar+Addition.h"
#import "IMSession.h"
#import "IMContact.h"
#import "IMClient.h"
#import "BRActionSheetView.h"
#import "BRAlertView.h"
#import "DoctorAuthentiationModel.h"
#import "QualificationCertificationViewController.h"
#import "ChatTitleView.h"
#import "EnterQuickOrderViewController.h"
#import "BRQuickPrescribeViewController.h"
#import "IMDataBaseManager.h"
#import "BRQuickPrescribePatientModel.h"
#import "BRMessagePrescribeViewController.h"
#import "QuickPrescribeSessionViewController.h"
#import "BRUpdateUserInfoModel.h"
#import "ScanQrcodeViewController.h"
#import "Config.h"
#import "BRAlertView.h"
#import <UIKit/UIKit.h>

@interface ChatViewController ()<UIScrollViewDelegate, PrescriptionVCDelegate, SessionVCDelegate, PatientDocumentDelegate>

@property (strong, nonatomic) TitleBarView *titleBar;
@property (strong, nonatomic) UIScrollView *scrollView;
@property (nonatomic, strong) PatientDocumentViewController *patientDocumentViewController;
@property (nonatomic, strong) SessionViewController *sessionViewController;
@property (nonatomic, strong) QuickPrescribeSessionViewController *quickPrescribeSessionViewController;
@property (nonatomic, strong) PrescriptionViewController *prescriptionViewController;
@property (nonatomic, strong) EnterQuickOrderViewController *enterQuickOrderViewController;
//当前数据
@property (nonatomic, strong) DoctorAuthentiationModel *authentiationModel;

//是否为快速开方患者判断
@property (nonatomic, assign) BOOL isQuickOrderUser;
//是否关注微信
@property (nonatomic, assign) BOOL isSubscribeWx;
//患者的来源
@property (nonatomic, assign) int userSource;

//快速开方传入信息
//@property (nonatomic, copy) NSString *nickname;
//@property (nonatomic, copy) NSString *ageString;
//@property (nonatomic, assign) IMContactGender gender;
//@property (nonatomic, copy) NSString *isPregnant;

@property (nonatomic, strong) BRQuickPrescribePatientModel *QuickInfoModel;


@end

@implementation ChatViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self showNavBackItem];
    
    
    //通知监听,微信用户修改了用户信息
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(updateUserInfo:) name:kIMNotificationUPdatePatientInfo object:nil];
    
    
    NSDictionary *dict = @{
        @"method_code" : @"000444",
        @"patientId" : self.session.sessionId
    };
    
    MBProgressHUD *HUD = [Utils createLoadingHUD];
    
    __weak __typeof(self)weakSelf = self;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
       HUD.hidden = YES;
        
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            
            BRQuickPrescribePatientModel *quickPatientModel = [BRQuickPrescribePatientModel mj_objectWithKeyValues:responseObject];
            
            weakSelf.QuickInfoModel = quickPatientModel;
            
//            NSLog(@"taker list = %lu", quickPatientModel.takerList.count);
//            NSLog(@"%@",quickPatientModel);
            
            weakSelf.isQuickOrderUser = (quickPatientModel.userSource == 1 || quickPatientModel.userSource == 2) ? YES : NO;
            weakSelf.isSubscribeWx = [quickPatientModel.isSubscribeWx isEqualToString:@"1"] ? YES : NO;
            weakSelf.userSource = quickPatientModel.userSource;
            
            [weakSelf configUI];
            [weakSelf addContentView];

            //sessionViewController 添加回调代理
            [[IMClient shareInstance].sessionManager addDelegate:weakSelf.sessionViewController];

            if (!weakSelf.isQuickOrderUser && self.isJumpToPrescripton) {
                self.prescriptionViewController.subPatientModel = self.patientModel;
            }
            
        }else {
            NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
            [weakSelf.view makeToast:errorMsg duration:2 position:CSToastPositionCenter title:nil image:nil style:nil completion:^(BOOL didTap) {
                [weakSelf.navigationController popViewControllerAnimated:YES];
            }];
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        
        HUD.hidden = YES;
        
        NSString *errorMsg = error.localizedDescription;
        [weakSelf.view makeToast:errorMsg duration:2 position:CSToastPositionCenter title:nil image:nil style:nil completion:^(BOOL didTap) {
            [weakSelf.navigationController popViewControllerAnimated:YES];
        }];
    }];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    self.navigationController.navigationBar.hidden = NO;
    
    UINavigationBar *navigationBar = self.navigationController.navigationBar;
    [navigationBar hideBottomHairline];
    
    //获取认证信息
//    [self requestAuthInfo];
    
    [self updateHeaderView];
}

#pragma mark - config UI
- (void)configUI {
    
    self.view.backgroundColor = [UIColor whiteColor];
    
    TitleBarView *titleBar = [[TitleBarView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, kCellDefaultHeight) andTitles:@[@"医案",@"交流",@"用药"]];
    titleBar.isShowBottomLine = YES;
    titleBar.backgroundColor = [UIColor whiteColor];
    titleBar.leftOrRightSpace = 60;

    [self.view addSubview:titleBar];
    
    titleBar.bottomLineView.backgroundColor = [UIColor colorWithHex:0xeaeaea];
    
    __weak __typeof(self)weakSelf = self;
    
    titleBar.titleButtonClicked = ^(NSUInteger index) {
        [self.scrollView setContentOffset:CGPointMake(SCREEN_WIDTH * index, 0)];
        if (index == 0) {
            [self.patientDocumentViewController refreshTable];
        }
        if (index == 0 || index == 2) {
            [self.sessionViewController endInputing];
        }
        if (index == 2) {
            
            if (!weakSelf.isQuickOrderUser) {
                //提示是否继续未完成药方
                [self.prescriptionViewController loadTemporayPrescription];
            }
  
        }
        if (index == 0 || index == 1) {
            if (!weakSelf.isQuickOrderUser) {
                [self.prescriptionViewController endInputing];
            }
           
        }
    };
    _titleBar = titleBar;
    
//    _titleBar.currentIndex = 1;
    
    if (!self.isQuickOrderUser && self.isJumpToPrescripton) {
//        self.prescriptionViewController.subPatientModel = self.patientModel;
        _titleBar.currentIndex = 2;
    }
    else {
        _titleBar.currentIndex = 1;
    }
    
    
    // Create mobile button
    UIImage *mobileImage = [[UIImage imageNamed:@"navi_mobile_btn"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    UIBarButtonItem *rightBarButtonItem = [[UIBarButtonItem alloc] initWithImage:mobileImage style:UIBarButtonItemStylePlain target:self action:@selector(clickMobileButton:)];
    
    // Set single right bar button item
    self.navigationItem.rightBarButtonItem = rightBarButtonItem;
}

- (void)addContentView {
    
    [self.view addSubview:self.scrollView];
    
    self.patientDocumentViewController = [[PatientDocumentViewController alloc] init];
    self.patientDocumentViewController.delegate = self;
    
    [self addChildViewController:self.patientDocumentViewController];
    [self.scrollView addSubview:self.patientDocumentViewController.view];
    self.patientDocumentViewController.view.frame = CGRectMake(0, 0, SCREEN_WIDTH, self.scrollView.frame.size.height);
    self.patientDocumentViewController.patientId = self.session.sessionId;
    self.patientDocumentViewController.patientName = self.session.name;
    [self.patientDocumentViewController configUI];
    
    self.sessionViewController = [[SessionViewController alloc] init];
    self.sessionViewController.session = self.session;
    self.sessionViewController.delegate = self;
    
    if (self.isQuickOrderUser && !self.isSubscribeWx) {
        
//        self.quickPrescribeSessionViewController = [[QuickPrescribeSessionViewController alloc] init];
//        [self addChildViewController:self.quickPrescribeSessionViewController];
//        
//        [self.scrollView addSubview:self.quickPrescribeSessionViewController.view];
//        self.quickPrescribeSessionViewController.view.frame = CGRectMake(SCREEN_WIDTH, 0, SCREEN_WIDTH, self.scrollView.frame.size.height);
        [self addChildViewController:self.sessionViewController];
        [self.scrollView addSubview:self.sessionViewController.view];
        
        //传入当前为快速开方患者
        self.sessionViewController.isQuickPrescriptionUser = YES;
        
        self.sessionViewController.view.frame = CGRectMake(SCREEN_WIDTH, 0, SCREEN_WIDTH, self.scrollView.frame.size.height);
        
        self.enterQuickOrderViewController = [[EnterQuickOrderViewController alloc] init];
        
        [self addChildViewController:self.enterQuickOrderViewController];
        
        [self.scrollView addSubview:self.enterQuickOrderViewController.view];
        
        self.enterQuickOrderViewController.view.frame = CGRectMake(SCREEN_WIDTH * 2, 0, SCREEN_WIDTH, self.scrollView.frame.size.height);
        
        __weak __typeof(self)weakSelf = self;
        self.enterQuickOrderViewController.clickEnterQuickOrderBlock = ^{
            
            //判断进入短信开方还是微信开方
            if (weakSelf.userSource == 1) {
                //微信开方
                
                BRQuickPrescribeViewController *quickPrescribeVC = [[BRQuickPrescribeViewController alloc] init];
                if (weakSelf.QuickInfoModel.takerList.count > 0) {
                    quickPrescribeVC.takerInfoModel = weakSelf.QuickInfoModel.takerList[0];
                }
                
                [weakSelf.navigationController pushViewController:quickPrescribeVC animated:YES];
            }else if (weakSelf.userSource == 2){
                //短信开方
                BRMessagePrescribeViewController *messagePrescribeVC = [[BRMessagePrescribeViewController alloc] init];
                if (weakSelf.QuickInfoModel.takerList.count > 0) {
                    messagePrescribeVC.takerInfoModel = weakSelf.QuickInfoModel.takerList[0];
                }
                [weakSelf.navigationController pushViewController:messagePrescribeVC animated:YES];
            }
        };
    }
    else {
        
        //正常session
        
        [self addChildViewController:self.sessionViewController];
        [self.scrollView addSubview:self.sessionViewController.view];
        self.sessionViewController.view.frame = CGRectMake(SCREEN_WIDTH, 0, SCREEN_WIDTH, self.scrollView.frame.size.height);
        
        self.prescriptionViewController = [[PrescriptionViewController alloc] init];
        self.prescriptionViewController.patientId = self.session.sessionId;
        self.prescriptionViewController.delegate = self;
        
        [self addChildViewController:self.prescriptionViewController];
        
        [self.scrollView addSubview:self.prescriptionViewController.view];
        self.prescriptionViewController.view.frame = CGRectMake(SCREEN_WIDTH * 2, 0, SCREEN_WIDTH, self.scrollView.frame.size.height);
        
        __weak __typeof(self)weakSelf = self;
        self.prescriptionViewController.updatePatientNameBlock = ^{
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf updateHeaderView];
            });
        };
    }
    
    
    
    
    
    [self.scrollView setContentSize:CGSizeMake(SCREEN_WIDTH * 3, self.scrollView.frame.size.height)];
    
    if (@available(iOS 11.0, *)) {
        self.titleBar.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        // Fallback on earlier versions
    }
}

- (UIScrollView *)scrollView {
    if (!_scrollView) {
        CGFloat topHeight = kTopBarHeight;
        _scrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, topHeight, SCREEN_WIDTH, UIScreenHeight - topHeight - kCellDefaultHeight - kStatusBarHeight)];
        _scrollView.pagingEnabled = YES;
        _scrollView.showsHorizontalScrollIndicator = NO;
        _scrollView.bounces = NO;
        _scrollView.scrollEnabled = YES;
        _scrollView.delegate = self;
    }
    return _scrollView;
}

- (void)updateHeaderView {
    
    __weak ChatViewController *weakSelf = self;
    
    IMSession *updatedSession = [[IMClient shareInstance].sessionManager getOneSessionWithSessionId:_session.sessionId];
    
    [[IMClient shareInstance].contactManager getIMContactWithPatientId:_session.sessionId completion:^(IMContact *aContact) {
        
//        NSString *name = @"";
//        if(updatedSession.name){
//            name = updatedSession.name;
//        }else{
//            name = aContact.nickname;
//        }
        
//        if ([aContact.remark isEqualToString:@""]) {
//            name = aContact.nickname;
//        }else{
//            name = [NSString stringWithFormat:@"%@（%@）",aContact.remark,aContact.nickname];
//        }
        
       
        ChatTitleView *titleView = [ChatTitleView getChatTitleViewWithName:aContact.nickname gender:aContact.gender age:[NSString stringWithFormat:@"%@",aContact.age]];
        weakSelf.navigationItem.titleView = titleView;
        
//        if(weakSelf.isQuickOrderUser){
//            weakSelf.nickname = updatedSession.name;
//            weakSelf.ageString = aContact.age;
//            weakSelf.gender = aContact.gender;
            
//            weakSelf.enterQuickOrderViewController
//        }
    }];
    
}
#pragma mark -  通知处理
//监听用户是否修改了用户信息
- (void)updateUserInfo:(NSNotification *)notify {
    
    NSDictionary *dict = [NSDictionary dictionaryWithDictionary:notify.userInfo];
    
    BRUpdateUserInfoModel *userInfoModel = [BRUpdateUserInfoModel mj_objectWithKeyValues:dict];
    
    //判断是否为当前用户
    if (![self.session.sessionId isEqualToString:userInfoModel.userId]) {
        return;
    }
    
    NSLog(@"chatview 接收到用户更新信息== %@",dict);
    NSLog(@"name == %@  userid = %@",userInfoModel.name, userInfoModel.userId);
    
    
    ChatTitleView *titleView = [ChatTitleView getChatTitleViewWithName:userInfoModel.name gender:userInfoModel.sex age:[NSString stringWithFormat:@"%@",userInfoModel.age]];
    self.navigationItem.titleView = titleView;
}

#pragma mark - 页面跳转
- (void)jumpToUseDrugPage:(NSNotification *)notify {
    
    NSDictionary *userInfo = notify.userInfo;
    
    NSLog(@"notify userinfo = %@",notify.userInfo);
    
    BRPatientModel *model = [[BRPatientModel alloc] init];
    model.isSelf = [userInfo objectForKey:@"isMark"];
    model.age = [userInfo objectForKey:@"age"];
    model.patientId = [userInfo objectForKey:@"id"];
    model.name = [userInfo objectForKey:@"name"];
    model.sex = [userInfo objectForKey:@"sex"];
    
    [self.prescriptionViewController changeSubPatientModel:model];
    
    _titleBar.currentIndex = 2;
}
#pragma mark - 聊天界面到 去用药 代理回调
//session页面回调
- (void)sessionVCDidToUseDrugInfo:(NSDictionary *)info {
    
    BRPatientModel *model = [[BRPatientModel alloc] init];
    model.isSelf = [info objectForKey:@"isMark"];
    model.age = [info objectForKey:@"age"];
    model.patientId = [info objectForKey:@"id"];
    model.name = [info objectForKey:@"name"];
    model.sex = [info objectForKey:@"sex"];
    
    [self.prescriptionViewController changeSubPatientModel:model];
    
    _titleBar.currentIndex = 2;
}
#pragma mark - 医案列表到去用药
- (void)patientDocumentDidToUseDrugInfo:(NSDictionary *)dict {
    [self sessionVCDidToUseDrugInfo:dict];
}
#pragma mark - 获取认证信息
- (void)requestAuthInfo {
    NSDictionary *dict = @{
                           @"method_code" : @"000045"
                           };
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:nil success:^(NSURLSessionDataTask *task, id responseObject) {
        NSString *code = [responseObject objectForKey:@"code"];
        if ([code isEqualToString:@"0000"]) {
            _authentiationModel = [DoctorAuthentiationModel mj_objectWithKeyValues:responseObject];
        }
        else{
            NSString *errorMsg = [responseObject objectForKey:@"errorMsg"];
            NSString *str = [NSString stringWithFormat:@"%@",errorMsg];
            [self.view makeToast:str duration:kToastDuration position:CSToastPositionCenter];
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        [self.view makeToast:@"网络请求失败" duration:kToastDuration position:CSToastPositionCenter];
    }];
}
#pragma mark - 显示认证弹窗信息
- (void)showAuthentiationAlert {
    if (!_authentiationModel) {
        [self requestAuthInfo];
    }
    
    //如果之前认证成功过 则不用继续判断
    if ([_authentiationModel.isAuthentication isEqualToString:@"1"]) {
        return;
    }
    
    //未认证
    if (_authentiationModel.currentAuthenticationState == DoctorAuthentiationStateNotAuth) {
        
        BRAlertView *alertView = [[BRAlertView alloc] init];
        [alertView.okButton setTitle:@"前往认证" forState:UIControlStateNormal];
        [alertView.cancelButton setTitle:@"暂不认证" forState:UIControlStateNormal];
        
        [alertView showAlertViewWithCancelButton:@"认证通过后可以使用该功能" completion:^(BOOL isOk) {
            //跳转到认证界面
            if (isOk) {
                QualificationCertificationViewController *qualificationVC = [[QualificationCertificationViewController alloc] init];
                [self.navigationController pushViewController:qualificationVC animated:YES];
            }
            [alertView close];
        }];
    }
    //认证中
    else if (_authentiationModel.currentAuthenticationState == DoctorAuthentiationStateReview) {
        BRAlertView *alertView = [[BRAlertView alloc] init];
        [alertView.okButton setTitle:@"确认" forState:UIControlStateNormal];
        
        [alertView showAlertView:@"认证审核中..." completion:^{
            [alertView close];
        }];
    }
    //认证失败
    else if (_authentiationModel.currentAuthenticationState == DoctorAuthentiationStateFailed) {
        BRAlertView *alertView = [[BRAlertView alloc] init];
        [alertView.okButton setTitle:@"前往认证" forState:UIControlStateNormal];
        [alertView.cancelButton setTitle:@"暂不认证" forState:UIControlStateNormal];
        
        [alertView showAlertViewWithCancelButton:@"认证失败，请重新认证" completion:^(BOOL isOk) {
            //跳转到认证界面
            if (isOk) {
                QualificationCertificationViewController *qualificationVC = [[QualificationCertificationViewController alloc] init];
                [self.navigationController pushViewController:qualificationVC animated:YES];
            }
            [alertView close];
        }];
        
    }
}

#pragma mark - ScrollViewDelegate
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    NSInteger currentIndex = scrollView.contentOffset.x / SCREEN_WIDTH;
    if (_titleBar.currentIndex != currentIndex) {
        [_titleBar setCurrentIndex:currentIndex];
    }
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    NSInteger currentIndex = scrollView.contentOffset.x / SCREEN_WIDTH;
    if (!currentIndex) {
//        [self.patientDocumentViewController viewShow];
    }
    
    //聊天界面切换到正常状态
//    [self.sessionViewController changeToNormalState];
}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    [self.sessionViewController inputViewDidEndEditingEvent];
}
#pragma mark - Click Event
- (void)clickMobileButton:(UIBarButtonItem *)sender {
    
    sender.enabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        sender.enabled = YES;
    });
    
    __weak ChatViewController *weakSelf = self;
    
    [[IMClient shareInstance].contactManager getIMContactWithPatientId:_session.sessionId completion:^(IMContact *aContact) {
        if (aContact.mobile && ![aContact.mobile isEqualToString:@""]) {
            NSMutableString * str=[[NSMutableString alloc] initWithFormat:@"telprompt://%@",aContact.mobile];
//            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:str]];
            NSURL *url = [NSURL URLWithString:str];

            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {
                    if (success) {
                        NSLog(@"成功打开URL");
                    } else {
                        NSLog(@"无法打开URL");
                    }
                }];
            } else {
                NSLog(@"无法处理此URL");
            }
            
        }else{
            [weakSelf.view makeToast:@"患者未完善个人信息" duration:2 position:CSToastPositionCenter];
        }
    }];
}
- (void)clickBackButton:(UIButton *)sender {
    
    __weak ChatViewController *weakSelf = self;
    
    if ([_prescriptionViewController prescriptionIfChanged]) {
        
        /*
        BRAlertView *alertView = [[BRAlertView alloc] init];
        __weak BRAlertView *weakAlertView = alertView;
        [alertView.cancelButton setTitle:@"临时保存" forState:UIControlStateNormal];
        [alertView.okButton setTitle:@"退出" forState:UIControlStateNormal];
        [alertView showAlertViewWithCancelButton:@"用药方案尚未完成，是否退出？" completion:^(BOOL isOk) {
            
            if (!isOk) {
                //临时保存处方
                [weakSelf.prescriptionViewController saveTemporaryPrescription];
            }
            else{
                //删除处方
                NSString *primaryKey = [NSString stringWithFormat:@"%@%@",[UserManager shareInstance].getUserId,self.session.sessionId];
                [[IMDataBaseManager shareInstance]deleteTemporaryPrescriptionWithPrimaryKey:primaryKey];
            }
            
            [weakSelf popViewController];
            
            [alertView close];
            
        }];
         */
        
        [self.prescriptionViewController saveTemporaryPrescription];
        
    }
    else {
        
        //删除处方
        NSString *primaryKey = [NSString stringWithFormat:@"%@%@",[UserManager shareInstance].getUserId,self.session.sessionId];
        [[IMDataBaseManager shareInstance]deleteTemporaryPrescriptionWithPrimaryKey:primaryKey];
        
    }
    
    [self popViewController];

}

- (void)popViewController {
    
    [[IMClient shareInstance].sessionManager removeDelegate:self.sessionViewController];
//    [[NSNotificationCenter defaultCenter] removeObserver:self name:kNotificationGoToUseDrugPage object:nil];
    [self.navigationController popViewControllerAnimated:YES];
    
    [[NSNotificationCenter defaultCenter] removeObserver:_prescriptionViewController name:kIMNotificationUPdatePatientInfo object:nil];
}

#pragma mark - Scan Button Action
- (void)clickScanButton:(UIBarButtonItem *)sender {
    sender.enabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        sender.enabled = YES;
    });
    
    // Check if user is logged in and has valid session token
    NSString *sessionToken = [Config shareInstance].sessionToken;
    NSString *userId = [UserManager shareInstance].getUserId;
    
    if (!sessionToken || [sessionToken isEqualToString:@""] || !userId || [userId isEqualToString:@""]) {
        [self.view makeToast:@"请先登录" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    // Create and configure scan controller
    ScanQrcodeViewController *scanVC = [[ScanQrcodeViewController alloc] init];
    
    // Override the scan result handling for web login
    [self setupWebLoginScanHandler:scanVC];
    
    [self.navigationController pushViewController:scanVC animated:YES];
}

- (void)setupWebLoginScanHandler:(ScanQrcodeViewController *)scanVC {
    // We'll use method swizzling or notification to handle the scan result
    // For now, let's use notification pattern
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(handleQRCodeScanResult:) 
                                                 name:@"QRCodeScanResultNotification" 
                                               object:nil];
}

- (void)handleQRCodeScanResult:(NSNotification *)notification {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"QRCodeScanResultNotification" object:nil];
    
    NSString *scanResult = notification.userInfo[@"result"];
    
    if ([self isWebLoginQRCode:scanResult]) {
        [self processWebLoginQRCode:scanResult];
    } else {
        [self.view makeToast:@"这不是医案导出二维码" duration:2 position:CSToastPositionCenter];
    }
}

- (BOOL)isWebLoginQRCode:(NSString *)qrContent {
    // Check if QR code is for web login: brzy-web://login?session=xxx&server=xxx
    return [qrContent hasPrefix:@"brzy-web://login?"];
}

- (void)processWebLoginQRCode:(NSString *)qrContent {
    // Parse QR code URL to extract session and server info
    NSURL *url = [NSURL URLWithString:qrContent];
    NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
    
    NSString *sessionId = nil;
    NSString *serverUrl = nil;
    
    for (NSURLQueryItem *item in components.queryItems) {
        if ([item.name isEqualToString:@"session"]) {
            sessionId = item.value;
        } else if ([item.name isEqualToString:@"server"]) {
            serverUrl = item.value;
        }
    }
    
    if (!sessionId || !serverUrl) {
        [self.view makeToast:@"二维码格式错误" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    // Show confirmation dialog
    [self showWebLoginConfirmationWithSessionId:sessionId serverUrl:serverUrl];
}

- (void)showWebLoginConfirmationWithSessionId:(NSString *)sessionId serverUrl:(NSString *)serverUrl {
    BRAlertView *alertView = [[BRAlertView alloc] init];
    [alertView.okButton setTitle:@"确认授权" forState:UIControlStateNormal];
    [alertView.cancelButton setTitle:@"取消" forState:UIControlStateNormal];
    
    NSString *patientName = self.session.name ?: @"当前患者";
    NSString *message = [NSString stringWithFormat:@"确认授权医案导出网页端访问 %@ 的医案数据吗？", patientName];
    
    __weak typeof(self) weakSelf = self;
    [alertView showAlertViewWithCancelButton:message completion:^(BOOL isOk) {
        if (isOk) {
            [weakSelf authorizeWebLoginWithSessionId:sessionId serverUrl:serverUrl];
        }
        [alertView close];
    }];
}

- (void)authorizeWebLoginWithSessionId:(NSString *)sessionId serverUrl:(NSString *)serverUrl {
    // Show loading
    MBProgressHUD *HUD = [Utils createLoadingHUD];
    
    // Prepare device info with device identifiers
    NSString *appVersion = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    NSString *osVersion = [[UIDevice currentDevice] systemVersion];
    NSString *deviceModel = [[UIDevice currentDevice] model];
    
    // Get device identifier
    NSString *deviceId = [UIDevice currentDevice].identifierForVendor.UUIDString;
    NSString *shortDeviceId = [deviceId substringToIndex:MIN(deviceId.length, 16)]; // 截取前16位
    
    // Prepare current patient info
    NSString *currentPatientId = self.session.sessionId;
    NSString *currentPatientName = self.session.name;
    
    // Prepare request payload with device identifiers
    NSDictionary *deviceInfo = @{
        @"app_version": appVersion ?: @"",
        @"os_version": osVersion ?: @"",
        @"device_model": deviceModel ?: @"",
        @"platform": @"iOS",
        @"device_id": shortDeviceId ?: @"",
        @"imei": shortDeviceId ?: @"",
        @"mac": shortDeviceId ?: @""
    };
    
    // Generate a proper auth code for this authorization
    NSTimeInterval timestamp = [[NSDate date] timeIntervalSince1970] * 1000;
    NSInteger random = arc4random_uniform(9999);
    NSString *userId = [UserManager shareInstance].getUserId;
    NSString *authCode = [NSString stringWithFormat:@"%.0f_%04ld_%@_web", timestamp, (long)random, userId];
    
    NSDictionary *requestData = @{
        @"auth_code": authCode,
        @"session_token": [Config shareInstance].sessionToken,
        @"web_session_id": sessionId,
        @"device_info": deviceInfo,
        @"current_patient_id": currentPatientId ?: @"",
        @"current_patient_name": currentPatientName ?: @""
    };
    
    // Send authorization request
    NSString *authUrl = [NSString stringWithFormat:@"%@/api/auth/scan", serverUrl];
    
    __weak typeof(self) weakSelf = self;
    [self sendAuthRequest:authUrl data:requestData completion:^(BOOL success, NSString *message) {
        dispatch_async(dispatch_get_main_queue(), ^{
            HUD.hidden = YES;
            
            if (success) {
                [weakSelf.view makeToast:@"授权成功" duration:2 position:CSToastPositionCenter];
            } else {
                NSString *errorMsg = message ?: @"授权失败";
                [weakSelf.view makeToast:errorMsg duration:2 position:CSToastPositionCenter];
            }
        });
    }];
}

- (void)sendAuthRequest:(NSString *)url data:(NSDictionary *)data completion:(void(^)(BOOL success, NSString *message))completion {
    // Create URL request
    NSURL *requestURL = [NSURL URLWithString:url];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:requestURL];
    request.HTTPMethod = @"POST";
    [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    
    // Convert data to JSON
    NSError *jsonError;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:data options:0 error:&jsonError];
    
    if (jsonError) {
        if (completion) completion(NO, @"请求数据格式错误");
        return;
    }
    
    request.HTTPBody = jsonData;
    
    // Send request
    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData *responseData, NSURLResponse *response, NSError *error) {
        if (error) {
            if (completion) completion(NO, error.localizedDescription);
            return;
        }
        
        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
        if (httpResponse.statusCode != 200) {
            NSString *errorMsg = [NSString stringWithFormat:@"服务器错误: %ld", (long)httpResponse.statusCode];
            if (completion) completion(NO, errorMsg);
            return;
        }
        
        // Parse response
        NSError *parseError;
        NSDictionary *responseDict = [NSJSONSerialization JSONObjectWithData:responseData options:0 error:&parseError];
        
        if (parseError) {
            if (completion) completion(NO, @"响应数据解析失败");
            return;
        }
        
        NSNumber *code = responseDict[@"code"];
        NSString *message = responseDict[@"message"];
        
        BOOL success = code && [code intValue] == 200;
        if (completion) completion(success, message);
    }];
    
    [task resume];
}

#pragma mark -
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}

#pragma mark- PrescriptionVCDelegate
- (void)scrollToChatViewController {
    
    _titleBar.currentIndex = 1;
    
}

@end
