//
//  BRWechatPocketMoneyViewController.m
//  BRZY
//
//  Created by 许江涛 on 2022/1/22.
//  Copyright © 2022 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRWechatPocketMoneyViewController.h"
#import "WechatWithdrawChooseCell.h"
#import "WechatWithDrawSumCell.h"
#import "WechatWithdrawBottomView.h"
#import "BRGuiZeViewController.h"
#import "WechatWithdrawInputNumCell.h"
#import "WXApi.h"
#import "BRWechatBindResultModel.h"
#import "BRWithdrawInfoModel.h"
#import "BRAlertView.h"
#import "ShowMyPurseSMSCode.h"
#import "Reachability.h"
#import "BRActionSheetView.h"
#import "BRWithdrawPasswordView.h"
#import "WithdrawPasswordViewController.h"
#import "NSString+YYAdd.h"
//#import <ShareSDK/ShareSDK.h>
//#import <ShareSDKConnector/ShareSDKConnector.h>

@interface BRWechatPocketMoneyViewController ()<UITableViewDataSource, UITableViewDelegate, WXApiDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIButton *withdrawButton;

@property (nonatomic, strong) BRWithdrawInfoModel *withdrawInfoModel;
//输入的提现金额
@property (nonatomic, copy)   NSString *withdrawMoney;
//验证码
@property (nonatomic, strong) NSString *smsCode;
@property (nonatomic, strong) ShowMyPurseSMSCode *smsCodeView;

@property (nonatomic, assign) NSInteger timeNum;
@property (nonatomic, strong) NSTimer *timer;

@property (nonatomic, strong)Reachability *reachability;

@property (nonatomic, assign) BOOL isBind;

// 密码验证相关属性
@property (nonatomic, strong)BRWithdrawPasswordView *passwordView;
@property (nonatomic, strong)NSString *withdrawPassword;

// 验证方式标识 0:短信验证 1:密码验证
@property (nonatomic, assign)NSInteger verificationType;

@end

@implementation BRWechatPocketMoneyViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.navigationItem.title = @"提现到微信钱包";
    [self showNavBackItem];
    _withdrawMoney = @"";
    _isBind = NO;
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(wechatAuthSuccess:) name:kNotificationWechatAuth object:nil];
    
    [self configUI];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    //更新用户信息
    [self requestUpdateUserInfo];
    //更新可提现金额
    [self requestCanCashData];
}

- (void)configUI {
    
    [self.view addSubview:self.tableView];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    //提现按钮
    [self.view addSubview:self.withdrawButton];
    
    [self.withdrawButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(@30);
        make.right.mas_equalTo(@-30);
        make.height.mas_equalTo(@40);
        make.centerY.equalTo(self.view.mas_centerY).with.offset(20);
    }];
}

#pragma mark - 获取可提现金额
- (void)requestCanCashData {
   
    NSDictionary *dict = @{
        @"method_code" : @"000069"
    };
    
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"绑定中..."];
    __weak typeof(progressHUD) hud = progressHUD;
    __weak __typeof(self)weakSelf = self;
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        hud.hidden = YES;
        weakSelf.withdrawInfoModel = [BRWithdrawInfoModel mj_objectWithKeyValues:responseObject];
        [weakSelf.tableView reloadData];
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        hud.hidden = YES;
        [weakSelf.view makeToast:error.localizedDescription duration:2 position:CSToastPositionCenter];
    }];
}

//更新用户信息
- (void)requestUpdateUserInfo {
    NSDictionary *dict = @{@"method_code":@"000017",
                           @"tel":[UserManager shareInstance].getTelephone
                           };
    __weak __typeof(self)weakSelf = self;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        UserInfo *userInfo = [UserInfo mj_objectWithKeyValues:responseObject];
        
        if (userInfo.wxOpenId.length > 0) {
            weakSelf.isBind = YES;
        }
        else {
            weakSelf.isBind = NO;
        }
        
        [weakSelf.tableView reloadData];
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
    }];
}

#pragma mark - click event
- (void)clickWithdrawButtonEvent:(UIButton *)sender {
    
    [self.view endEditing:YES];
    
    if (self.isBind == NO) {
        [self.view makeToast:@"请先绑定微信！" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    if ([self.withdrawMoney isEqualToString:@""]) {
        [self.view makeToast:@"请输入提现金额" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    //如果提现金额大于实际可提现金额
    CGFloat canCash = [self.withdrawInfoModel.canCashPrice floatValue];
    CGFloat wantCash = [self.withdrawMoney floatValue];
    
    if (wantCash > canCash) {
        NSString *text = [NSString stringWithFormat:@"您今日可提现的金额不得大于%@元",self.withdrawInfoModel.canCashPrice];
        [self.view makeToast:text duration:2 position:CSToastPositionCenter];
        return;
    }
    
    if (wantCash < 999) {
        
        BRAlertView *alertView = [[BRAlertView alloc] init];
        alertView.isHideWhenTapBackground = NO;
        [alertView.cancelButton setTitle:@"取消" forState:UIControlStateNormal];
        [alertView.okButton setTitle:@"提现" forState:UIControlStateNormal];
        
        NSString *message = [NSString stringWithFormat:@"您的提现金额未满999，需要您支付1元手续费，是否提现？"];
        __weak __typeof(self)weakSelf = self;
        [alertView showAlertViewWithCancelButton:message completion:^(BOOL isOk) {
            if (isOk) {
                //显示验证方式选择
                [weakSelf showVerificationTypeActionSheet];
            }
        }];
        
        return;
    }
    //显示验证方式选择
    [self showVerificationTypeActionSheet];
}


#pragma mark - 验证码
- (void)smsAuthAction {
    __weak typeof(self)mySelf = self;
    
    self.smsCode = @"";
    
    self.smsCodeView = [[ShowMyPurseSMSCode alloc] initWithBlock:^(NSString *smsCode) {
        
        mySelf.smsCode = [smsCode copy];
        if (self.smsCodeView.selectViewList.smsBtn.userInteractionEnabled) {
            
            [mySelf timeOver];
            
        } else {
            
            self.smsCodeView.selectViewList.smsBtn.userInteractionEnabled = NO;
            
        }
        [mySelf smsYanzheng];
        
    } :^{
        
        [mySelf setSmsCode];
        
    }];
    [self.smsCodeView.selectViewList.yes setTitle:@"确认" forState:UIControlStateNormal];
    self.smsCodeView.selectViewList.titleLabel.text = @"短信验证";
}


- (void)refreshSmsCodeBtn {
    
    self.timeNum --;
    if (self.timeNum >= 0) {
        self.smsCodeView.selectViewList.smsBtn.titleLabel.text = [NSString stringWithFormat:@"重新发送(%ld)s",self.timeNum];
        [self.smsCodeView.selectViewList.smsBtn setTitle:[NSString stringWithFormat:@"重新发送(%ld)s",self.timeNum] forState:UIControlStateNormal];
        [self.smsCodeView.selectViewList.smsBtn setTitleColor:[UIColor br_textLightGrayColor] forState:UIControlStateNormal];
        [self.smsCodeView.selectViewList.smsBtn setBackgroundImage:[UIImage imageWithColor:[UIColor whiteColor]] forState:UIControlStateNormal];
    } else {
        
        [self timeOver];
    }
}


- (void)timeOver {
    self.timeNum = 60;
    self.smsCodeView.selectViewList.smsBtn.userInteractionEnabled = YES;
    [self.smsCodeView.selectViewList.smsBtn setTitle:@"获取验证码" forState:UIControlStateNormal];
    [self.smsCodeView.selectViewList.smsBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.smsCodeView.selectViewList.smsBtn setBackgroundImage:[UIImage imageWithColor:[UIColor br_mainBlueColor]] forState:UIControlStateNormal];
    [self.timer invalidate];
    self.timer = nil;
}

- (void)smsYanzheng {
    
    UIWindow *window = [UIApplication sharedApplication].keyWindow;
    if (!self.smsCode.length) {
        
        [window makeToast:@"请输入短信验证码" duration:2 position:CSToastPositionCenter];
        
        return;
    }
    
    // 去掉000086验证步骤，直接进入提现流程
    [self.smsCodeView removeView];
    [self withdrawAction];
}

- (void)setSmsCode {
    
    self.smsCodeView.selectViewList.smsBtn.userInteractionEnabled = NO;
    self.timer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(refreshSmsCodeBtn) userInfo:nil repeats:YES];
    self.timeNum = 60;
    
    UIWindow *window = [UIApplication sharedApplication].keyWindow;
    
    __weak typeof(self)mySelf = self;
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithObjectsAndKeys:@"000031",@"method_code", nil];
    [dic setObject:@"1" forKey:@"type"];
    [dic setObject:@"1" forKey:@"optType"];
    
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;
            
            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                
                [window makeToast:@"验证码已发送" duration:2 position:CSToastPositionCenter];
                
            } else {
                
                [window makeToast:[dataDic objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
                [mySelf timeOver];
            }
            
        } else {
            [window makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
            [mySelf timeOver];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);
        
        [mySelf timeOver];
        
        if (!mySelf.reachability) {
            mySelf.reachability = [Reachability reachabilityWithHostName:@"www.baidu.com"];
        }
        if ([mySelf.reachability currentReachabilityStatus] == NotReachable) {
            //没有网络
            
            [window makeToast:@"无网络，请检查您的网络连接" duration:2 position:CSToastPositionCenter];
            
        } else {
            if ([error.localizedDescription isEqualToString:@"The request timed out."]) {
                //请求超时
                
                [window makeToast:@"请求超时，请稍后重试" duration:2 position:CSToastPositionCenter];
                
            } else {
                //请求失败
                
                [window makeToast:@"加载失败" duration:2 position:CSToastPositionCenter];
            }
        }
        
    }];
}


#pragma mark - 提现
- (void)withdrawAction {
  
    NSLog(@"开始提现====");
    
    // 微信提现接口  000436 参数 userId，amount，新增password，smsCode，apiVer
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:0];
    [dict setValue:@"000436" forKey:@"method_code"];
    [dict setValue:[[UserManager shareInstance] getUserId] forKey:@"userId"];
    [dict setValue:self.withdrawMoney forKey:@"amount"];
    [dict setValue:@"2" forKey:@"apiVer"];
    
    // 根据验证方式传递对应参数
    if (self.verificationType == 1) {
        // 密码验证 - 传递MD5大写的密码
        NSString *md5Password = [[self.withdrawPassword md5String] uppercaseString];
        [dict setValue:md5Password forKey:@"password"];
        [dict setValue:@"" forKey:@"smsCode"];  // 密码验证时smsCode传空
    } else {
        // 短信验证 - 传递验证码
        [dict setValue:@"" forKey:@"password"];  // 短信验证时password传空
        [dict setValue:self.smsCode forKey:@"smsCode"];
    }
    
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"提现中..."];
    __weak typeof(progressHUD) hud = progressHUD;
    __weak __typeof(self)weakSelf = self;
    
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        hud.hidden = YES;
        NSDictionary *dict = (NSDictionary *)responseObject;
        NSString *code = [dict objectForKey:@"code"];
        NSString *errorMsg = @"提现失败";
        if ([dict objectForKey:@"errorMsg"]) {
            errorMsg = [dict objectForKey:@"errorMsg"];
        }
        
        if ([code isEqualToString:@"0000"]) {
            //刷新当前金额
            [weakSelf.view makeToast:@"提现成功" duration:2 position:CSToastPositionCenter title:nil image:nil style:nil completion:^(BOOL didTap) {
                [weakSelf.navigationController popViewControllerAnimated:YES];
            }];
            
            [weakSelf requestCanCashData];
        }
        //提现失败
        else {
            NSString *msg = errorMsg;
            
//            if ([code isEqualToString:@"0016"]) {
//                msg = @"绑定的微信姓名与认证的不同";
//            }
//            else if ([code isEqualToString:@"0017"]){
//                msg = @"微信余额不足";
//            }
            
            [self.view makeToast:msg duration:2 position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        hud.hidden = YES;
        [weakSelf.view makeToast:error.localizedDescription duration:2 position:CSToastPositionCenter];
    }];
}

- (void)requestWechatInfoAction {
    
    //构造SendAuthReq结构体
    SendAuthReq* req = [[SendAuthReq alloc] init];
    req.scope = @"snsapi_userinfo";
    req.state = @"123";
    //第三方向微信终端发送一个SendAuthReq消息结构
    [WXApi sendReq:req completion:^(BOOL success) {
        if (success) {
            NSLog(@"wxapi send req success");
        }
        else {
            NSLog(@"wxapi send req failed");
        }
    }];
}

- (void)wechatAuthSuccess:(NSNotification *)notification {
    
    NSDictionary *userInfo = notification.userInfo;
    
    NSLog(@"user info == %@",userInfo);
    
    int ErrCode = [[userInfo objectForKey:@"ErrCode"] intValue];
    NSString *code = [userInfo objectForKey:@"code"];
    
    if (ErrCode != 0) {
        
        NSString *text = @"登录失败";
        
        if (ErrCode == -2) {
            text = @"已取消";
        }
        else if (ErrCode == -4) {
            text = @"已拒绝授权";
        }
        
        [self.view makeToast:text duration:2 position:CSToastPositionCenter];
        
        return;
    }
    
    //获取code
    NSMutableDictionary *dict = [NSMutableDictionary dictionaryWithCapacity:0];
    [dict setValue:@"000435" forKey:@"method_code"];
    [dict setValue:code forKey:@"code"];
    [dict setValue:[[UserManager shareInstance] getUserId] forKey:@"userId"];
    
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"绑定中..."];
    __weak typeof(progressHUD) hud = progressHUD;
    __weak __typeof(self)weakSelf = self;
    [HTTPRequest POST:kServerDomain parameters:dict progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        hud.hidden = YES;
        BRWechatBindResultModel *model = [BRWechatBindResultModel mj_objectWithKeyValues:responseObject];
        //绑定成功
        if ([model.code isEqualToString:@"0000"]) {
            //更新用户信息
            [[UserManager shareInstance] setUserInfoWithValue:model.code key:kUserDefaultWXOpenid];
            
            [weakSelf.view makeToast:@"绑定成功" duration:2 position:CSToastPositionCenter];
            weakSelf.isBind = YES;
            
            [weakSelf.tableView reloadData];
        }
        else {
            [weakSelf.view makeToast:model.errorMsg duration:2 position:CSToastPositionCenter];
        }
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        hud.hidden = YES;
        
        [weakSelf.view makeToast:error.localizedDescription duration:2 position:CSToastPositionCenter];
    }];
}

#pragma mark - UITableViewDataSource, UITableViewDelegate
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 2;
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == 1) {
        return 2;
    }
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    
    if (indexPath.section == 0) {
        WechatWithdrawChooseCell *chooseCell = [tableView dequeueReusableCellWithIdentifier:@"wechatWithDrawChooseCellId" forIndexPath:indexPath];
        chooseCell.selectionStyle = UITableViewCellSelectionStyleNone;
        
//        NSString *wxOpenId = [UserManager shareInstance].getWXOpenId;
        chooseCell.alreadyBind = self.isBind;
//        if ([wxOpenId isEqualToString:@""]) {
//            chooseCell.alreadyBind = NO;
//        }
//        else {
//            chooseCell.alreadyBind = YES;
//        }
        
        __weak __typeof(self)weakSelf = self;
        //绑定微信
        chooseCell.clickBindButtonBlock = ^{
            [weakSelf requestWechatInfoAction];
        };
        //更换微信账号
        chooseCell.clickChangeBindBlock = ^{
            [weakSelf requestWechatInfoAction];
        };
        
        return chooseCell;
    }
    else if (indexPath.section == 1) {
        if (indexPath.row == 0) {
            WechatWithDrawSumCell *sumCell = [tableView dequeueReusableCellWithIdentifier:@"wechatWithdrawSumCellId" forIndexPath:indexPath];
            
            if (self.withdrawInfoModel) {
                sumCell.money = self.withdrawInfoModel.canCashPrice;
            }
            sumCell.selectionStyle = UITableViewCellSelectionStyleNone;
            return sumCell;
        }
        else if (indexPath.row == 1){
            WechatWithdrawInputNumCell *inputNumCell = [tableView dequeueReusableCellWithIdentifier:@"wechatWithdrawInputNumCellId" forIndexPath:indexPath];
            inputNumCell.selectionStyle = UITableViewCellSelectionStyleNone;
            
            if (self.withdrawInfoModel) {
                inputNumCell.money = self.withdrawInfoModel.canCashPrice;
            }
            
            __weak __typeof(self)weakSelf = self;
            
            inputNumCell.maxWithdrawalBlock = ^(NSString * _Nonnull maxium) {
                [weakSelf.view makeToast:[NSString stringWithFormat:@"本次最大提现金额为%@",maxium] duration:2 position:CSToastPositionTop];
            };
            
            
            inputNumCell.valueChangedBlock = ^(NSString * _Nonnull value) {
//                NSLog(@"value = %@",value);
                weakSelf.withdrawMoney = value;
            };
            
            return inputNumCell;
        }
    }
    
    return nil;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    if (indexPath.section == 0) {
        return 45;
    }
    else if (indexPath.section == 1) {
        return 45;
    }
    return 0;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    
    if (section == 1) {
        UIView *headView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, 10)];
        headView.backgroundColor = [UIColor clearColor];
        return headView;
    }
    
    return nil;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    
    if (section == 1) {
        return 10;
    }
    return 0;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    if (section == 1) {
        WechatWithdrawBottomView *bottomView = [[WechatWithdrawBottomView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, 45)];
        
        __weak __typeof(self)weakSelf = self;
        bottomView.clickWithdrawRegulationBlock = ^{
            BRGuiZeViewController *guizeVC = [[BRGuiZeViewController alloc] init];
            guizeVC.type = 1;
            [weakSelf.navigationController pushViewController:guizeVC animated:YES];
        };
        
        return bottomView;
    }
    return nil;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (section == 1) {
        return 45;
    }
    return 0;
}

- (void)dealloc {
    
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - lazy load
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.dataSource = self;
        _tableView.delegate = self;
        _tableView.bounces = NO;
        
        [_tableView registerClass:[WechatWithdrawChooseCell class] forCellReuseIdentifier:@"wechatWithDrawChooseCellId"];
        
        [_tableView registerClass:[WechatWithDrawSumCell class] forCellReuseIdentifier:@"wechatWithdrawSumCellId"];
        
        [_tableView registerClass:[WechatWithdrawInputNumCell class] forCellReuseIdentifier:@"wechatWithdrawInputNumCellId"];
    }
    return _tableView;
}

- (UIButton *)withdrawButton {
    if (!_withdrawButton) {
        _withdrawButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_withdrawButton setTitle:@"提 现" forState:UIControlStateNormal];
        [[_withdrawButton titleLabel] setFont:kFontRegular(19)];
        [_withdrawButton setBackgroundImage:[UIImage imageWithColor:[UIColor br_textBlueColor]] forState:UIControlStateNormal];
        [_withdrawButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        _withdrawButton.layer.cornerRadius = 20;
        _withdrawButton.layer.masksToBounds = YES;
        
        [_withdrawButton addTarget:self action:@selector(clickWithdrawButtonEvent:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _withdrawButton;
}

#pragma mark - 显示验证方式选择ActionSheet
- (void)showVerificationTypeActionSheet {
    NSArray *buttons = @[@"密码验证【推荐】", @"短信验证"];
    
    BRActionSheetView *actionSheet = [[BRActionSheetView alloc] init];
    actionSheet.title = @"选择验证方式";
    [actionSheet setButtons:buttons];
    [actionSheet show];
    
    __weak typeof(self) mySelf = self;
    __weak BRActionSheetView *weakActionSheet = actionSheet;
    actionSheet.clickActionButtonCallBack = ^(NSInteger index) {
        [weakActionSheet close];
        
        [mySelf.view endEditing:YES];
        
        if (index == 0) {
            // 密码验证
            mySelf.verificationType = 1;
            [mySelf handlePasswordVerification];
        } else if (index == 1) {
            // 短信验证码验证
            mySelf.verificationType = 0;
            [mySelf handleSMSVerification];
        }
    };
}

#pragma mark - 处理密码验证
- (void)handlePasswordVerification {
    [self checkWithdrawPasswordStatus];
}

#pragma mark - 检查是否设置了提现密码
- (void)checkWithdrawPasswordStatus {
    __weak typeof(self)mySelf = self;
    NSDictionary *dic = [NSDictionary dictionaryWithObjectsAndKeys:@"000454",@"method_code", nil];
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"正在检查"];
    __weak typeof(progressHUD) hud = progressHUD;
    
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        hud.hidden = YES;
        
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;
            
            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                // 检查是否设置了密码 - 使用正确的字段名 hasCashPwd
                BOOL hasPassword = [[dataDic objectForKey:@"hasCashPwd"] boolValue];
                if (hasPassword) {
                    // 已设置密码，显示密码验证弹窗
                    [mySelf showWithdrawPasswordVerificationDialog];
                } else {
                    // 未设置密码，跳转到设置提现密码页面
                    [mySelf navigateToSetWithdrawPassword];
                }
            } else {
                [mySelf.view makeToast:[dataDic objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
            }
        } else {
            [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);
        hud.hidden = YES;
        
        if (!mySelf.reachability) {
            mySelf.reachability = [Reachability reachabilityWithHostName:@"www.baidu.com"];
        }
        if ([mySelf.reachability currentReachabilityStatus] == NotReachable) {
            [mySelf.view makeToast:@"无网络，请检查您的网络连接" duration:2 position:CSToastPositionCenter];
        } else {
            if ([error.localizedDescription isEqualToString:@"The request timed out."]) {
                [mySelf.view makeToast:@"请求超时，请稍后重试" duration:2 position:CSToastPositionCenter];
            } else {
                [mySelf.view makeToast:@"检查失败" duration:2 position:CSToastPositionCenter];
            }
        }
    }];
}

#pragma mark - 显示提现密码验证弹窗
- (void)showWithdrawPasswordVerificationDialog {
    __weak typeof(self)mySelf = self;
    
    self.withdrawPassword = @"";
    
    self.passwordView = [[BRWithdrawPasswordView alloc] initWithPasswordBlock:^(NSString *password) {
        
        mySelf.withdrawPassword = [password copy];
        [mySelf.passwordView removeView];
        [mySelf passwordVerification];
        
    } cancelBlock:^{
        
        // 取消按钮点击
        
    }];
}

#pragma mark - 密码验证
- (void)passwordVerification {
    // 密码验证成功，直接进入提现流程
    [self withdrawAction];
}

#pragma mark - 处理短信验证
- (void)handleSMSVerification {
    [self smsAuthAction];
}

#pragma mark - 跳转到设置提现密码页面
- (void)navigateToSetWithdrawPassword {
    WithdrawPasswordViewController *withdrawPasswordVC = [[WithdrawPasswordViewController alloc] init];
    [self.navigationController pushViewController:withdrawPasswordVC animated:YES];
}

@end
