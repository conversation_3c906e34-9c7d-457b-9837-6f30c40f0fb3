//
//  PatientDocumentViewController.m
//  BRZY
//
//  Created by  xujiangtao on 2017/9/8.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "PatientDocumentViewController.h"
#import "BRPatientDocumentTableViewCell.h"
#import "MJRefresh.h"
#import "MedicatedInfoViewController.h"
#import "Reachability.h"
#import "PatientDocumentModel.h"
#import "BRSelectPatientView.h"
#import "BRNoDataView.h"
#import "BRAlertView.h"
#import "InterrogationAndVisitViewController.h"
#import "WzdWebViewController.h"
#import "BRActionSheetView.h"
#import "BRPatientModel.h"
#import "BRUpdateUserInfoModel.h"
#import "ScanQrcodeViewController.h"
#import "Config.h"

@interface PatientDocumentViewController () <UITableViewDelegate, UITableViewDataSource,WzdWebViewDelegate> {
    
    UILabel *_nameLabel;  //患者名字
    UILabel *_selfLabel;  //本人
    
    CGSize  selfSize;    //本人size
    NSString *_firstPatientId;  //第一次进入患者id
}

@property (nonatomic, strong)UIView  *topView;

@property (nonatomic, strong)Reachability *reachability;
@property (nonatomic, assign)int  requestPageNum;  //请求页数
@property (nonatomic, assign)BOOL requestIsAll;    //是否全部请求
@property (nonatomic, assign)BOOL isLoadData;   //no刷新数据。yes加载更多数据

@property (nonatomic, strong)UITableView    *tableView;
@property (nonatomic, strong)NSMutableArray *modelArr;
@property (nonatomic, strong)NSMutableArray *cellHArr;  //cell高度数组


@property (nonatomic, strong)NSMutableArray *nameSizeArr;
@property (nonatomic, strong)NSMutableArray *ageSizeArr;
@property (nonatomic, strong)NSMutableArray *pregnancySizeArr;
@property (nonatomic, strong)NSMutableArray *infoSizeArr;     //病症内容高度数组

@property (nonatomic, assign)BOOL  isViewShow;
@property (nonatomic, strong)BRNoDataView *noDataView;

@property (nonatomic, strong)UIButton *btn;
@property (nonatomic, strong)UIButton *exportBtn;

@end

@implementation PatientDocumentViewController

- (void)viewDidLoad {
    
    [super viewDidLoad];
    
    self.modelArr = [NSMutableArray array];
    
    //添加通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(syncPatientPrescriptionNotification:) name:kNotificationPrescriptionChangePatient object:nil];
    
    // 添加用户信息更新通知监听
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(updateUserInfo:)
                                                     name:kIMNotificationUPdatePatientInfo
                                                   object:nil];
        
}


- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

// 添加用户信息更新的处理方法
- (void)updateUserInfo:(NSNotification *)notify {
    NSDictionary *dict = [NSDictionary dictionaryWithDictionary:notify.userInfo];
    BRUpdateUserInfoModel *userInfoModel = [BRUpdateUserInfoModel mj_objectWithKeyValues:dict];
    
    // 如果更新的是当前显示的患者,更新顶部显示
    if ([self.patientId isEqualToString:userInfoModel.userId]) {
        self.patientName = userInfoModel.name;
        [self refreshTopView:[NSString stringWithFormat:@"%@病历", userInfoModel.name]];
    }
    
    // 重新计算布局尺寸并刷新列表
    [self handingData];
}

#pragma mark - configUI
- (void)configUI {
    _firstPatientId = self.patientId;
    self.isViewShow = YES;
    self.view.backgroundColor = [UIColor br_backgroundColor];
    [self createTopView];
    [self createTableView];
    
    [self getUserInfo];
    
}

- (void)viewShow {
    if (!self.requestPageNum) {
        
        [self requestData];
    }
}

//请求医案
- (void)requestData {
    
    if (!self.patientId.length) {
        [self.view makeToast:@"数据出错，patientId为空" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    [self.noDataView removeFromSuperview];
    self.noDataView = nil;
    
    __weak typeof(self)mySelf = self;
    self.requestPageNum ++;
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithObjectsAndKeys:@"000233",@"method_code",@"20",@"pageSize",self.patientId,@"patientId",[[UserManager shareInstance] getUserId],@"doctorId", nil];
    [dic setObject:[NSString stringWithFormat:@"%d",self.requestPageNum] forKey:@"page"];
    self.requestIsAll = NO;
    
    MBProgressHUD *progressHUD;
    if (self.isViewShow) {
        progressHUD = [Utils createLoadingHUDWithTitle:@"正在加载"];
    }
    __weak typeof(progressHUD) hud = progressHUD;
    self.isViewShow = YES;
    
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;

            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                if (![[dataDic objectForKey:@"consilias"] isKindOfClass:[NSNull class]]) {

                    NSArray *conArr = [dataDic objectForKey:@"consilias"];
                    for (NSDictionary *dataDic in conArr) {
                        PatientDocumentModel *model = [[PatientDocumentModel alloc] init];
                        [model setValuesForKeysWithDictionary:dataDic];
                        [mySelf.modelArr addObject:model];
                    }
                    mySelf.requestPageNum = [[dataDic objectForKey:@"page"] intValue];
                    if ([[dataDic objectForKey:@"page"] isEqualToString:[dataDic objectForKey:@"totalPage"]]) {
                        mySelf.requestIsAll = YES;
                    }

                    [mySelf handingData];

                } else {
                    
                    [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
                }

            } else {
                
                [mySelf.view makeToast:[dataDic objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
            }

        } else {
            
            [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
        }
        
        if (mySelf.isLoadData) {
            
            if (mySelf.requestIsAll) {
                mySelf.tableView.mj_footer.state = MJRefreshStateNoMoreData;
            } else {
                [mySelf.tableView.mj_footer endRefreshing];
            }
        } else {
            [mySelf.tableView.mj_header endRefreshing];
            if (mySelf.requestIsAll) {
                mySelf.tableView.mj_footer.state = MJRefreshStateNoMoreData;
            } else {
                mySelf.tableView.mj_footer.state = MJRefreshStateIdle;
            }
        }
        
        hud.hidden = YES;
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);
        hud.hidden = YES;
        if (mySelf.isLoadData) {
            [mySelf.tableView.mj_footer endRefreshing];
            
        } else {
            [mySelf.tableView.mj_header endRefreshing];
            
        }
        mySelf.noDataView = [[BRNoDataView alloc] initWithFrame:CGRectMake(0, mySelf.topView.frame.size.height, mySelf.view.frame.size.width, self.tableView.frame.size.height) withImage:[UIImage imageNamed:@"no_WIFI"] withText:@"网络异常，请手动点击重新加载"];
        [mySelf.view addSubview:mySelf.noDataView];
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:mySelf action:@selector(tapReloadData)];
        [mySelf.noDataView addGestureRecognizer:tap];
        //无网络时显示头部患者姓名
        [self refreshTopView:[NSString stringWithFormat:@"%@病例",self.patientName]];
    }];
}
#pragma mark - 去用药代理
- (void)wzdWebDidToUseDrugInfo:(NSDictionary *)dict {
    if (self.delegate) {
        [self.delegate patientDocumentDidToUseDrugInfo:dict];
    }
}

#pragma mark-  tap 重新加载
- (void)tapReloadData {
    [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationReloadDocumentList object:nil];
    self.requestPageNum = 0;
    [self requestData];
}

//创建弹出框
- (void)createAlert:(NSString *)title {
    
    BRAlertView *alertView = [[BRAlertView alloc] init];
    
    __weak typeof(alertView) aView = alertView;
    [alertView showAlertView:title completion:^{
        [aView close];
    }];
}

//创建顶部视图
- (void)createTopView {
    self.topView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, FONTSIZE(52))];
    self.topView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:self.topView];
    
    UIView *lineView = [[UIView alloc] initWithFrame:CGRectMake(0, self.topView.frame.size.height-1, self.topView.frame.size.width, 1)];
    lineView.backgroundColor = [UIColor colorWithRed:220.0/255 green:220.0/255 blue:220.0/255 alpha:1.f];
    [self.topView addSubview:lineView];
    
    _nameLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    _nameLabel.font = FONT_Regular(19);
    _nameLabel.textColor = [UIColor colorWithRed:22.0/255 green:25.0/255 blue:30.0/255 alpha:1.f];
    [self.topView addSubview:_nameLabel];
    
    selfSize = [@"本人" boundingRectWithSize:CGSizeMake(500, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(12)} context:nil].size;
    _selfLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    _selfLabel.textColor = [UIColor colorWithRed:145.0/255 green:155.0/255 blue:167.0/255 alpha:1.f];
    _selfLabel.font = FONT_Light(12);
    [self.topView addSubview:_selfLabel];
    
    // 计算"更换"按钮的宽度
    CGSize changeSize = [@"更换" boundingRectWithSize:CGSizeMake(500, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(15)} context:nil].size;
    CGFloat changeBtnWidth = changeSize.width + FONTSIZE(20); // 添加内边距
    
    // 计算"导出"按钮的宽度（包含图标）
    CGSize exportSize = [@"导出" boundingRectWithSize:CGSizeMake(500, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(15)} context:nil].size;
    CGFloat exportBtnWidth = exportSize.width + FONTSIZE(16) + FONTSIZE(40); // 图标宽度16 + 内边距20
    
    // 更换按钮
    self.btn = [UIButton buttonWithType:UIButtonTypeRoundedRect];
    self.btn.frame = CGRectMake(self.topView.frame.size.width-FONTSIZE(15)-changeBtnWidth, self.topView.frame.size.height/2-FONTSIZE(26)/2, changeBtnWidth, FONTSIZE(26));
    self.btn.backgroundColor = [UIColor br_mainBlueColor];
    [self.btn setTitle:@"更换" forState:UIControlStateNormal];
    [self.btn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.btn.titleLabel.font = FONT_Light(15);
    [self.btn addTarget:self action:@selector(btnClick) forControlEvents:UIControlEventTouchUpInside];
    self.btn.layer.masksToBounds = YES;
    self.btn.layer.cornerRadius = FONTSIZE(13);
    
    [self.topView addSubview:self.btn];
    
    // 创建导出按钮
    self.exportBtn = [UIButton buttonWithType:UIButtonTypeRoundedRect];
    self.exportBtn.frame = CGRectMake(self.btn.frame.origin.x-FONTSIZE(10)-exportBtnWidth, self.topView.frame.size.height/2-FONTSIZE(26)/2, exportBtnWidth, FONTSIZE(26));
    self.exportBtn.backgroundColor = [UIColor br_mainBlueColor];
    [self.exportBtn setTitle:@"导出" forState:UIControlStateNormal];
    [self.exportBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.exportBtn.titleLabel.font = FONT_Light(15);
    [self.exportBtn addTarget:self action:@selector(exportBtnClick) forControlEvents:UIControlEventTouchUpInside];
    self.exportBtn.layer.masksToBounds = YES;
    self.exportBtn.layer.cornerRadius = FONTSIZE(13);
    
    // 设置图标
    UIImage *exportIcon = [[UIImage imageNamed:@"daichuli_SM"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
    [self.exportBtn setImage:exportIcon forState:UIControlStateNormal];
    self.exportBtn.tintColor = [UIColor whiteColor];
    self.exportBtn.imageEdgeInsets = UIEdgeInsetsMake(0, -5, 0, 5);
    self.exportBtn.titleEdgeInsets = UIEdgeInsetsMake(0, 5, 0, -5);
    
    
    [self.topView addSubview:self.exportBtn];
}

//创建tableview
- (void)createTableView {
    
    self.cellHArr = [NSMutableArray array];
    self.infoSizeArr  =[NSMutableArray array];
    
    self.tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, self.topView.frame.size.height, self.view.frame.size.width, self.view.frame.size.height-self.topView.frame.size.height)];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectZero];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.backgroundColor = [UIColor br_backgroundColor];
    [self.view addSubview:self.tableView];
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    self.tableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingTarget:self refreshingAction:@selector(refreshTable)];
    self.tableView.mj_footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(loadMore)];
    self.tableView.estimatedRowHeight = 160.f;
}

- (void)authFailed {
    self.noDataView = [[BRNoDataView alloc] initWithFrame:CGRectMake(0, self.topView.frame.size.height, self.view.frame.size.width, self.tableView.frame.size.height) withImage:[UIImage imageNamed:@"patient_NoData"] withText:@"认证通过后才可查看医案"];
    [self.view addSubview:self.noDataView];
    
    self.btn.userInteractionEnabled = NO;
}

#pragma mark - 处理数据
- (void)handingData {
    
    if ([_firstPatientId isEqualToString:self.patientId]) {
        
        NSString *nameS = @"";
        if (self.patientName) {
            nameS = self.patientName;
        }
        [self refreshTopView:[NSString stringWithFormat:@"%@病历",nameS]];
    }
    
    if (!self.modelArr.count) {
        self.noDataView = [[BRNoDataView alloc] initWithFrame:CGRectMake(0, self.topView.frame.size.height, self.view.frame.size.width, self.tableView.frame.size.height) withImage:[UIImage imageNamed:@"patient_NoData"] withText:@"暂无医案记录"];
        [self.view addSubview:self.noDataView];
        return;
    }
    
    [self.cellHArr removeAllObjects];
    [self.infoSizeArr removeAllObjects];
    if (!self.nameSizeArr) {
        self.nameSizeArr = [NSMutableArray array];
    } else {
        [self.nameSizeArr removeAllObjects];
    }
    if (!self.ageSizeArr) {
        self.ageSizeArr = [NSMutableArray array];
    } else {
        [self.ageSizeArr removeAllObjects];
    }
    if (!self.pregnancySizeArr) {
        self.pregnancySizeArr = [NSMutableArray array];
    } else {
        [self.pregnancySizeArr removeAllObjects];
    }
    __weak typeof(self) mySelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        
        CGSize size2 = [@"患者:" boundingRectWithSize:CGSizeMake(200, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(15)} context:nil].size;
        for (PatientDocumentModel *model in mySelf.modelArr) {
            NSString *symStr = @"";
            if (![model.symptom isKindOfClass:[NSNull class]]) {
                symStr = model.symptom;
            }
            CGSize size = [symStr boundingRectWithSize:CGSizeMake(mySelf.tableView.frame.size.width-FONTSIZE(15)*2-4-FONTSIZE(14)*2-FONTSIZE(5)-size2.width, 1300) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(15)} context:nil].size;
            
            float cellH = FONTSIZE(15) + 4 + FONTSIZE(38) + FONTSIZE(15) + size2.height + FONTSIZE(11) + size.height + 1 + FONTSIZE(33) + FONTSIZE(19);
            [mySelf.cellHArr addObject:[NSNumber numberWithFloat:cellH + 1]];
            [mySelf.infoSizeArr addObject:[NSNumber numberWithFloat:size.height + 1]];
            
            NSString *nameStr = model.name;
            NSString *ageStr = [NSString stringWithFormat:@"%@",model.age];
            NSString *pregnancyStr = @"";
            if (![model.conceive isKindOfClass:[NSNull class]]) {
                if ([model.conceive isEqualToString:@"1"]) {
                    pregnancyStr = @"怀孕";
                }
            }
            
            CGSize nameSize = [nameStr boundingRectWithSize:CGSizeMake(700, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(15)} context:nil].size;
            CGSize ageSize = [ageStr boundingRectWithSize:CGSizeMake(700, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(15)} context:nil].size;
            CGSize pregnancySize = [pregnancyStr boundingRectWithSize:CGSizeMake(700, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(15)} context:nil].size;
            
            [mySelf.nameSizeArr addObject:[NSNumber numberWithFloat:nameSize.width+1]];
            [mySelf.ageSizeArr addObject:[NSNumber numberWithFloat:ageSize.width+1]];
            [mySelf.pregnancySizeArr addObject:[NSNumber numberWithFloat:pregnancySize.width+1]];
        }
        
        [mySelf.tableView reloadData];
    });
}

//下拉刷新
- (void)refreshTable {
    [self.modelArr removeAllObjects];
    self.requestPageNum = 0;
    self.isLoadData = NO;
    self.isViewShow = NO;
    [self requestData];
    
}

//上拉加载
- (void)loadMore {
    self.isLoadData = YES;
    self.isViewShow = NO;
    [self requestData];
}

#pragma mark -UITableView 代理
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.modelArr.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return [self.cellHArr[indexPath.row] floatValue];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *cellID = @"cellId";
     BRPatientDocumentTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellID];
    
    if (!cell) {
        cell = [[NSBundle mainBundle] loadNibNamed:@"BRPatientDocumentTableViewCell" owner:self options:nil].lastObject;
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        cell.clipsToBounds = YES;
        cell.backgroundColor = [UIColor br_backgroundColor];
        
    }
    
    if (self.cellHArr.count == 0) {
        return cell;
    }
    
    cell.backGImageView.frame = CGRectMake(cell.backGImageView.frame.origin.x, cell.backGImageView.frame.origin.y, cell.backGImageView.frame.size.width, [self.cellHArr[indexPath.row] floatValue] - FONTSIZE(15) - 4);
    cell.backGView.frame = CGRectMake(cell.backGView.frame.origin.x, cell.backGView.frame.origin.y, cell.backGView.frame.size.width, cell.backGImageView.frame.size.height-4);
    cell.lineView.frame = CGRectMake(FONTSIZE(14), cell.backGView.frame.size.height-FONTSIZE(33)-1, cell.backGView.frame.size.width-FONTSIZE(14)*2, 1);
    cell.infoLabel.frame = CGRectMake(FONTSIZE(14), cell.backGView.frame.size.height-FONTSIZE(33), cell.backGView.frame.size.width/2, FONTSIZE(33));
    cell.rightImageView.frame = CGRectMake(cell.backGView.frame.size.width-FONTSIZE(14)-FONTSIZE(20), cell.infoLabel.frame.origin.y+cell.infoLabel.frame.size.height/2-FONTSIZE(20)/2, FONTSIZE(20), FONTSIZE(20));
    
    if (!self.modelArr || self.modelArr.count == 0) {
        return cell;
    }
    
    PatientDocumentModel *model = self.modelArr[indexPath.row];
    
    if (![model.type isKindOfClass:[NSNull class]]) {
        if ([model.type isEqualToString:@"1"]) {
            
            cell.titleLabel.text = @"问诊单";
            
            cell.symptomsLabel.text = @"病症:";
            cell.titleBackGView.backgroundColor = [UIColor colorWithRed:83.0/255 green:133.0/255 blue:223.0/255 alpha:1.f];
            cell.headImageView.image = [UIImage imageNamed:@"patient_wenhao"];
            cell.rightImageView.image = [UIImage imageNamed:@"patient_blueRight"];
            cell.infoLabel.textColor = [UIColor colorWithRed:83.0/255 green:133.0/255 blue:223.0/255 alpha:1.f];
            
        } else if ([model.type isEqualToString:@"2"]) {
            
            cell.titleLabel.text = @"复诊单";
            
            cell.symptomsLabel.text = @"病症:";
            cell.titleBackGView.backgroundColor = [UIColor colorWithRed:228.0/255 green:102.0/255 blue:75.0/255 alpha:1.f];
            cell.headImageView.image = [UIImage imageNamed:@"patient_fuzhen"];
            cell.rightImageView.image = [UIImage imageNamed:@"patient_redRight"];
            cell.infoLabel.textColor = [UIColor colorWithRed:228.0/255 green:102.0/255 blue:75.0/255 alpha:1.f];
            
        } else if ([model.type isEqualToString:@"3"]) {
            
            cell.titleLabel.text = @"用药";
            
            cell.symptomsLabel.text = @"辨证:";
            cell.titleBackGView.backgroundColor = [UIColor colorWithRed:89.0/255 green:187.0/255 blue:134.0/255 alpha:1.f];
            cell.headImageView.image = [UIImage imageNamed:@"patient_yongyao"];
            cell.rightImageView.image = [UIImage imageNamed:@"patient_greenRight"];
            cell.infoLabel.textColor = [UIColor colorWithRed:89.0/255 green:187.0/255 blue:134.0/255 alpha:1.f];
            
        }
    }
    if (![model.createTime isKindOfClass:[NSNull class]]) {
        cell.timeLabel.text = model.createTime;
    }
    
    
    float maxNameW = 0;
    NSString *pregnancyStr = @"";
    if (![model.conceive isKindOfClass:[NSNull class]]) {
        if ([model.conceive isEqualToString:@"1"]) {
            pregnancyStr = @"怀孕";
        }
    }
    
    float nameSizeW = [self.nameSizeArr[indexPath.row] floatValue];
    float ageSizeW = [self.ageSizeArr[indexPath.row] floatValue];
    float pregSizeW = [self.pregnancySizeArr[indexPath.row] floatValue];
    
    if (pregnancyStr.length) {
        maxNameW = cell.backGView.frame.size.width - cell.patientLabel.frame.origin.x - cell.patientLabel.frame.size.width - FONTSIZE(5) - FONTSIZE(14) - pregSizeW - FONTSIZE(5) - FONTSIZE(20) - FONTSIZE(10) - ageSizeW - FONTSIZE(10);
    } else {
        maxNameW = cell.backGView.frame.size.width - cell.patientLabel.frame.origin.x - cell.patientLabel.frame.size.width - FONTSIZE(5) - FONTSIZE(14) - FONTSIZE(20) - FONTSIZE(10) - ageSizeW - FONTSIZE(10);
    }
    if (nameSizeW < maxNameW) {
        cell.nameLabel.frame = CGRectMake(cell.patientLabel.frame.origin.x+cell.patientLabel.frame.size.width+FONTSIZE(5), cell.patientLabel.frame.origin.y, nameSizeW, cell.patientLabel.frame.size.height);
    } else {
        cell.nameLabel.frame = CGRectMake(cell.patientLabel.frame.origin.x+cell.patientLabel.frame.size.width+FONTSIZE(5), cell.patientLabel.frame.origin.y, maxNameW, cell.patientLabel.frame.size.height);
    }
    if (![model.name isKindOfClass:[NSNull class]]) {
        cell.nameLabel.text = model.name;
    }
    
//    cell.ageLabel.frame = CGRectMake(cell.nameLabel.frame.origin.x+cell.nameLabel.frame.size.width+FONTSIZE(10), cell.nameLabel.frame.origin.y, ageSizeW, cell.nameLabel.frame.size.height);
//    if (![model.age isKindOfClass:[NSNull class]]) {
//        cell.ageLabel.text = [NSString stringWithFormat:@"%@岁",model.age];
//    }
//    
//    cell.sexImageView.frame = CGRectMake(cell.ageLabel.frame.origin.x+cell.ageLabel.frame.size.width+FONTSIZE(10), cell.ageLabel.frame.origin.y+cell.ageLabel.frame.size.height/2-FONTSIZE(20)/2, FONTSIZE(20), FONTSIZE(20));
//    if (![model.sex isKindOfClass:[NSNull class]]) {
//        if ([model.sex isEqualToString:@"0"]) {
//            cell.sexImageView.image = [UIImage imageNamed:@"patient_woman"];
//        } else {
//            cell.sexImageView.image = [UIImage imageNamed:@"patient_man"];
//        }
//    }
    
    cell.genderString = model.sex;
    cell.ageString = model.age;
    
    cell.pregnancyLabel.frame = CGRectMake(cell.sexImageView.frame.origin.x+cell.sexImageView.frame.size.width+FONTSIZE(5), cell.ageLabel.frame.origin.y, pregSizeW, cell.ageLabel.frame.size.height);
    
    cell.pregnancyLabel.text = pregnancyStr;
    
    cell.symptomsInfoLabel.frame = CGRectMake(cell.symptomsLabel.frame.origin.x+cell.symptomsLabel.frame.size.width+FONTSIZE(5), cell.symptomsLabel.frame.origin.y, cell.backGView.frame.size.width-cell.symptomsLabel.frame.origin.x-cell.symptomsLabel.frame.size.width-FONTSIZE(5)-FONTSIZE(14), [self.infoSizeArr[indexPath.row] floatValue]);
    
    cell.symptomsInfoLabel.text = model.symptom;
    
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    
    PatientDocumentModel *model = self.modelArr[indexPath.row];
    
    if (![model.type isKindOfClass:[NSNull class]]) {
        
        if ([model.type isEqualToString:@"3"]) {
            //用药
            MedicatedInfoViewController *medi = [[MedicatedInfoViewController alloc] init];
            medi.btnShowOrNot = NSBtnNotShow;
            medi.orderId = model.orderId;
            [self.navigationController pushViewController:medi animated:YES];
            
        } else {
            //问诊单,复诊单
            
            NSString *urlStr = @"";
            if (![model.url isKindOfClass:[NSNull class]]) {
                urlStr = model.url;
            }
            urlStr = [NSString stringWithFormat:@"%@%@",urlStr,@"&flag=0"];
            
            WzdWebViewController *webViewController = [[WzdWebViewController alloc] init];
            webViewController.delegate = self;
            webViewController.likeUrl = urlStr;
            //        webViewController.showTitle = @"发送问诊单";
            if ([model.type isEqualToString:@"1"]) {
                webViewController.showTitle = @"问诊单";
            } else {
                webViewController.showTitle = @"复诊单";
            }
            [self.navigationController pushViewController:webViewController animated:YES];
            
            
//            InterrogationAndVisitViewController *inV = [[InterrogationAndVisitViewController alloc] init];
//            inV.dataModel = model;
//            inV.patientId = self.patientId;
//            [self.navigationController pushViewController:inV animated:YES];
        }
    }
    
}
#pragma mark - 与用药界面同步更新选择的患者
- (void)syncPatientPrescriptionNotification:(NSNotification *)notification {
    
    NSDictionary *userInfo = [notification userInfo];
    //获取参数，进行同步
    NSString *patientId = [userInfo objectForKey:@"id"];
    NSString *patientName = [userInfo objectForKey:@"name"];
    
    //如果与当前的用户id相同，则不用进行处理
    if ([patientId isEqualToString:self.patientId]) {
        return;
    }
    
    self.patientId = patientId;
    self.requestPageNum = 0;
    self.isLoadData = NO;
    [self.modelArr removeAllObjects];
    
    [self requestData];
    
    [self refreshTopView:[NSString stringWithFormat:@"%@病历",patientName]];
}

#pragma mark -  请求患者信息，更新当前本人的名称

- (void)getUserInfo {
    
    __weak typeof(self)mySelf = self;
    
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithObjectsAndKeys:@"000232",@"method_code",_firstPatientId,@"patientId", nil];
    
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"正在加载"];
    __weak typeof(progressHUD) hud = progressHUD;
    
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;
            
            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                
                NSArray *patientArray = [BRPatientModel mj_objectArrayWithKeyValuesArray:[dataDic objectForKey:@"patients"]];
                for (int i = 0; i < patientArray.count; i++) {
                    BRPatientModel *model = [patientArray objectAtIndex:i];
                    if (model.isSelf) {
                        mySelf.patientName = model.name;
                        [mySelf refreshTopView:[NSString stringWithFormat:@"%@病例",mySelf.patientName]];
                        break;
                    }
                }
                
            } else {
                NSString *errorMsg = [dataDic objectForKey:@"errorMsg"];
                [mySelf.view makeToast:errorMsg duration:2 position:CSToastPositionCenter];
            }
            
        } else {
            [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
        }
        
        hud.hidden = YES;
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);
        
        if (!mySelf.reachability) {
            mySelf.reachability = [Reachability reachabilityWithHostName:@"www.baidu.com"];
        }
        
        if ([mySelf.reachability currentReachabilityStatus] == NotReachable) {
            [mySelf.view makeToast:@"无网络，请检查您的网络连接" duration:2 position:CSToastPositionCenter];
        } else {
            if ([error.localizedDescription isEqualToString:@"The request timed out."]) {
                [mySelf.view makeToast:@"请求超时，请稍后重试" duration:2 position:CSToastPositionCenter];
            } else {
                [mySelf.view makeToast:@"加载失败" duration:2 position:CSToastPositionCenter];
            }
        }
        
        hud.hidden = YES;
    }];
}

#pragma mark - 更换
- (void)btnClick {
    self.btn.userInteractionEnabled = NO;
    __weak typeof(self)mySelf = self;
    
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithObjectsAndKeys:@"000232",@"method_code",_firstPatientId,@"patientId", nil];
    
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"正在加载"];
    __weak typeof(progressHUD) hud = progressHUD;
    
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        mySelf.btn.userInteractionEnabled = YES;
        
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;
            
            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                if (![[dataDic objectForKey:@"patients"] isKindOfClass:[NSNull class]]) {
                    // 使用 MJExtension 转换数据模型
                    NSArray *patientArray = [BRPatientModel mj_objectArrayWithKeyValuesArray:[dataDic objectForKey:@"patients"]];
                    
                    NSMutableArray *titleArray = [NSMutableArray arrayWithCapacity:0];
                    int isSelfIndex = 0;
                    
                    // 遍历模型数组获取名字和isSelf标记
                    for (int i = 0; i < patientArray.count; i++) {
                        BRPatientModel *model = patientArray[i];
                        [titleArray addObject:model.name ?: @""];
                        
                        if ([model.isSelf isEqualToString:@"1"]) {
                            isSelfIndex = i + 1;
                        }
                    }
                    
                    BRActionSheetView *actionSheetView = [[BRActionSheetView alloc] init];
                    actionSheetView.title = @"选择患者";
                    [actionSheetView setButtons:titleArray withSelfIndex:isSelfIndex withWithdrawMethod:NO];
                    
                    [actionSheetView show];
                    
                    __weak BRActionSheetView *weakActionSheet = actionSheetView;
                    
                    weakActionSheet.clickActionButtonCallBack = ^(NSInteger index) {
                        [weakActionSheet close];
                        
                        BRPatientModel *selectedModel = patientArray[index];
                        
                        if (selectedModel.patientId) {
                            mySelf.patientId = [NSString stringWithFormat:@"%@", selectedModel.patientId];
                            mySelf.requestPageNum = 0;
                            mySelf.isLoadData = NO;
                            [mySelf.modelArr removeAllObjects];
                            [mySelf requestData];
                            
                            // 转换回字典以保持原有通知逻辑
                            NSDictionary *userInfo = selectedModel.mj_keyValues;
                            [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationPatientDocumentChangePatient object:nil userInfo:userInfo];
                        }
                        
                        [mySelf refreshTopView:[NSString stringWithFormat:@"%@病历", selectedModel.name ?: @""]];
                    };
                    
                } else {
                    [mySelf.view makeToast:@"无数据" duration:2 position:CSToastPositionCenter];
                }
                
            } else {
                NSString *errorMsg = [dataDic objectForKey:@"errorMsg"];
                [mySelf.view makeToast:errorMsg duration:2 position:CSToastPositionCenter];
            }
            
        } else {
            [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
        }
        
        hud.hidden = YES;
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);
        mySelf.btn.userInteractionEnabled = YES;
        
        if (!mySelf.reachability) {
            mySelf.reachability = [Reachability reachabilityWithHostName:@"www.baidu.com"];
        }
        
        if ([mySelf.reachability currentReachabilityStatus] == NotReachable) {
            [mySelf.view makeToast:@"无网络，请检查您的网络连接" duration:2 position:CSToastPositionCenter];
        } else {
            if ([error.localizedDescription isEqualToString:@"The request timed out."]) {
                [mySelf.view makeToast:@"请求超时，请稍后重试" duration:2 position:CSToastPositionCenter];
            } else {
                [mySelf.view makeToast:@"加载失败" duration:2 position:CSToastPositionCenter];
            }
        }
        
        hud.hidden = YES;
    }];
}

//刷新顶部视图
- (void)refreshTopView:(NSString *)nameStr {
    // 计算两个按钮的总宽度
    float buttonsWidth = self.btn.frame.size.width + self.exportBtn.frame.size.width + FONTSIZE(10); // 两个按钮 + 间距
    float maxW = self.topView.frame.size.width-FONTSIZE(15)*2-buttonsWidth-selfSize.width-8;
    CGSize size2 = [nameStr boundingRectWithSize:CGSizeMake(500, 30) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading attributes:@{NSFontAttributeName:FONT_Regular(19)} context:nil].size;
    
    if (size2.width < maxW) {
        _nameLabel.frame = CGRectMake(FONTSIZE(15), self.topView.frame.size.height/2-size2.height/2, size2.width+1, size2.height);
    } else {
        _nameLabel.frame = CGRectMake(FONTSIZE(15), self.topView.frame.size.height/2-size2.height/2, maxW, size2.height);
    }
    _selfLabel.frame = CGRectMake(_nameLabel.frame.origin.x+_nameLabel.frame.size.width+8, _nameLabel.frame.origin.y+_nameLabel.frame.size.height-selfSize.height-2, selfSize.width, selfSize.height);
    
    _nameLabel.text = nameStr;
    if ([_firstPatientId isEqualToString:self.patientId]) {
        _selfLabel.text = @"本人";
    } else {
        _selfLabel.text = @"";
    }
    
}

#pragma mark - Export Button Action
- (void)exportBtnClick {
    self.exportBtn.enabled = NO;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.exportBtn.enabled = YES;
    });
    
    // Check if user is logged in and has valid session token
    NSString *sessionToken = [Config shareInstance].sessionToken;
    NSString *userId = [UserManager shareInstance].getUserId;
    
    if (!sessionToken || [sessionToken isEqualToString:@""] || !userId || [userId isEqualToString:@""]) {
        [self.view makeToast:@"请先登录" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    // Create and configure scan controller
    ScanQrcodeViewController *scanVC = [[ScanQrcodeViewController alloc] init];
    scanVC.shouldShowWebExportHint = YES;
    
    // Override the scan result handling for web login
    [self setupWebLoginScanHandler:scanVC];
    
    [self.navigationController pushViewController:scanVC animated:YES];
}

- (void)setupWebLoginScanHandler:(ScanQrcodeViewController *)scanVC {
    // We'll use method swizzling or notification to handle the scan result
    // For now, let's use notification pattern
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(handleQRCodeScanResult:) 
                                                 name:@"QRCodeScanResultNotification" 
                                               object:nil];
}

- (void)handleQRCodeScanResult:(NSNotification *)notification {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:@"QRCodeScanResultNotification" object:nil];
    
    NSString *scanResult = notification.userInfo[@"result"];
    
    if ([self isWebLoginQRCode:scanResult]) {
        [self processWebLoginQRCode:scanResult];
    } else {
        [self.view makeToast:@"这不是医案导出二维码" duration:2 position:CSToastPositionCenter];
    }
}

- (BOOL)isWebLoginQRCode:(NSString *)qrContent {
    // Check if QR code is for web login: brzy-web://login?session=xxx&server=xxx
    return [qrContent hasPrefix:@"brzy-web://login?"];
}

- (void)processWebLoginQRCode:(NSString *)qrContent {
    // Parse QR code URL to extract session and server info
    NSURL *url = [NSURL URLWithString:qrContent];
    NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
    
    NSString *sessionId = nil;
    NSString *serverUrl = nil;
    
    for (NSURLQueryItem *item in components.queryItems) {
        if ([item.name isEqualToString:@"session"]) {
            sessionId = item.value;
        } else if ([item.name isEqualToString:@"server"]) {
            serverUrl = item.value;
        }
    }
    
    if (!sessionId || !serverUrl) {
        [self.view makeToast:@"二维码格式错误" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    // Show confirmation dialog
    [self showWebLoginConfirmationWithSessionId:sessionId serverUrl:serverUrl];
}

- (void)showWebLoginConfirmationWithSessionId:(NSString *)sessionId serverUrl:(NSString *)serverUrl {
    BRAlertView *alertView = [[BRAlertView alloc] init];
    [alertView.okButton setTitle:@"确认授权" forState:UIControlStateNormal];
    [alertView.cancelButton setTitle:@"取消" forState:UIControlStateNormal];
    
    NSString *patientName = self.patientName ?: @"当前患者";
    NSString *message = [NSString stringWithFormat:@"确认授权医案导出网页端访问 %@ 的医案数据吗？", patientName];
    
    __weak typeof(self) weakSelf = self;
    [alertView showAlertViewWithCancelButton:message completion:^(BOOL isOk) {
        if (isOk) {
            [weakSelf authorizeWebLoginWithSessionId:sessionId serverUrl:serverUrl];
        }
        [alertView close];
    }];
}

- (void)authorizeWebLoginWithSessionId:(NSString *)sessionId serverUrl:(NSString *)serverUrl {
    // Show loading
    MBProgressHUD *HUD = [Utils createLoadingHUD];
    
    // Prepare device info with device identifiers
    NSString *appVersion = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    NSString *osVersion = [[UIDevice currentDevice] systemVersion];
    NSString *deviceModel = [[UIDevice currentDevice] model];
    
    // Get device identifier
    NSString *deviceId = [UIDevice currentDevice].identifierForVendor.UUIDString;
    NSString *shortDeviceId = [deviceId substringToIndex:MIN(deviceId.length, 16)]; // 截取前16位
    
    // Prepare current patient info
    NSString *currentPatientId = self.patientId;
    NSString *currentPatientName = self.patientName;
    
    // Prepare request payload with device identifiers
    NSDictionary *deviceInfo = @{
        @"app_version": appVersion ?: @"",
        @"os_version": osVersion ?: @"",
        @"device_model": deviceModel ?: @"",
        @"platform": @"iOS",
        @"device_id": shortDeviceId ?: @"",
        @"imei": shortDeviceId ?: @"",
        @"mac": shortDeviceId ?: @""
    };
    
    // Generate a proper auth code for this authorization
    NSTimeInterval timestamp = [[NSDate date] timeIntervalSince1970] * 1000;
    NSInteger random = arc4random_uniform(9999);
    NSString *userId = [UserManager shareInstance].getUserId;
    NSString *authCode = [NSString stringWithFormat:@"%.0f_%04ld_%@_web", timestamp, (long)random, userId];
    
    NSDictionary *requestData = @{
        @"auth_code": authCode,
        @"session_token": [Config shareInstance].sessionToken,
        @"web_session_id": sessionId,
        @"device_info": deviceInfo,
        @"current_patient_id": currentPatientId ?: @"",
        @"current_patient_name": currentPatientName ?: @""
    };
    
    // Send authorization request
    NSString *authUrl = [NSString stringWithFormat:@"%@/api/auth/scan", serverUrl];
    
    __weak typeof(self) weakSelf = self;
    [self sendAuthRequest:authUrl data:requestData completion:^(BOOL success, NSString *message) {
        dispatch_async(dispatch_get_main_queue(), ^{
            HUD.hidden = YES;
            
            if (success) {
                [weakSelf.view makeToast:@"授权成功" duration:2 position:CSToastPositionCenter];
            } else {
                NSString *errorMsg = message ?: @"授权失败";
                [weakSelf.view makeToast:errorMsg duration:2 position:CSToastPositionCenter];
            }
        });
    }];
}

- (void)sendAuthRequest:(NSString *)url data:(NSDictionary *)data completion:(void(^)(BOOL success, NSString *message))completion {
    // Create URL request
    NSURL *requestURL = [NSURL URLWithString:url];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:requestURL];
    request.HTTPMethod = @"POST";
    [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    
    // Convert data to JSON
    NSError *jsonError;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:data options:0 error:&jsonError];
    
    if (jsonError) {
        if (completion) completion(NO, @"请求数据格式错误");
        return;
    }
    
    request.HTTPBody = jsonData;
    
    // Send request
    NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:request completionHandler:^(NSData *responseData, NSURLResponse *response, NSError *error) {
        if (error) {
            if (completion) completion(NO, error.localizedDescription);
            return;
        }
        
        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
        if (httpResponse.statusCode != 200) {
            NSString *errorMsg = [NSString stringWithFormat:@"服务器错误: %ld", (long)httpResponse.statusCode];
            if (completion) completion(NO, errorMsg);
            return;
        }
        
        // Parse response
        NSError *parseError;
        NSDictionary *responseDict = [NSJSONSerialization JSONObjectWithData:responseData options:0 error:&parseError];
        
        if (parseError) {
            if (completion) completion(NO, @"响应数据解析失败");
            return;
        }
        
        NSNumber *code = responseDict[@"code"];
        NSString *message = responseDict[@"message"];
        
        BOOL success = code && [code intValue] == 200;
        if (completion) completion(success, message);
    }];
    
    [task resume];
}

#pragma mark -
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}

@end
