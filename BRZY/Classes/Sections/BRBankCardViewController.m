//
//  BRBankCardViewController.m
//  BRZY
//
//  Created by <PERSON><PERSON><PERSON> han on 2017/12/6.
//  Copyright © 2017年 Yi YiKang Technology(Beijing) Co.,Ltd. All rights reserved.
//

#import "BRBankCardViewController.h"
#import "BRGuiZeViewController.h"
#import "Reachability.h"
#import "BRBinDingViewController.h"
#import "BRAlertView.h"
#import "ShowMyPurseSMSCode.h"
#import "BRActionSheetView.h"
#import "BRWithdrawPasswordView.h"
#import "WithdrawPasswordViewController.h"
#import "NSString+YYAdd.h"



@interface BRBankCardViewController () <UITableViewDelegate,UITableViewDataSource,UITextFieldDelegate>

@property (nonatomic, strong)UITableView *tableView;

@property (nonatomic, strong)NSString *bankName;
@property (nonatomic, strong)NSString *bankCard;
@property (nonatomic, strong)NSString *moneyNum;
@property (nonatomic, strong)Reachability *reachability;
@property (nonatomic, strong)NSDictionary *dataDic;
@property (nonatomic, strong)UITextField *textField;
@property (nonatomic, strong)NSString *cardNumAll;
@property (nonatomic, strong)NSString *peopleName;
@property (nonatomic, strong)NSString *smsCode;
@property (nonatomic, strong)ShowMyPurseSMSCode *smsCodeView;
@property (nonatomic, assign)NSInteger timeNum;
@property (nonatomic, strong)NSTimer *timer;
@property (nonatomic, strong)UIButton *tixianBtn;
@property (nonatomic, strong)BRWithdrawPasswordView *passwordView;
@property (nonatomic, strong)NSString *withdrawPassword;

@property (nonatomic, strong)NSString *bankCardNumStr;

@property (nonatomic) BOOL isHaveDian;

// 验证方式标识 0:短信验证 1:密码验证
@property (nonatomic, assign)NSInteger verificationType;

@end

@implementation BRBankCardViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor colorWithRed:COLORNUM(246.0) green:COLORNUM(246.0) blue:COLORNUM(246.0) alpha:1.f];
    __weak typeof(self)mySelf = self;
    [self creatLeftAndTitleNavItemwithTitle:@"提现到银行卡" backClick:^{
        [mySelf.navigationController popViewControllerAnimated:YES];
    }];
    [self createUI];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self requestData];
}

- (NSString *)cardNumAll {
    if (!_cardNumAll) {
        _cardNumAll = @"";
    }
    return _cardNumAll;
}

- (void)createUI {
    
    self.tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, FONTSIZE(45)*2)];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.bounces = NO;
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectZero];
    [self.view addSubview:self.tableView];
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    CGSize size = [@"提现规则" boundingRectWithSize:CGSizeMake(1000, 35) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(15)} context:nil].size;
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeRoundedRect];
    btn.frame = CGRectMake(self.view.frame.size.width-FONTSIZE(15)-size.width, self.tableView.frame.size.height+FONTSIZE(11), size.width, size.height);
    [btn setTitle:@"提现规则" forState:UIControlStateNormal];
    [btn setTitleColor:[UIColor colorWithRed:83.0/255 green:133.0/255 blue:223.0/255 alpha:1.f] forState:UIControlStateNormal];
    [btn addTarget:self action:@selector(guizeClick) forControlEvents:UIControlEventTouchUpInside];
    btn.titleLabel.font = FONT_Light(15);
    [self.view addSubview:btn];
    
    UIButton *imageBtn = [UIButton buttonWithType:UIButtonTypeRoundedRect];
    imageBtn.frame = CGRectMake(btn.frame.origin.x-FONTSIZE(3)-FONTSIZE(20), btn.frame.origin.y+btn.frame.size.height/2-FONTSIZE(20)/2, FONTSIZE(20), FONTSIZE(20));
    [imageBtn setBackgroundImage:[UIImage imageNamed:@"myPurse_tanhao"] forState:UIControlStateNormal];
    [imageBtn addTarget:self action:@selector(guizeClick) forControlEvents:UIControlEventTouchUpInside];
    imageBtn.layer.masksToBounds = YES;
    imageBtn.layer.cornerRadius = imageBtn.frame.size.width/2;
    [self.view addSubview:imageBtn];
    
    self.tixianBtn = [UIButton buttonWithType:UIButtonTypeRoundedRect];
    self.tixianBtn.frame = CGRectMake(FONTSIZE(45), imageBtn.frame.origin.y+imageBtn.frame.size.height+FONTSIZE(133), self.view.frame.size.width-FONTSIZE(45)*2, FONTSIZE(40));
    [self.tixianBtn setTitle:@"提 现" forState:UIControlStateNormal];
    self.tixianBtn.backgroundColor = [UIColor colorWithRed:83.0/255 green:133.0/255 blue:223.0/255 alpha:1.f];
    [self.tixianBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.tixianBtn.titleLabel.font = FONT_Regular(19);
    self.tixianBtn.layer.masksToBounds = YES;
    self.tixianBtn.layer.cornerRadius = FONTSIZE(18);
    [self.tixianBtn addTarget:self action:@selector(tixianClick2) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.tixianBtn];
    
    UIView *lineView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.frame.size.width, 0.5)];
    lineView.backgroundColor = [UIColor colorWithRed:COLORNUM(196.0) green:COLORNUM(196.0) blue:COLORNUM(196.0) alpha:1.f];
    [self.view addSubview:lineView];
    
    [self requestData];
}
//请求绑定银行卡
- (void)requestData {
    
    __weak typeof(self)mySelf = self;
    NSDictionary *dic = [NSDictionary dictionaryWithObjectsAndKeys:@"000071",@"method_code", nil];
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"正在加载"];
    __weak typeof(progressHUD) hud = progressHUD;
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        hud.hidden = YES;
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;
            
            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                mySelf.dataDic = dataDic;
                
                if (![mySelf dataIsNull:[dataDic objectForKey:@"bankName"]]) {
                    mySelf.bankName = [dataDic objectForKey:@"bankName"];
                }
                if (![mySelf dataIsNull:[dataDic objectForKey:@"bankUserName"]]) {
                    mySelf.peopleName = [dataDic objectForKey:@"bankUserName"];
                }
                if (![mySelf dataIsNull:[dataDic objectForKey:@"credNo"]]) {
                    NSString *creStr = [dataDic objectForKey:@"credNo"];
                    mySelf.bankCardNumStr = [dataDic objectForKey:@"credNo"];
                    if (creStr.length > 4) {
                        mySelf.cardNumAll = [dataDic objectForKey:@"credNo"];
                        creStr = [creStr substringFromIndex:creStr.length-4];
                        mySelf.bankCard = [NSString stringWithFormat:@"(%@)",creStr];
                    }
                }
                
                [mySelf requestData2];
                
            } else {
                
                [mySelf.view makeToast:[dataDic objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
            }
            
        } else {
            
            [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);
        hud.hidden = YES;
        if (!mySelf.reachability) {
            mySelf.reachability = [Reachability reachabilityWithHostName:@"www.baidu.com"];
        }
        if ([mySelf.reachability currentReachabilityStatus] == NotReachable) {
            //没有网络
            
            [mySelf.view makeToast:@"无网络，请检查您的网络连接" duration:2 position:CSToastPositionCenter];
            
        } else {
            if ([error.localizedDescription isEqualToString:@"The request timed out."]) {
                //请求超时
                
                [mySelf.view makeToast:@"请求超时，请稍后重试" duration:2 position:CSToastPositionCenter];
                
            } else {
                //请求失败
                [mySelf.view makeToast:@"加载失败" duration:2 position:CSToastPositionCenter];
            }
        }
        
    }];
}
//请求可提现金额
- (void)requestData2 {
    
    __weak typeof(self)mySelf = self;
    NSDictionary *dic = [NSDictionary dictionaryWithObjectsAndKeys:@"000069",@"method_code", nil];

    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;
            
            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                
                if (![mySelf dataIsNull:[dataDic objectForKey:@"canCashPrice"]]) {
                    mySelf.moneyNum = [dataDic objectForKey:@"canCashPrice"];
                }
                
                [mySelf.tableView reloadData];
                
            } else {
                
                [mySelf.view makeToast:[dataDic objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
            }
            
        } else {
            
            [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
        }

    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);

        if (!mySelf.reachability) {
            mySelf.reachability = [Reachability reachabilityWithHostName:@"www.baidu.com"];
        }
        if ([mySelf.reachability currentReachabilityStatus] == NotReachable) {
            //没有网络
            
            [mySelf.view makeToast:@"无网络，请检查您的网络连接" duration:2 position:CSToastPositionCenter];
            
        } else {
            if ([error.localizedDescription isEqualToString:@"The request timed out."]) {
                //请求超时
                
                [mySelf.view makeToast:@"请求超时，请稍后重试" duration:2 position:CSToastPositionCenter];
                
            } else {
                //请求失败
                [mySelf.view makeToast:@"加载失败" duration:2 position:CSToastPositionCenter];
            }
        }
        
    }];
}
//弹出框
- (void)createAlert:(NSString *)title {
    
    BRAlertView *alertView = [[BRAlertView alloc] init];
    
    __weak typeof(alertView) aView = alertView;
    [alertView showAlertView:title completion:^{
        [aView close];
    }];
}

#pragma mark - 提现规则
- (void)guizeClick {
    BRGuiZeViewController *guize = [[BRGuiZeViewController alloc] init];
    [self.navigationController pushViewController:guize animated:YES];
}
//提现
- (void)tixianClick2 {
    
    // 收起键盘
    [self.view endEditing:YES];
    
    if ([self.bankName isEqualToString:@"请绑定银行卡"]) {
        
        [self.view makeToast:@"请绑定银行卡" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    if (!self.textField.text.length) {
        
        [self.view makeToast:@"请输入提现金额" duration:2 position:CSToastPositionCenter];
        return;
    }
    
    if ([self.textField.text isEqualToString:@"0"]) {
        
        [self.view makeToast:@"提现金额不能为0元" duration:2 position:CSToastPositionCenter];
        return;
    }
    if ([self.textField.text floatValue] > [self.moneyNum floatValue]) {
        [self.view makeToast:[NSString stringWithFormat:@"您今日可提现的金额不得大于%@元",self.moneyNum] duration:2 position:CSToastPositionCenter];
        return;
    }
    
    [self showVerificationTypeActionSheet];
}

#pragma mark - 显示验证方式选择ActionSheet
- (void)showVerificationTypeActionSheet {
    NSArray *buttons = @[@"密码验证【推荐】", @"短信验证"];
    
    BRActionSheetView *actionSheet = [[BRActionSheetView alloc] init];
    actionSheet.title = @"选择验证方式";
    [actionSheet setButtons:buttons];
    [actionSheet show];
    
    __weak typeof(self) mySelf = self;
    __weak BRActionSheetView *weakActionSheet = actionSheet;
    actionSheet.clickActionButtonCallBack = ^(NSInteger index) {
        [weakActionSheet close];
        
        [mySelf.view endEditing:YES];
        
        if (index == 0) {
            // 密码验证
            mySelf.verificationType = 1;
            [mySelf handlePasswordVerification];
        } else if (index == 1) {
            // 短信验证码验证
            mySelf.verificationType = 0;
            [mySelf handleSMSVerification];
        }
    };
}

#pragma mark - 处理密码验证
- (void)handlePasswordVerification {
    [self checkWithdrawPasswordStatus];
}

#pragma mark - 检查是否设置了提现密码
- (void)checkWithdrawPasswordStatus {
    __weak typeof(self)mySelf = self;
    NSDictionary *dic = [NSDictionary dictionaryWithObjectsAndKeys:@"000454",@"method_code", nil];
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"正在检查"];
    __weak typeof(progressHUD) hud = progressHUD;
    
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        hud.hidden = YES;
        
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;
            
            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                // 检查是否设置了密码 - 使用正确的字段名 hasCashPwd
                BOOL hasPassword = [[dataDic objectForKey:@"hasCashPwd"] boolValue];
                if (hasPassword) {
                    // 已设置密码，显示密码验证弹窗
                    [mySelf showWithdrawPasswordVerificationDialog];
                } else {
                    // 未设置密码，跳转到设置提现密码页面
                    [mySelf navigateToSetWithdrawPassword];
                }
            } else {
                [mySelf.view makeToast:[dataDic objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
            }
        } else {
            [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);
        hud.hidden = YES;
        
        if (!mySelf.reachability) {
            mySelf.reachability = [Reachability reachabilityWithHostName:@"www.baidu.com"];
        }
        if ([mySelf.reachability currentReachabilityStatus] == NotReachable) {
            [mySelf.view makeToast:@"无网络，请检查您的网络连接" duration:2 position:CSToastPositionCenter];
        } else {
            if ([error.localizedDescription isEqualToString:@"The request timed out."]) {
                [mySelf.view makeToast:@"请求超时，请稍后重试" duration:2 position:CSToastPositionCenter];
            } else {
                [mySelf.view makeToast:@"检查失败" duration:2 position:CSToastPositionCenter];
            }
        }
    }];
}

#pragma mark - 显示提现密码验证弹窗
- (void)showWithdrawPasswordVerificationDialog {
    __weak typeof(self)mySelf = self;
    
    self.withdrawPassword = @"";
    
    self.passwordView = [[BRWithdrawPasswordView alloc] initWithPasswordBlock:^(NSString *password) {
        
        mySelf.withdrawPassword = [password copy];
        [mySelf.passwordView removeView];
        [mySelf passwordVerification];
        
    } cancelBlock:^{
        
        // 取消按钮点击
        
    }];
}

#pragma mark - 密码验证
- (void)passwordVerification {
    // 密码验证成功，直接进入提现流程
    [self tixianClick];
}

#pragma mark - 处理短信验证
- (void)handleSMSVerification {
    __weak typeof(self)mySelf = self;
    
    self.smsCode = @"";
    
    self.smsCodeView = [[ShowMyPurseSMSCode alloc] initWithBlock:^(NSString *smsCode) {
        
        mySelf.smsCode = [smsCode copy];
        if (self.smsCodeView.selectViewList.smsBtn.userInteractionEnabled) {
            
            [mySelf timeOver];
            
        } else {
            
            self.smsCodeView.selectViewList.smsBtn.userInteractionEnabled = NO;
            
        }
        [mySelf smsYanzheng];
        
    } :^{
        
        [mySelf setSmsCode];
        
    }];
    [self.smsCodeView.selectViewList.yes setTitle:@"确认" forState:UIControlStateNormal];
    self.smsCodeView.selectViewList.titleLabel.text = @"短信验证";
}

#pragma mark - 验证码倒计时
- (void)refreshSmsCodeBtn {
    
    self.timeNum --;
    if (self.timeNum >= 0) {
        self.smsCodeView.selectViewList.smsBtn.titleLabel.text = [NSString stringWithFormat:@"重新发送(%ld)s",self.timeNum];
        [self.smsCodeView.selectViewList.smsBtn setTitle:[NSString stringWithFormat:@"重新发送(%ld)s",self.timeNum] forState:UIControlStateNormal];
        [self.smsCodeView.selectViewList.smsBtn setTitleColor:[UIColor br_textLightGrayColor] forState:UIControlStateNormal];
        [self.smsCodeView.selectViewList.smsBtn setBackgroundImage:[UIImage imageWithColor:[UIColor whiteColor]] forState:UIControlStateNormal];
    } else {
        
        [self timeOver];
    }
}

- (void)timeOver {
    self.timeNum = 60;
    self.smsCodeView.selectViewList.smsBtn.userInteractionEnabled = YES;
    [self.smsCodeView.selectViewList.smsBtn setTitle:@"获取验证码" forState:UIControlStateNormal];
    [self.smsCodeView.selectViewList.smsBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.smsCodeView.selectViewList.smsBtn setBackgroundImage:[UIImage imageWithColor:[UIColor br_mainBlueColor]] forState:UIControlStateNormal];
    [self.timer invalidate];
    self.timer = nil;
}

#pragma mark - 获取短信验证码
- (void)setSmsCode {
    
    self.smsCodeView.selectViewList.smsBtn.userInteractionEnabled = NO;
    self.timer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(refreshSmsCodeBtn) userInfo:nil repeats:YES];
    self.timeNum = 60;
    
    UIWindow *window = [UIApplication sharedApplication].keyWindow;
    
    __weak typeof(self)mySelf = self;
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithObjectsAndKeys:@"000031",@"method_code", nil];
    [dic setObject:@"1" forKey:@"type"];
    [dic setObject:@"1" forKey:@"optType"];
    
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {
        
    } success:^(NSURLSessionDataTask *task, id responseObject) {
        
        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;
            
            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                
                [window makeToast:@"验证码已发送" duration:2 position:CSToastPositionCenter];
                
            } else {
                
                [window makeToast:[dataDic objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
                [mySelf timeOver];
            }
            
        } else {
            [window makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
            [mySelf timeOver];
        }
        
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);
        
        [mySelf timeOver];
        
        if (!mySelf.reachability) {
            mySelf.reachability = [Reachability reachabilityWithHostName:@"www.baidu.com"];
        }
        if ([mySelf.reachability currentReachabilityStatus] == NotReachable) {
            //没有网络
            
            [window makeToast:@"无网络，请检查您的网络连接" duration:2 position:CSToastPositionCenter];
            
        } else {
            if ([error.localizedDescription isEqualToString:@"The request timed out."]) {
                //请求超时
                
                [window makeToast:@"请求超时，请稍后重试" duration:2 position:CSToastPositionCenter];
                
            } else {
                //请求失败
                
                [window makeToast:@"加载失败" duration:2 position:CSToastPositionCenter];
            }
        }
        
    }];
}

#pragma mark - 验证短信验证码
- (void)smsYanzheng {
    
    UIWindow *window = [UIApplication sharedApplication].keyWindow;
    if (!self.smsCode.length) {
        
        [window makeToast:@"请输入短信验证码" duration:2 position:CSToastPositionCenter];
        
        return;
    }
    
    // 去掉000086验证步骤，直接进入提现流程
    [self.smsCodeView removeView];
    [self tixianClick];
}

#pragma mark - 提现
- (void)tixianClick {
    
    __weak typeof(self)mySelf = self;
    NSMutableDictionary *dic = [NSMutableDictionary dictionaryWithObjectsAndKeys:@"000241",@"method_code", nil];
    [dic setObject:[[UserManager shareInstance] getUserId] forKey:@"userId"];
    [dic setObject:self.cardNumAll forKey:@"payAccount"];
    [dic setObject:self.peopleName forKey:@"payName"];
    [dic setObject:self.textField.text forKey:@"amount"];
    [dic setObject:self.bankName forKey:@"bankName"];
    [dic setObject:@"2" forKey:@"apiVer"];
    
    // 根据验证方式传递对应参数
    if (self.verificationType == 1) {
        // 密码验证 - 传递MD5大写的密码
        NSString *md5Password = [[self.withdrawPassword md5String] uppercaseString];
        [dic setObject:md5Password forKey:@"password"];
        [dic setObject:@"" forKey:@"smsCode"];  // 密码验证时smsCode传空
    } else {
        // 短信验证 - 传递验证码
        [dic setObject:@"" forKey:@"password"];  // 短信验证时password传空
        [dic setObject:self.smsCode forKey:@"smsCode"];
    }
    
    MBProgressHUD *progressHUD = [Utils createLoadingHUDWithTitle:@"正在加载"];
    __weak typeof(progressHUD) hud = progressHUD;
    [HTTPRequest POST:kServerDomain parameters:dic progress:^(NSProgress *progress) {

    } success:^(NSURLSessionDataTask *task, id responseObject) {

        if ([responseObject isKindOfClass:[NSDictionary class]]) {
            NSDictionary *dataDic = (NSDictionary *)responseObject;

            if ([[dataDic objectForKey:@"code"] isEqualToString:@"0000"]) {
                
                mySelf.tixianBtn.userInteractionEnabled = NO;
                [mySelf.view makeToast:@"提交成功,等待审核" duration:2 position:CSToastPositionCenter];
                mySelf.timer = [NSTimer scheduledTimerWithTimeInterval:2.0 target:self selector:@selector(tixianSuccess) userInfo:nil repeats:NO];


            } else {

                [mySelf.view makeToast:[dataDic objectForKey:@"errorMsg"] duration:2 position:CSToastPositionCenter];
            }

        } else {

            [mySelf.view makeToast:@"数据出错" duration:2 position:CSToastPositionCenter];
        }
        hud.hidden = YES;
    } failure:^(NSURLSessionDataTask *task, NSError *error) {
        NSLog(@"%@",error);
        hud.hidden = YES;
        if (!mySelf.reachability) {
            mySelf.reachability = [Reachability reachabilityWithHostName:@"www.baidu.com"];
        }
        if ([mySelf.reachability currentReachabilityStatus] == NotReachable) {
            //没有网络

            [mySelf.view makeToast:@"无网络，请检查您的网络连接" duration:2 position:CSToastPositionCenter];

        } else {
            if ([error.localizedDescription isEqualToString:@"The request timed out."]) {
                //请求超时

                [mySelf.view makeToast:@"请求超时，请稍后重试" duration:2 position:CSToastPositionCenter];

            } else {
                //请求失败
                [mySelf.view makeToast:@"加载失败" duration:2 position:CSToastPositionCenter];
            }
        }

    }];
}

- (void)tixianSuccess {
    [self.timer invalidate];
    self.timer = nil;
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark -UITableView 代理
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 2;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return FONTSIZE(45);
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *cellID = @"cellId";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellID];
    
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellID];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
        cell.clipsToBounds = YES;
        cell.backgroundColor = [UIColor whiteColor];
    }
    
    for (UILabel *label in cell.subviews) {
        if (label.tag == 50 || label.tag == 51) {
            [label removeFromSuperview];
        }
    }
    
    for (UITextField *textField in cell.subviews) {
        if (textField.tag == 60) {
            [textField removeFromSuperview];
        }
    }
    
    if (indexPath.row) {
        cell.accessoryType = UITableViewCellAccessoryNone;
        CGSize size = [@"提现金额" boundingRectWithSize:CGSizeMake(1000, 35) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
        UILabel *nameLabel = [[UILabel alloc] initWithFrame:CGRectMake(FONTSIZE(20), 0, size.width, FONTSIZE(45))];
        nameLabel.text = @"提现金额";
        nameLabel.font = FONT_Light(16);
        nameLabel.textColor = [UIColor colorWithRed:COLORNUM(29.0) green:COLORNUM(32.0) blue:COLORNUM(36.0) alpha:1.f];
        nameLabel.tag = 50;
        [cell addSubview:nameLabel];
        
        self.textField = [[UITextField alloc] initWithFrame:CGRectMake(nameLabel.frame.origin.x+nameLabel.frame.size.width+FONTSIZE(8), 0, self.tableView.frame.size.width-nameLabel.frame.origin.x-nameLabel.frame.size.width-FONTSIZE(8)-FONTSIZE(30), FONTSIZE(45))];

        self.textField.placeholder = [NSString stringWithFormat:@"本次可提现%@元",self.moneyNum];
        self.textField.tag = 60;
        self.textField.delegate = self;
        self.textField.font = FONT_Light(16);
        self.textField.keyboardType = UIKeyboardTypeDecimalPad;
        self.textField.textColor = [UIColor colorWithRed:COLORNUM(29.0) green:COLORNUM(32.0) blue:COLORNUM(36.0) alpha:1.f];
        [cell addSubview:self.textField];
        
        cell.separatorInset = UIEdgeInsetsMake(0, kScreenWidth, 0, 0);
        
    } else {
        
        if (!self.bankName.length) {
            self.bankName = @"请绑定银行卡";
            self.bankCard = @"";
        } 
        
        cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
        CGSize size = [self.bankName boundingRectWithSize:CGSizeMake(1000, 35) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName:FONT_Light(16)} context:nil].size;
        UILabel *nameLabel = [[UILabel alloc] initWithFrame:CGRectMake(FONTSIZE(20), 0, size.width, FONTSIZE(45))];
        nameLabel.text = self.bankName;
        nameLabel.font = FONT_Light(16);
        nameLabel.textColor = [UIColor colorWithRed:COLORNUM(29.0) green:COLORNUM(32.0) blue:COLORNUM(36.0) alpha:1.f];
        nameLabel.tag = 50;
        [cell addSubview:nameLabel];
        
        UILabel *cardLabel = [[UILabel alloc] initWithFrame:CGRectMake(nameLabel.frame.origin.x+nameLabel.frame.size.width+FONTSIZE(8), 0, self.tableView.frame.size.width-nameLabel.frame.origin.x-nameLabel.frame.size.width-FONTSIZE(8)-FONTSIZE(30), FONTSIZE(45))];
        cardLabel.text = self.bankCard;
        cardLabel.font = FONT_Light(16);
        cardLabel.tag = 51;
        cardLabel.textColor = [UIColor colorWithRed:COLORNUM(177.0) green:COLORNUM(177.0) blue:COLORNUM(177.0) alpha:1.f];
        [cell addSubview:cardLabel];
    }
    
    
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    if (!indexPath.row) {
        BRBinDingViewController *binding = [[BRBinDingViewController alloc] init];
        binding.peopleName = self.peopleName;
        binding.bankName = self.bankName;
        binding.bankCard = self.bankCardNumStr;
        __weak typeof(self) mySelf = self;
        binding.ncBlock = ^(NSString *name, NSString *cardNum, NSString *bankName) {
            mySelf.bankName = bankName;
            mySelf.peopleName = name;
            if (cardNum.length > 4) {
                mySelf.cardNumAll = [cardNum copy];
                mySelf.bankCard = [NSString stringWithFormat:@"(%@)",[cardNum substringFromIndex:cardNum.length-4]];
                
            }
            
            mySelf.bankCardNumStr = cardNum;
            
            [mySelf.tableView reloadData];
        };
        [self.navigationController pushViewController:binding animated:YES];
    }
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string
{
    // 如果是删除键，允许删除
    if (string.length == 0) {
        return YES;
    }
    
    // 获取当前输入的字符串
    NSString *inputString = string;
    
    // 替换中文句号为英文小数点
    if ([inputString isEqualToString:@"。"]) {
        inputString = @".";
    }
    
    // 获取当前输入的字符
    unichar single = [inputString characterAtIndex:0];
    
    // 只允许输入数字和小数点
    if (!((single >= '0' && single <= '9') || single == '.')) {
        return NO;
    }
    
    // 拼接输入后的文本
    NSString *futureString = [textField.text stringByReplacingCharactersInRange:range withString:inputString];
    
    // 不允许空字符串
    if (futureString.length == 0) {
        return YES;
    }
    
    // 判断是否有小数点
    BOOL hasDecimalPoint = [textField.text containsString:@"."];
    
    // 处理首位输入
    if (futureString.length == 1) {
        if ([inputString isEqualToString:@"."]) {
            textField.text = @"0";
            if ([string isEqualToString:@"。"]) {
                textField.text = @"0.";
                return NO;
            }
            return YES;
        }
        return YES;
    }
    
    // 处理以0开头的情况
    if (futureString.length > 1 && [futureString hasPrefix:@"0"]) {
        if (![futureString hasPrefix:@"0."]) {
            return NO;
        }
    }
    
    // 限制只能有一个小数点
    if (hasDecimalPoint && (single == '.' || [inputString isEqualToString:@"。"])) {
        return NO;
    }
    
    // 限制小数点后最多两位
    if ([futureString containsString:@"."]) {
        NSArray *components = [futureString componentsSeparatedByString:@"."];
        if (components.count > 1 && [[components lastObject] length] > 2) {
            return NO;
        }
    }
    
    // 验证金额范围，使用 NSDecimalNumber 处理
    NSDecimalNumber *inputAmount = [NSDecimalNumber decimalNumberWithString:futureString];
    NSDecimalNumber *maximumAmount = [NSDecimalNumber decimalNumberWithString:self.moneyNum];
    NSDecimalNumber *maxLimit = [NSDecimalNumber decimalNumberWithString:@"9999"];
    
    // 设置最大值
    if ([maximumAmount compare:maxLimit] == NSOrderedDescending) {
        maximumAmount = maxLimit;
    }
    
    // 比较金额
    if ([inputAmount compare:maximumAmount] == NSOrderedDescending) {
        [self.view makeToast:[NSString stringWithFormat:@"本次最大提现金额为%@",maximumAmount] duration:2 position:CSToastPositionTop];
        return NO;
    }
    
    // 限制整数部分长度
    NSString *integerPart = [futureString containsString:@"."] ?
        [futureString componentsSeparatedByString:@"."].firstObject :
        futureString;
    if (integerPart.length > 4) {
        return NO;
    }
    
    // 如果是输入中文句号，需要手动设置文本并阻止默认输入
    if ([string isEqualToString:@"。"]) {
        textField.text = futureString;
        return NO;
    }
    
    return YES;
}

#pragma mark - 跳转到设置提现密码页面
- (void)navigateToSetWithdrawPassword {
    WithdrawPasswordViewController *withdrawPasswordVC = [[WithdrawPasswordViewController alloc] init];
    [self.navigationController pushViewController:withdrawPasswordVC animated:YES];
}

@end
